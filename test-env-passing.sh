#!/bin/bash

# 测试环境变量传递
echo "=== 测试环境变量传递 ==="

# 定义颜色
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 生成测试版本号
TEST_VERSION="1.0.$(date +%Y%m%d%H%M%S)"
echo -e "${YELLOW}测试版本号: ${TEST_VERSION}${NC}"

echo ""
echo -e "${YELLOW}=== 测试1: 直接设置环境变量 ===${NC}"
export npm_package_version="${TEST_VERSION}"
export APP_VERSION="${TEST_VERSION}"
export VITE_APP_VERSION="${TEST_VERSION}"
export NODE_ENV="test"

echo "设置的环境变量:"
echo "  npm_package_version: ${npm_package_version}"
echo "  APP_VERSION: ${APP_VERSION}"
echo "  VITE_APP_VERSION: ${VITE_APP_VERSION}"
echo "  NODE_ENV: ${NODE_ENV}"

echo ""
echo "运行调试脚本:"
node debug-env.js

echo ""
echo -e "${YELLOW}=== 测试2: 使用env命令传递 ===${NC}"
echo "运行: env npm_package_version=\"${TEST_VERSION}\" node debug-env.js"
env npm_package_version="${TEST_VERSION}" APP_VERSION="${TEST_VERSION}" VITE_APP_VERSION="${TEST_VERSION}" NODE_ENV="test" node debug-env.js

echo ""
echo -e "${YELLOW}=== 测试3: 检查npm run命令环境变量传递 ===${NC}"

# 创建临时的package.json脚本来测试
echo "创建临时测试脚本..."
npm pkg set scripts.test-env="node debug-env.js"

echo "运行: npm run test-env"
npm_package_version="${TEST_VERSION}" APP_VERSION="${TEST_VERSION}" VITE_APP_VERSION="${TEST_VERSION}" NODE_ENV="test" npm run test-env

# 清理
npm pkg delete scripts.test-env

echo ""
echo -e "${GREEN}测试完成！${NC}"
echo ""
echo -e "${YELLOW}如果上面的测试显示版本号正确，那么问题可能在于:${NC}"
echo "1. Vite构建过程中环境变量丢失"
echo "2. 插件执行时机问题"
echo "3. npm run build命令的环境变量传递问题"
