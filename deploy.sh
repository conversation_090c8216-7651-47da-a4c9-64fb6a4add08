#!/bin/bash

# 显示执行的命令
set -x

# 定义颜色
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 服务器信息
SERVER="101.132.25.134"
USER="root"
PASSWORD="Zlxj@123456"
REMOTE_DIR="/var/www/smart/"

# 生成版本号（基于时间戳）
VERSION="1.0.$(date +%Y%m%d%H%M%S)"
echo -e "${YELLOW}当前部署版本: ${VERSION}${NC}"

# 设置多种环境变量，确保构建时能正确获取
export npm_package_version="${VERSION}"
export APP_VERSION="${VERSION}"
export VITE_APP_VERSION="${VERSION}"
export NODE_ENV="development"

# 显示设置的环境变量
echo -e "${YELLOW}环境变量设置:${NC}"
echo "  npm_package_version: ${npm_package_version}"
echo "  APP_VERSION: ${APP_VERSION}"
echo "  VITE_APP_VERSION: ${VITE_APP_VERSION}"
echo "  NODE_ENV: ${NODE_ENV}"

echo -e "${GREEN}开始构建项目...${NC}"

# 创建临时的.env文件来确保环境变量传递
cat > .env.build << EOF
npm_package_version=${VERSION}
APP_VERSION=${VERSION}
VITE_APP_VERSION=${VERSION}
NODE_ENV=development
EOF

echo -e "${YELLOW}创建临时环境文件 .env.build:${NC}"
cat .env.build

# 构建项目，使用多种方式确保环境变量传递
echo -e "${YELLOW}执行构建命令...${NC}"
env npm_package_version="${VERSION}" APP_VERSION="${VERSION}" VITE_APP_VERSION="${VERSION}" NODE_ENV="development" npm run build

# 清理临时文件
rm -f .env.build

# 检查构建是否成功
if [ $? -ne 0 ]; then
    echo -e "\033[0;31m构建失败，退出部署脚本${NC}"
    exit 1
fi

echo -e "${GREEN}构建完成，开始上传文件到服务器...${NC}"

# 使用 rsync 上传文件
# -a: 归档模式，保留所有文件属性
# -v: 显示详细信息
# -z: 压缩传输
# --delete: 删除目标目录中有而源目录中没有的文件
# --progress: 显示传输进度
sshpass -p "${PASSWORD}" rsync -avz --delete --progress ./dist/ ${USER}@${SERVER}:${REMOTE_DIR}

# 检查上传是否成功
if [ $? -ne 0 ]; then
    echo -e "\033[0;31m文件上传失败${NC}"
    exit 1
fi

echo -e "${GREEN}部署完成！${NC}" 