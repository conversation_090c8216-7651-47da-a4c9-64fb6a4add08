# 全局右键菜单组件使用指南

## 概述

这是一个功能完整的全局右键菜单组件，支持在任意视图中右键弹出自定义菜单。组件具有以下特性：

- ✅ 支持自定义菜单项（图标、快捷键、禁用状态等）
- ✅ 支持响应菜单项点击事件
- ✅ 全局管理，确保同时只有一个菜单显示
- ✅ 自动位置调整，防止菜单超出视窗
- ✅ 支持键盘操作（ESC关闭）
- ✅ 支持暗色主题
- ✅ 提供Vue指令和Composable两种使用方式

## 快速开始

### 方式一：使用Vue指令（推荐）

```vue
<template>
  <div v-context-menu="menuItems" class="my-element">
    右键点击我
  </div>
</template>

<script setup>
import { useContextMenu } from '@/composables/useContextMenu.js'

const { createMenuItem, createDivider } = useContextMenu()

const menuItems = [
  createMenuItem('复制', { 
    icon: 'el-icon-copy-document', 
    shortcut: 'Ctrl+C', 
    action: 'copy' 
  }),
  createMenuItem('粘贴', { 
    icon: 'el-icon-document', 
    shortcut: 'Ctrl+V', 
    action: 'paste' 
  }),
  createDivider(),
  createMenuItem('删除', { 
    icon: 'el-icon-delete', 
    action: 'delete' 
  })
]
</script>
```

### 方式二：使用Composable

```vue
<template>
  <div @contextmenu="showMenu" class="my-element">
    右键点击我
  </div>
</template>

<script setup>
import { useContextMenu } from '@/composables/useContextMenu.js'

const { showContextMenu, createMenuItem, handleMenuItemClick } = useContextMenu()

const menuItems = [
  createMenuItem('操作1', { action: 'action1' }),
  createMenuItem('操作2', { action: 'action2' })
]

const showMenu = (event) => {
  showContextMenu(event, menuItems)
}

// 处理菜单项点击
const menuHandler = handleMenuItemClick((item, index, target) => {
  console.log('点击了:', item.label)
  // 执行相应操作
})
</script>
```

## 高级用法

### 动态菜单项

```vue
<script setup>
const dynamicMenuItems = computed(() => {
  const items = [
    createMenuItem('刷新', { action: 'refresh' })
  ]
  
  if (isEditing.value) {
    items.push(createMenuItem('保存', { action: 'save' }))
    items.push(createMenuItem('取消', { action: 'cancel' }))
  } else {
    items.push(createMenuItem('编辑', { action: 'edit' }))
  }
  
  return items
})
</script>

<template>
  <div v-context-menu="{ items: dynamicMenuItems, callback: handleMenuClick }">
    动态菜单示例
  </div>
</template>
```

### 带回调的指令用法

```vue
<template>
  <div v-context-menu="{ items: menuItems, callback: handleMenuClick }">
    带回调的菜单
  </div>
</template>

<script setup>
const handleMenuClick = (item, index, element) => {
  console.log('菜单项:', item)
  console.log('索引:', index)
  console.log('触发元素:', element)
  
  switch (item.action) {
    case 'copy':
      // 执行复制
      break
    case 'delete':
      // 执行删除
      break
  }
}
</script>
```

## API 参考

### createMenuItem(label, options)

创建菜单项的辅助函数。

**参数：**
- `label` (string): 菜单项文本
- `options` (object): 配置选项
  - `icon` (string): 图标类名
  - `shortcut` (string): 快捷键显示文本
  - `disabled` (boolean): 是否禁用
  - `hidden` (boolean): 是否隐藏
  - `action` (string): 操作标识符

**示例：**
```js
createMenuItem('复制', {
  icon: 'el-icon-copy-document',
  shortcut: 'Ctrl+C',
  action: 'copy',
  disabled: false
})
```

### createDivider()

创建分割线。

**示例：**
```js
const menuItems = [
  createMenuItem('操作1', { action: 'action1' }),
  createDivider(),
  createMenuItem('操作2', { action: 'action2' })
]
```

### showContextMenu(event, items)

显示右键菜单。

**参数：**
- `event` (Event): 右键点击事件对象
- `items` (Array): 菜单项数组

### handleMenuItemClick(callback)

创建菜单项点击处理器。

**参数：**
- `callback` (Function): 点击回调函数 `(item, index, target) => {}`

## 指令修饰符

### .cursor

添加鼠标样式提示：

```vue
<div v-context-menu.cursor="menuItems">
  右键点击我（带鼠标样式）
</div>
```

## 样式自定义

组件支持暗色主题，会自动根据系统主题或手动设置的主题进行切换。

如需自定义样式，可以覆盖以下CSS类：

```css
.context-menu {
  /* 菜单容器样式 */
}

.context-menu-item {
  /* 菜单项样式 */
}

.context-menu-item:hover {
  /* 菜单项悬停样式 */
}

.context-menu-item--disabled {
  /* 禁用菜单项样式 */
}

.context-menu-item--divider {
  /* 分割线样式 */
}
```

## 演示页面

访问 `/demo/context-menu` 查看完整的使用示例和演示。

## 注意事项

1. 组件已全局注册，无需手动导入
2. 同时只能显示一个右键菜单
3. 菜单会自动调整位置避免超出视窗
4. 点击菜单外部或按ESC键会自动关闭菜单
5. 滚动页面时菜单会自动关闭
