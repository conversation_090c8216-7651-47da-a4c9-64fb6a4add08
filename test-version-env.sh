#!/bin/bash

# 测试版本环境变量设置
echo "=== 测试版本环境变量设置 ==="

# 定义颜色
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 生成测试版本号
TEST_VERSION="1.0.$(date +%Y%m%d%H%M%S)"
echo -e "${YELLOW}测试版本号: ${TEST_VERSION}${NC}"

# 设置环境变量
export npm_package_version="${TEST_VERSION}"
export APP_VERSION="${TEST_VERSION}"
export VITE_APP_VERSION="${TEST_VERSION}"
export NODE_ENV="test"

echo ""
echo -e "${YELLOW}设置的环境变量:${NC}"
echo "  npm_package_version: ${npm_package_version}"
echo "  APP_VERSION: ${APP_VERSION}"
echo "  VITE_APP_VERSION: ${VITE_APP_VERSION}"
echo "  NODE_ENV: ${NODE_ENV}"

echo ""
echo -e "${YELLOW}验证环境变量是否正确传递到Node.js进程:${NC}"

# 创建临时测试脚本
cat > temp_test_env.js << 'EOF'
console.log('=== Node.js 进程中的环境变量 ===');
console.log('npm_package_version:', process.env.npm_package_version);
console.log('APP_VERSION:', process.env.APP_VERSION);
console.log('VITE_APP_VERSION:', process.env.VITE_APP_VERSION);
console.log('NODE_ENV:', process.env.NODE_ENV);

// 模拟版本插件的逻辑
const getVersion = () => {
  if (process.env.npm_package_version && process.env.npm_package_version !== '0.0.0') {
    return process.env.npm_package_version;
  }
  
  if (process.env.APP_VERSION) {
    return process.env.APP_VERSION;
  }
  
  if (process.env.VITE_APP_VERSION) {
    return process.env.VITE_APP_VERSION;
  }
  
  const timestamp = new Date().toISOString().replace(/[-:T]/g, '').slice(0, 14);
  return `1.0.${timestamp}`;
};

const version = getVersion();
console.log('');
console.log('=== 版本插件会使用的版本号 ===');
console.log('最终版本号:', version);

const versionInfo = {
  version: version,
  buildTime: Date.now(),
  hash: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
  gitCommit: 'test-commit',
  buildEnv: process.env.NODE_ENV || 'development',
  deployTime: new Date().toISOString()
};

console.log('');
console.log('=== 完整版本信息 ===');
console.log(JSON.stringify(versionInfo, null, 2));
EOF

# 运行测试脚本
node temp_test_env.js

# 清理临时文件
rm temp_test_env.js

echo ""
echo -e "${GREEN}测试完成！${NC}"
echo ""
echo -e "${YELLOW}如果版本号显示正确，可以运行以下命令进行实际构建:${NC}"
echo "npm_package_version=\"${TEST_VERSION}\" APP_VERSION=\"${TEST_VERSION}\" VITE_APP_VERSION=\"${TEST_VERSION}\" npm run build"
