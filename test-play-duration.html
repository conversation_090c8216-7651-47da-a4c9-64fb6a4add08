<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PlayDuration 功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.completed {
            background-color: #e8f5e8;
            color: #2e7d32;
        }
        .status.in-progress {
            background-color: #fff3e0;
            color: #f57c00;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PlayDuration 功能实现报告</h1>
        
        <div class="test-section">
            <div class="test-title">功能概述</div>
            <div class="test-description">
                在时间轴视图中为分镜添加了 playDuration 设置功能，采用类似 BgAudioClip.vue 中 clip-handle right 的拖拽交互方式。
                用户可以通过拖拽右侧手柄来调整分镜的播放时长，并确保设置的时长不小于分镜的最小时长。
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">已实现功能</div>
            <ul class="feature-list">
                <li><span class="status completed">✓ 完成</span> 修复 getShotDuration 方法支持 playDuration 字段</li>
                <li><span class="status completed">✓ 完成</span> 在时间轴视图中添加拖拽手柄界面</li>
                <li><span class="status completed">✓ 完成</span> 实现拖拽调整 playDuration 的交互逻辑</li>
                <li><span class="status completed">✓ 完成</span> 添加最小时长验证机制</li>
                <li><span class="status completed">✓ 完成</span> 集成 saveShot 方法调用</li>
                <li><span class="status completed">✓ 完成</span> 更新 VideoEditor.vue 数据结构</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">核心实现细节</div>
            <div class="test-description">
                <strong>1. 拖拽手柄设计：</strong>
                <div class="code-block">
&lt;div class="play-duration-handle" 
     v-if="!isPlaying" 
     @mousedown.stop="startPlayDurationResize(element, index, $event)"
     :title="`拖拽调整播放时长: ${formatDuration(getShotDuration(element))}`"&gt;
  &lt;div class="handle-line"&gt;&lt;/div&gt;
  &lt;div class="handle-grip"&gt;&lt;/div&gt;
&lt;/div&gt;
                </div>
                
                <strong>2. 时长计算逻辑：</strong>
                <div class="code-block">
// 获取分镜的最小时长（不考虑 playDuration）
const getMinShotDuration = (shot) => {
  // 语音时长 > 视频时长 > 默认5秒
}

// 获取分镜时长（优先使用 playDuration）
const getShotDuration = (shot) => {
  if (shot.playDuration && shot.playDuration > 0) {
    return shot.playDuration;
  }
  return getMinShotDuration(shot);
}
                </div>

                <strong>3. 拖拽交互：</strong>
                <div class="code-block">
const handlePlayDurationResizeMove = (event) => {
  const deltaX = event.clientX - playDurationResizing.value.startX;
  const deltaTime = (deltaX / props.timelineScale) * 1000;
  
  let newDuration = Math.max(
    playDurationResizing.value.minDuration,
    playDurationResizing.value.originalDuration + deltaTime
  );
  
  // 限制最大时长为60秒
  newDuration = Math.min(newDuration, 60000);
  
  shot.playDuration = newDuration;
}
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">交互体验特点</div>
            <ul class="feature-list">
                <li><strong>视觉反馈：</strong> 手柄仅在悬停时显示，避免界面混乱</li>
                <li><strong>拖拽精度：</strong> 基于时间轴缩放比例计算，支持精确调整</li>
                <li><strong>约束验证：</strong> 自动限制最小时长和最大时长（60秒）</li>
                <li><strong>实时预览：</strong> 拖拽过程中实时更新分镜宽度</li>
                <li><strong>数据同步：</strong> 拖拽结束后自动保存到服务器</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">使用方法</div>
            <div class="test-description">
                1. 切换到<span class="highlight">时间轴视图</span>模式<br>
                2. 将鼠标悬停在分镜右侧，会出现<span class="highlight">蓝色拖拽手柄</span><br>
                3. 按住手柄向左右拖拽调整播放时长<br>
                4. 释放鼠标完成调整，系统自动保存
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">技术要点</div>
            <ul class="feature-list">
                <li><strong>事件处理：</strong> 使用 mousedown/mousemove/mouseup 实现拖拽</li>
                <li><strong>状态管理：</strong> playDurationResizing 状态跟踪拖拽过程</li>
                <li><strong>样式设计：</strong> 参考 BgAudioClip 的 clip-handle 样式</li>
                <li><strong>数据流：</strong> 子组件 → 父组件 → API 保存</li>
                <li><strong>内存管理：</strong> 组件卸载时清理事件监听器</li>
            </ul>
        </div>
    </div>
</body>
</html>
