# 版本管理和自动更新系统

## 概述

本系统实现了完整的版本管理和自动更新提醒功能，包括：
- 🔄 自动版本检测
- 📦 构建时版本号生成
- 🚀 部署脚本集成
- 📱 用户友好的更新提醒

## 版本号规则

### 语义化版本号格式
```
主版本.次版本.修订版本
例如: 1.0.20241201143022
```

- **主版本**: 重大功能更新或架构变更
- **次版本**: 新功能添加或重要改进
- **修订版本**: 基于时间戳的构建版本 (YYYYMMDDHHMISS)

## 部署脚本使用

### 1. 开发环境部署 (`deploy.sh`)

```bash
# 直接运行
./deploy.sh

# 或者指定自定义版本号
export npm_package_version="1.0.20241201143022"
./deploy.sh
```

**特点:**
- 自动生成基于时间戳的版本号
- 部署到开发服务器
- 设置 `NODE_ENV=development`

### 2. 生产环境部署 (`deploy-prod.sh`)

```bash
# 直接运行（会有确认提示）
./deploy-prod.sh

# 或者指定自定义版本号
export npm_package_version="1.1.20241201143022"
./deploy-prod.sh
```

**特点:**
- 生成语义化版本号
- 创建版本备份
- 部署确认机制
- 版本历史记录
- 设置 `NODE_ENV=production`

## 版本文件结构

构建后会生成 `version.json` 文件：

```json
{
  "version": "1.0.20241201143022",
  "buildTime": 1701425422000,
  "hash": "abc123def456",
  "gitCommit": "a1b2c3d4",
  "buildEnv": "production",
  "deployTime": "2024-12-01T14:30:22.000Z"
}
```

### 字段说明

- `version`: 版本号（来自 npm_package_version 环境变量）
- `buildTime`: 构建时间戳（毫秒）
- `hash`: 随机生成的构建哈希值
- `gitCommit`: Git 提交哈希（自动获取）
- `buildEnv`: 构建环境（development/production）
- `deployTime`: 部署时间（ISO 格式）

## 版本检测机制

### 1. 检测频率
- **定时检查**: 每1分钟检查一次（可配置）
- **页面激活**: 页面重新可见时检查
- **多标签同步**: 通过 localStorage 同步

### 2. 版本比较逻辑

优先级顺序：
1. **构建时间比较**: `buildTime` 更新则认为有新版本
2. **哈希值比较**: `hash` 不同则认为有新版本  
3. **版本号比较**: 语义化版本号比较

### 3. 用户体验

- 🎨 美观的更新提醒弹框
- ⌨️ 键盘快捷键支持（ESC关闭，Enter确认）
- 📱 移动端适配
- 🌙 深色主题支持
- 🔒 防误操作设计

## 自定义配置

### 修改检查频率

```javascript
// 在 versionService.js 中修改
this.options = {
  checkIntervalMs: 5 * 60 * 1000, // 改为5分钟
  // ... 其他配置
};
```

### 自定义版本号

```bash
# 在部署脚本中设置
export npm_package_version="2.0.0"
npm run build
```

### 环境变量支持

构建时支持以下环境变量：

- `npm_package_version`: 版本号
- `NODE_ENV`: 构建环境
- `GIT_COMMIT`: Git 提交哈希
- `VERCEL_GIT_COMMIT_SHA`: Vercel 部署时的提交哈希
- `GITHUB_SHA`: GitHub Actions 中的提交哈希

## 测试功能

### 1. 测试版本构建

```bash
# 运行测试脚本
./test-version-build.sh
```

### 2. 手动测试步骤

1. **修改版本文件**: 编辑 `public/version.json`
2. **触发检查**: 刷新页面或等待自动检查
3. **验证弹框**: 确认更新提醒正常显示
4. **测试操作**: 验证"立即更新"、"稍后提醒"等功能

### 3. 多环境测试

```bash
# 开发环境
export NODE_ENV=development
export npm_package_version="1.0.dev"
npm run build

# 生产环境  
export NODE_ENV=production
export npm_package_version="1.0.prod"
npm run build:prod
```

## 最佳实践

### 1. 版本号管理

- 使用语义化版本号
- 重大更新时增加主版本号
- 新功能时增加次版本号
- 修复和小改动使用时间戳

### 2. 部署流程

1. **开发测试**: 使用 `deploy.sh` 部署到开发环境
2. **功能验证**: 确认新功能正常工作
3. **生产部署**: 使用 `deploy-prod.sh` 部署到生产环境
4. **版本确认**: 验证版本更新提醒正常工作

### 3. 监控和维护

- 定期检查版本历史记录
- 监控用户更新行为
- 及时处理版本检测异常

## 故障排除

### 1. 版本文件未生成

**可能原因:**
- 构建插件未正确配置
- 环境变量未设置
- 构建过程出错

**解决方案:**
```bash
# 检查环境变量
echo $npm_package_version
echo $NODE_ENV

# 手动设置并构建
export npm_package_version="1.0.test"
npm run build
```

### 2. 版本检测不工作

**可能原因:**
- version.json 文件不可访问
- 网络连接问题
- 浏览器缓存问题

**解决方案:**
```bash
# 检查文件是否存在
curl http://localhost:3000/version.json

# 清除浏览器缓存
# 检查控制台错误信息
```

### 3. 弹框不显示

**可能原因:**
- 版本比较逻辑问题
- 组件未正确集成
- 事件监听器未设置

**解决方案:**
```javascript
// 在浏览器控制台中测试
console.log(window.versionService);
window.versionService.checkVersion();
```

## 扩展功能

### 1. 版本更新日志

可以在版本文件中添加更新日志：

```json
{
  "version": "1.0.1",
  "changelog": [
    "修复了登录问题",
    "优化了页面加载速度"
  ]
}
```

### 2. 强制更新

对于重要的安全更新：

```json
{
  "version": "1.0.1",
  "forceUpdate": true,
  "updateMessage": "发现重要安全更新，请立即更新"
}
```

### 3. 分环境配置

```javascript
const config = {
  development: { 
    checkIntervalMs: 30000,
    enableVisibilityCheck: true
  },
  production: { 
    checkIntervalMs: 300000,
    enableVisibilityCheck: true
  }
};
```

## 技术支持

如遇问题，请：
1. 检查浏览器控制台错误
2. 验证网络请求状态
3. 确认版本文件格式正确
4. 联系开发团队获取支持
