#!/bin/bash

# 最终版本测试脚本
echo "=== 最终版本测试 ==="

# 定义颜色
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 生成测试版本号
TEST_VERSION="1.0.$(date +%Y%m%d%H%M%S)"
echo -e "${YELLOW}测试版本号: ${TEST_VERSION}${NC}"

echo ""
echo -e "${YELLOW}=== 步骤1: 设置环境变量 ===${NC}"
export npm_package_version="${TEST_VERSION}"
export APP_VERSION="${TEST_VERSION}"
export VITE_APP_VERSION="${TEST_VERSION}"
export NODE_ENV="development"

echo "环境变量设置:"
echo "  npm_package_version: ${npm_package_version}"
echo "  APP_VERSION: ${APP_VERSION}"
echo "  VITE_APP_VERSION: ${VITE_APP_VERSION}"
echo "  NODE_ENV: ${NODE_ENV}"

echo ""
echo -e "${YELLOW}=== 步骤2: 运行调试脚本 ===${NC}"
node debug-env.js

echo ""
echo -e "${YELLOW}=== 步骤3: 运行构建调试 ===${NC}"
echo "运行: npm run build:debug"
npm_package_version="${TEST_VERSION}" APP_VERSION="${TEST_VERSION}" VITE_APP_VERSION="${TEST_VERSION}" NODE_ENV="development" npm run build:debug

echo ""
echo -e "${YELLOW}=== 步骤4: 检查构建结果 ===${NC}"
if [ -f "dist/version.json" ]; then
    echo -e "${GREEN}✓ 版本文件已生成${NC}"
    echo "内容:"
    cat dist/version.json
    
    # 检查版本号是否正确
    BUILT_VERSION=$(cat dist/version.json | grep -o '"version": "[^"]*"' | cut -d'"' -f4)
    echo ""
    echo "期望版本号: ${TEST_VERSION}"
    echo "实际版本号: ${BUILT_VERSION}"
    
    if [ "${BUILT_VERSION}" = "${TEST_VERSION}" ]; then
        echo -e "${GREEN}✓ 版本号匹配！问题已解决${NC}"
    else
        echo -e "${RED}✗ 版本号不匹配，问题仍然存在${NC}"
    fi
else
    echo -e "${RED}✗ 版本文件未生成${NC}"
fi

echo ""
echo -e "${YELLOW}=== 步骤5: 测试部署脚本 ===${NC}"
echo "如果上面的测试成功，现在测试部署脚本:"
echo "./deploy.sh"

echo ""
echo -e "${GREEN}测试完成！${NC}"
