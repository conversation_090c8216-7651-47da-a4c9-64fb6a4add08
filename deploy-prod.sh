#!/bin/bash

# 显示执行的命令
set -x

# 定义颜色
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 正式环境服务器信息
SERVER="8.133.3.160" # 正式环境服务器地址
USER="root"
PASSWORD="Zlxj@123456"
REMOTE_DIR="/var/www/smart/" # 正式环境部署目录
VERSION_DIR="/var/www/versions/" # 版本存储目录
VERSION_NAME="v$(date +%Y%m%d_%H%M%S)" # 基于时间的版本号

# 生成语义化版本号
SEMANTIC_VERSION="1.0.$(date +%Y%m%d%H%M%S)"
echo -e "${YELLOW}===== 正式环境部署脚本 =====${NC}"
echo -e "${YELLOW}部署版本号: ${SEMANTIC_VERSION}${NC}"
echo -e "${YELLOW}版本标识: ${VERSION_NAME}${NC}"

# 设置多种环境变量，确保构建时能正确获取
export npm_package_version="${SEMANTIC_VERSION}"
export APP_VERSION="${SEMANTIC_VERSION}"
export VITE_APP_VERSION="${SEMANTIC_VERSION}"
export NODE_ENV="production"

# 显示设置的环境变量
echo -e "${YELLOW}环境变量设置:${NC}"
echo "  npm_package_version: ${npm_package_version}"
echo "  APP_VERSION: ${APP_VERSION}"
echo "  VITE_APP_VERSION: ${VITE_APP_VERSION}"
echo "  NODE_ENV: ${NODE_ENV}"

echo -e "${GREEN}开始构建项目...${NC}"

# 构建生产环境项目，显式传递环境变量
npm_package_version="${SEMANTIC_VERSION}" APP_VERSION="${SEMANTIC_VERSION}" VITE_APP_VERSION="${SEMANTIC_VERSION}" NODE_ENV="production" npm run build:prod

# 检查构建是否成功
if [ $? -ne 0 ]; then
    echo -e "${RED}构建失败，退出部署脚本${NC}"
    exit 1
fi

# 添加确认步骤
echo -e "${YELLOW}警告: 即将上传文件到正式环境服务器 ${SERVER}${NC}"
echo -e "${YELLOW}本次部署版本: ${VERSION_NAME}${NC}"
echo -e "${YELLOW}请确认是否继续? (y/n)${NC}"
read -r confirm
if [[ ! "$confirm" =~ ^[yY]$ ]]; then
    echo -e "${RED}部署已取消${NC}"
    exit 0
fi

echo -e "${GREEN}构建完成，开始上传文件到正式环境服务器...${NC}"

# 创建备份（可选）
echo -e "${GREEN}在服务器上创建当前版本的备份...${NC}"
BACKUP_DIR="${REMOTE_DIR}_backup_$(date +%Y%m%d_%H%M%S)"
sshpass -p "${PASSWORD}" ssh ${USER}@${SERVER} "if [ -d ${REMOTE_DIR} ]; then cp -r ${REMOTE_DIR} ${BACKUP_DIR}; fi"

# 使用 rsync 上传文件
# -a: 归档模式，保留所有文件属性
# -v: 显示详细信息
# -z: 压缩传输
# --delete: 删除目标目录中有而源目录中没有的文件
# --progress: 显示传输进度
echo -e "${GREEN}上传新文件到服务器...${NC}"
sshpass -p "${PASSWORD}" rsync -avz --delete --progress ./dist/ ${USER}@${SERVER}:${REMOTE_DIR}

# 检查上传是否成功
if [ $? -ne 0 ]; then
    echo -e "${RED}文件上传失败${NC}"
    exit 1
fi

# 版本管理 - 将当前版本存储到版本目录
echo -e "${GREEN}正在保存版本 ${VERSION_NAME}...${NC}"
# 确保版本目录存在
sshpass -p "${PASSWORD}" ssh ${USER}@${SERVER} "mkdir -p ${VERSION_DIR}"
# 创建当前版本目录
CURRENT_VERSION_DIR="${VERSION_DIR}${VERSION_NAME}"
sshpass -p "${PASSWORD}" ssh ${USER}@${SERVER} "mkdir -p ${CURRENT_VERSION_DIR}"
# 复制当前部署到版本目录
sshpass -p "${PASSWORD}" ssh ${USER}@${SERVER} "cp -r ${REMOTE_DIR}* ${CURRENT_VERSION_DIR}/"
# 更新版本记录文件
DEPLOY_INFO="版本: ${VERSION_NAME}, 部署时间: $(date '+%Y-%m-%d %H:%M:%S'), 部署人: $(whoami)"
sshpass -p "${PASSWORD}" ssh ${USER}@${SERVER} "echo '${DEPLOY_INFO}' >> ${VERSION_DIR}version_history.log"

# 可能需要的其他操作（清除缓存等）
echo -e "${GREEN}清理服务器缓存...${NC}"
sshpass -p "${PASSWORD}" ssh ${USER}@${SERVER} "find ${REMOTE_DIR} -type f -name '*.cache' -delete"

echo -e "${YELLOW}=====================${NC}"
echo -e "${GREEN}正式环境部署完成！${NC}"
echo -e "${GREEN}版本 ${VERSION_NAME} 已成功保存到 ${VERSION_DIR}${NC}"
echo -e "${YELLOW}=====================${NC}" 