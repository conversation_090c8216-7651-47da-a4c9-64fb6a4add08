import { ElMessage } from 'element-plus'
import router from '../router'

/**
 * HTTP错误处理工具
 * 统一处理各种HTTP状态码错误
 */
class HttpErrorHandler {
  /**
   * 处理HTTP响应错误
   * @param {Object} error - axios错误对象
   * @returns {Promise} - 返回rejected Promise
   */
  static handleError(error) {
    if (error.response) {
      const status = error.response.status
      const config = error.config
      
      console.error(`HTTP ${status} 错误:`, {
        url: config?.url,
        method: config?.method,
        status,
        statusText: error.response.statusText,
        data: error.response.data
      })
      
      // 根据状态码处理不同错误
      switch (status) {
        case 502:
          this.handle502Error(error)
          break
        case 500:
          this.handle500Error(error)
          break
        case 503:
          this.handle503Error(error)
          break
        case 504:
          this.handle504Error(error)
          break
        case 401:
          this.handle401Error(error)
          break
        case 403:
          this.handle403Error(error)
          break
        case 404:
          this.handle404Error(error)
          break
        case 429:
          this.handle429Error(error)
          break
        default:
          this.handleGenericError(error)
      }
    } else if (error.request) {
      // 网络错误
      this.handleNetworkError(error)
    } else {
      // 请求配置错误
      this.handleConfigError(error)
    }
    
    return Promise.reject(error)
  }
  
  /**
   * 处理502 Bad Gateway错误
   * @param {Object} error - 错误对象
   */
  static handle502Error(error) {
    console.error('502 Bad Gateway 错误:', error)
    
    // 跳转到502错误页面
    if (router.currentRoute.value.path !== '/error/502') {
      router.push('/error/502')
    }
    
    ElMessage.error({
      message: '服务器暂时无法处理请求，请稍后重试',
      duration: 5000,
      showClose: true
    })
  }
  
  /**
   * 处理500内部服务器错误
   * @param {Object} error - 错误对象
   */
  static handle500Error(error) {
    console.error('500 Internal Server Error:', error)
    ElMessage.error({
      message: '服务器内部错误，请稍后重试',
      duration: 5000,
      showClose: true
    })
  }
  
  /**
   * 处理503服务不可用错误
   * @param {Object} error - 错误对象
   */
  static handle503Error(error) {
    console.error('503 Service Unavailable:', error)
    ElMessage.error({
      message: '服务暂时不可用，请稍后重试',
      duration: 5000,
      showClose: true
    })
  }
  
  /**
   * 处理504网关超时错误
   * @param {Object} error - 错误对象
   */
  static handle504Error(error) {
    console.error('504 Gateway Timeout:', error)
    ElMessage.error({
      message: '网关超时，请稍后重试',
      duration: 5000,
      showClose: true
    })
  }
  
  /**
   * 处理401未授权错误
   * @param {Object} error - 错误对象
   */
  static handle401Error(error) {
    console.error('401 Unauthorized:', error)
    
    // 清除本地存储的认证信息
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    
    // 跳转到登录页面
    if (router.currentRoute.value.path !== '/login') {
      router.push('/login')
    }
    
    ElMessage.error({
      message: '登录已过期，请重新登录',
      duration: 3000,
      showClose: true
    })
  }
  
  /**
   * 处理403禁止访问错误
   * @param {Object} error - 错误对象
   */
  static handle403Error(error) {
    console.error('403 Forbidden:', error)
    ElMessage.error({
      message: '没有权限访问该资源',
      duration: 3000,
      showClose: true
    })
  }
  
  /**
   * 处理404未找到错误
   * @param {Object} error - 错误对象
   */
  static handle404Error(error) {
    console.error('404 Not Found:', error)
    ElMessage.error({
      message: '请求的资源不存在',
      duration: 3000,
      showClose: true
    })
  }
  
  /**
   * 处理429请求过多错误
   * @param {Object} error - 错误对象
   */
  static handle429Error(error) {
    console.error('429 Too Many Requests:', error)
    ElMessage.error({
      message: '请求过于频繁，请稍后重试',
      duration: 5000,
      showClose: true
    })
  }
  
  /**
   * 处理网络错误
   * @param {Object} error - 错误对象
   */
  static handleNetworkError(error) {
    console.error('Network Error:', error)
    ElMessage.error({
      message: '网络连接失败，请检查网络设置',
      duration: 5000,
      showClose: true
    })
  }
  
  /**
   * 处理请求配置错误
   * @param {Object} error - 错误对象
   */
  static handleConfigError(error) {
    console.error('Request Config Error:', error)
    ElMessage.error({
      message: '请求配置错误',
      duration: 3000,
      showClose: true
    })
  }
  
  /**
   * 处理通用错误
   * @param {Object} error - 错误对象
   */
  static handleGenericError(error) {
    const status = error.response?.status
    console.error(`HTTP ${status} Error:`, error)
    
    ElMessage.error({
      message: `请求失败 (${status})`,
      duration: 3000,
      showClose: true
    })
  }
  
  /**
   * 创建响应拦截器
   * @returns {Function} - axios响应拦截器函数
   */
  static createResponseInterceptor() {
    return (error) => {
      return this.handleError(error)
    }
  }
  
  /**
   * 检查是否为502错误
   * @param {Object} error - 错误对象
   * @returns {boolean} - 是否为502错误
   */
  static is502Error(error) {
    return error.response && error.response.status === 502
  }
  
  /**
   * 检查是否为服务器错误 (5xx)
   * @param {Object} error - 错误对象
   * @returns {boolean} - 是否为服务器错误
   */
  static isServerError(error) {
    const status = error.response?.status
    return status >= 500 && status < 600
  }
  
  /**
   * 检查是否为客户端错误 (4xx)
   * @param {Object} error - 错误对象
   * @returns {boolean} - 是否为客户端错误
   */
  static isClientError(error) {
    const status = error.response?.status
    return status >= 400 && status < 500
  }
}

export default HttpErrorHandler

/**
 * 创建通用的axios响应拦截器
 * @returns {Array} - [成功处理函数, 错误处理函数]
 */
export function createHttpInterceptors() {
  return [
    // 成功响应处理
    (response) => response,
    // 错误响应处理
    (error) => HttpErrorHandler.handleError(error)
  ]
}

/**
 * 简化的502错误处理函数
 * @param {Object} error - 错误对象
 * @returns {Promise} - rejected Promise
 */
export function handle502Error(error) {
  return HttpErrorHandler.handle502Error(error)
}
