/**
 * 路由历史记录工具
 * 用于跟踪用户的导航历史，支持返回上一页功能
 */

class RouteHistory {
  constructor() {
    this.history = []
    this.maxHistoryLength = 10 // 最多保存10条历史记录
    this.excludeRoutes = ['/error/502', '/test/502'] // 排除的路由
  }

  /**
   * 添加路由到历史记录
   * @param {Object} route - Vue Router路由对象
   */
  push(route) {
    // 排除特定路由
    if (this.excludeRoutes.includes(route.path)) {
      return
    }

    const routeInfo = {
      path: route.path,
      name: route.name,
      fullPath: route.fullPath,
      query: route.query,
      params: route.params,
      timestamp: Date.now()
    }

    // 避免重复添加相同路由
    const lastRoute = this.history[this.history.length - 1]
    if (lastRoute && lastRoute.fullPath === routeInfo.fullPath) {
      return
    }

    this.history.push(routeInfo)

    // 限制历史记录长度
    if (this.history.length > this.maxHistoryLength) {
      this.history.shift()
    }

    // 保存到sessionStorage
    this.saveToStorage()
  }

  /**
   * 获取上一页路由信息
   * @returns {Object|null} - 上一页路由信息
   */
  getPrevious() {
    if (this.history.length < 2) {
      return null
    }
    return this.history[this.history.length - 2]
  }

  /**
   * 获取当前页路由信息
   * @returns {Object|null} - 当前页路由信息
   */
  getCurrent() {
    if (this.history.length === 0) {
      return null
    }
    return this.history[this.history.length - 1]
  }

  /**
   * 获取完整历史记录
   * @returns {Array} - 历史记录数组
   */
  getAll() {
    return [...this.history]
  }

  /**
   * 清空历史记录
   */
  clear() {
    this.history = []
    this.removeFromStorage()
  }

  /**
   * 从历史记录中移除最后一条记录
   */
  pop() {
    if (this.history.length > 0) {
      this.history.pop()
      this.saveToStorage()
    }
  }

  /**
   * 检查是否可以返回上一页
   * @returns {boolean} - 是否可以返回
   */
  canGoBack() {
    return this.history.length > 1
  }

  /**
   * 获取返回路径
   * @returns {string|null} - 返回路径
   */
  getBackPath() {
    const previous = this.getPrevious()
    return previous ? previous.fullPath : null
  }

  /**
   * 保存到sessionStorage
   */
  saveToStorage() {
    try {
      sessionStorage.setItem('routeHistory', JSON.stringify(this.history))
    } catch (error) {
      console.warn('Failed to save route history to sessionStorage:', error)
    }
  }

  /**
   * 从sessionStorage加载
   */
  loadFromStorage() {
    try {
      const stored = sessionStorage.getItem('routeHistory')
      if (stored) {
        this.history = JSON.parse(stored)
        // 清理过期记录（超过1小时）
        const now = Date.now()
        this.history = this.history.filter(route => 
          now - route.timestamp < 60 * 60 * 1000
        )
      }
    } catch (error) {
      console.warn('Failed to load route history from sessionStorage:', error)
      this.history = []
    }
  }

  /**
   * 从sessionStorage移除
   */
  removeFromStorage() {
    try {
      sessionStorage.removeItem('routeHistory')
    } catch (error) {
      console.warn('Failed to remove route history from sessionStorage:', error)
    }
  }

  /**
   * 获取面包屑导航数据
   * @param {number} maxItems - 最大显示项目数
   * @returns {Array} - 面包屑数据
   */
  getBreadcrumbs(maxItems = 5) {
    const recent = this.history.slice(-maxItems)
    return recent.map(route => ({
      name: route.name || route.path,
      path: route.fullPath,
      timestamp: route.timestamp
    }))
  }

  /**
   * 查找特定路由在历史中的位置
   * @param {string} path - 路由路径
   * @returns {number} - 索引位置，-1表示未找到
   */
  findRoute(path) {
    return this.history.findIndex(route => route.fullPath === path)
  }

  /**
   * 获取统计信息
   * @returns {Object} - 统计信息
   */
  getStats() {
    const now = Date.now()
    const recentRoutes = this.history.filter(route => 
      now - route.timestamp < 30 * 60 * 1000 // 30分钟内
    )

    return {
      totalRoutes: this.history.length,
      recentRoutes: recentRoutes.length,
      canGoBack: this.canGoBack(),
      oldestTimestamp: this.history.length > 0 ? this.history[0].timestamp : null,
      newestTimestamp: this.history.length > 0 ? this.history[this.history.length - 1].timestamp : null
    }
  }
}

// 创建全局实例
const routeHistory = new RouteHistory()

// 初始化时从存储加载
routeHistory.loadFromStorage()

export default routeHistory

/**
 * Vue Router导航守卫
 * 在路由变化时自动记录历史
 */
export function setupRouteHistoryGuard(router) {
  router.beforeEach((to, from, next) => {
    // 记录来源路由
    if (from.name !== null) {
      routeHistory.push(from)
    }
    next()
  })

  router.afterEach((to) => {
    // 记录目标路由
    routeHistory.push(to)
  })
}

/**
 * 获取返回上一页的路径
 * @returns {string|null} - 返回路径
 */
export function getBackPath() {
  return routeHistory.getBackPath()
}

/**
 * 检查是否可以返回上一页
 * @returns {boolean} - 是否可以返回
 */
export function canGoBack() {
  return routeHistory.canGoBack()
}

/**
 * 清空路由历史
 */
export function clearRouteHistory() {
  routeHistory.clear()
}

/**
 * 获取路由历史统计
 * @returns {Object} - 统计信息
 */
export function getRouteStats() {
  return routeHistory.getStats()
}
