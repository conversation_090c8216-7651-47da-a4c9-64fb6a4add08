import { ElMessage } from 'element-plus'

/**
 * 消息管理器 - 确保新消息总是显示在固定位置
 * 当新消息出现时，自动关闭之前的消息
 */
class MessageManager {
  constructor() {
    this.currentMessage = null
    this.messageQueue = []
  }

  /**
   * 显示消息，自动关闭之前的消息
   * @param {Object|String} options - 消息选项或消息文本
   * @param {String} type - 消息类型 (success, error, warning, info)
   */
  show(options, type = 'info') {
    // 如果有当前消息，立即关闭它
    if (this.currentMessage) {
      this.currentMessage.close()
    }

    // 清空消息队列中的所有消息
    this.messageQueue.forEach(msg => {
      if (msg && typeof msg.close === 'function') {
        msg.close()
      }
    })
    this.messageQueue = []

    // 处理参数
    let messageOptions = {}
    if (typeof options === 'string') {
      messageOptions = {
        message: options,
        type: type
      }
    } else {
      messageOptions = { ...options }
    }

    // 设置默认选项
    const defaultOptions = {
      duration: 3000,
      showClose: false,
      center: false, // 我们通过 CSS 控制居中
      offset: 20,
      onClose: () => {
        this.currentMessage = null
      }
    }

    // 合并选项
    const finalOptions = { ...defaultOptions, ...messageOptions }

    // 显示新消息
    this.currentMessage = ElMessage(finalOptions)
    
    return this.currentMessage
  }

  /**
   * 显示成功消息
   * @param {String|Object} options - 消息内容或选项
   */
  success(options) {
    if (typeof options === 'string') {
      return this.show({ message: options, type: 'success' })
    }
    return this.show({ ...options, type: 'success' })
  }

  /**
   * 显示错误消息
   * @param {String|Object} options - 消息内容或选项
   */
  error(options) {
    if (typeof options === 'string') {
      return this.show({ message: options, type: 'error' })
    }
    return this.show({ ...options, type: 'error' })
  }

  /**
   * 显示警告消息
   * @param {String|Object} options - 消息内容或选项
   */
  warning(options) {
    if (typeof options === 'string') {
      return this.show({ message: options, type: 'warning' })
    }
    return this.show({ ...options, type: 'warning' })
  }

  /**
   * 显示信息消息
   * @param {String|Object} options - 消息内容或选项
   */
  info(options) {
    if (typeof options === 'string') {
      return this.show({ message: options, type: 'info' })
    }
    return this.show({ ...options, type: 'info' })
  }

  /**
   * 关闭当前消息
   */
  close() {
    if (this.currentMessage) {
      this.currentMessage.close()
      this.currentMessage = null
    }
  }

  /**
   * 关闭所有消息
   */
  closeAll() {
    // 关闭当前消息
    this.close()
    
    // 关闭队列中的所有消息
    this.messageQueue.forEach(msg => {
      if (msg && typeof msg.close === 'function') {
        msg.close()
      }
    })
    this.messageQueue = []

    // 关闭页面上所有可见的消息
    const messages = document.querySelectorAll('.el-message')
    messages.forEach(msg => {
      if (msg.parentNode) {
        msg.style.transition = 'all 0.3s ease-out'
        msg.style.opacity = '0'
        msg.style.transform = 'translateX(-50%) translateY(-20px) scale(0.95)'
        setTimeout(() => {
          if (msg.parentNode) {
            msg.parentNode.removeChild(msg)
          }
        }, 300)
      }
    })
  }
}

// 创建全局实例
const messageManager = new MessageManager()

// 导出实例和类
export default messageManager
export { MessageManager }

// 为了方便使用，也可以直接导出方法
export const showMessage = messageManager.show.bind(messageManager)
export const showSuccess = messageManager.success.bind(messageManager)
export const showError = messageManager.error.bind(messageManager)
export const showWarning = messageManager.warning.bind(messageManager)
export const showInfo = messageManager.info.bind(messageManager)
export const closeMessage = messageManager.close.bind(messageManager)
export const closeAllMessages = messageManager.closeAll.bind(messageManager)
