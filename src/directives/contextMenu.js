import { useContextMenuDirective } from '../composables/useContextMenu.js'

// 创建右键菜单指令
export const contextMenuDirective = {
  mounted(el, binding) {
    const { showMenu } = useContextMenuDirective()
    
    // 保存处理函数引用，用于后续清理
    el._contextMenuHandler = (event) => {
      showMenu(el, binding, event)
    }
    
    // 添加右键事件监听
    el.addEventListener('contextmenu', el._contextMenuHandler)
    
    // 添加样式提示（可选）
    if (binding.modifiers.cursor) {
      el.style.cursor = 'context-menu'
    }
  },
  
  updated(el, binding) {
    // 当绑定值更新时，不需要重新绑定事件
    // 事件处理函数会自动使用最新的 binding.value
  },
  
  unmounted(el) {
    // 清理事件监听器
    if (el._contextMenuHandler) {
      el.removeEventListener('contextmenu', el._contextMenuHandler)
      delete el._contextMenuHandler
    }
    
    // 清理样式
    if (el.style.cursor === 'context-menu') {
      el.style.cursor = ''
    }
  }
}

// 导出指令配置对象
export default {
  install(app) {
    app.directive('context-menu', contextMenuDirective)
  }
}

// 使用示例：
/*
// 基本用法 - 传入菜单项数组
<div v-context-menu="menuItems">右键点击我</div>

// 高级用法 - 传入配置对象
<div v-context-menu="{ items: menuItems, callback: handleMenuClick }">右键点击我</div>

// 添加鼠标样式提示
<div v-context-menu.cursor="menuItems">右键点击我（带鼠标样式）</div>

// 菜单项格式：
const menuItems = [
  {
    label: '复制',
    icon: 'el-icon-copy-document',
    shortcut: 'Ctrl+C',
    action: 'copy'
  },
  {
    label: '粘贴',
    icon: 'el-icon-document',
    shortcut: 'Ctrl+V',
    action: 'paste',
    disabled: true
  },
  { divider: true }, // 分割线
  {
    label: '删除',
    icon: 'el-icon-delete',
    action: 'delete'
  }
]

// 回调函数格式：
const handleMenuClick = (item, index, element) => {
  console.log('点击了菜单项:', item.label)
  console.log('菜单项索引:', index)
  console.log('触发元素:', element)
  
  // 根据 action 执行相应操作
  switch (item.action) {
    case 'copy':
      // 执行复制操作
      break
    case 'paste':
      // 执行粘贴操作
      break
    case 'delete':
      // 执行删除操作
      break
  }
}
*/
