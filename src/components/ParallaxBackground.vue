<template>
  <div 
    ref="containerRef"
    class="parallax-container"
    @mousemove="handleMouseMove"
    @mouseleave="handleMouseLeave"
  >
    <div 
      v-for="(layer, index) in layers" 
      :key="index"
      class="parallax-layer"
      :style="getLayerStyle(layer, index)"
    >
      <div 
        class="layer-content" 
        :style="{ 
          backgroundColor: layer.color,
          filter: `blur(${layer.blur}px)`,
          opacity: layer.opacity,
          width: `${layer.size}px`,
          height: `${layer.size}px`,
          borderRadius: layer.shape === 'circle' ? '50%' : '30% 70% 70% 30% / 30% 30% 70% 70%'
        }"
      ></div>
    </div>
    <slot></slot>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  count: {
    type: Number,
    default: 5
  },
  intensity: {
    type: Number,
    default: 20 // 视差强度
  },
  colors: {
    type: Array,
    default: () => [
      'rgba(99, 102, 241, 0.08)',  // 蓝紫色
      'rgba(14, 165, 233, 0.05)',  // 蓝色
      'rgba(139, 92, 246, 0.07)',  // 紫色
      'rgba(20, 184, 166, 0.06)',  // 青色
      'rgba(239, 68, 68, 0.04)'    // 红色
    ]
  }
});

const containerRef = ref(null);
const mousePosition = reactive({ x: 0, y: 0 });
const targetPosition = reactive({ x: 0, y: 0 });
const currentPosition = reactive({ x: 0, y: 0 });
let animationId = null;

// 创建图层数据
const layers = computed(() => {
  return Array.from({ length: props.count }, (_, index) => {
    const depth = (index + 1) / props.count; // 图层深度，决定移动速度
    return {
      depth,
      color: props.colors[index % props.colors.length],
      size: 100 + Math.random() * 400, // 图层大小
      x: Math.random() * 100, // 初始x位置 (百分比)
      y: Math.random() * 100, // 初始y位置 (百分比)
      opacity: 0.3 + Math.random() * 0.4, // 透明度
      blur: 20 + Math.random() * 40, // 模糊程度
      shape: Math.random() > 0.5 ? 'circle' : 'blob' // 形状类型
    };
  });
});

// 处理鼠标移动
function handleMouseMove(e) {
  if (!containerRef.value) return;
  
  const rect = containerRef.value.getBoundingClientRect();
  
  // 计算鼠标在容器内的相对位置 (-1 到 1 之间)
  mousePosition.x = ((e.clientX - rect.left) / rect.width) * 2 - 1;
  mousePosition.y = ((e.clientY - rect.top) / rect.height) * 2 - 1;
  
  // 设置目标位置
  targetPosition.x = mousePosition.x * props.intensity;
  targetPosition.y = mousePosition.y * props.intensity;
  
  if (!animationId) {
    animationId = requestAnimationFrame(updatePosition);
  }
}

// 鼠标离开时重置位置
function handleMouseLeave() {
  targetPosition.x = 0;
  targetPosition.y = 0;
}

// 平滑更新位置
function updatePosition() {
  // 平滑过渡到目标位置
  currentPosition.x += (targetPosition.x - currentPosition.x) * 0.1;
  currentPosition.y += (targetPosition.y - currentPosition.y) * 0.1;
  
  // 如果变化非常小，则停止动画
  if (
    Math.abs(targetPosition.x - currentPosition.x) < 0.01 &&
    Math.abs(targetPosition.y - currentPosition.y) < 0.01
  ) {
    currentPosition.x = targetPosition.x;
    currentPosition.y = targetPosition.y;
    cancelAnimationFrame(animationId);
    animationId = null;
  } else {
    animationId = requestAnimationFrame(updatePosition);
  }
}

// 获取图层样式
function getLayerStyle(layer, index) {
  const xOffset = currentPosition.x * layer.depth;
  const yOffset = currentPosition.y * layer.depth;
  
  let transform = `translate(${xOffset}px, ${yOffset}px)`;
  
  return {
    transform,
    zIndex: -10 - index, // 确保图层在内容之后
    left: `${layer.x}%`,
    top: `${layer.y}%`
  };
}

onMounted(() => {
  // 初始动画
  layers.value.forEach((layer, index) => {
    setTimeout(() => {
      layer.opacity = Math.max(0.2, layer.opacity);
    }, index * 100);
  });
});

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId);
    animationId = null;
  }
});
</script>

<style scoped>
.parallax-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.parallax-layer {
  position: absolute;
  pointer-events: none;
  will-change: transform;
  transition: opacity 0.8s ease;
}

.layer-content {
  border-radius: 50%;
  transition: background-color 0.5s ease, transform 0.3s ease;
}
</style> 