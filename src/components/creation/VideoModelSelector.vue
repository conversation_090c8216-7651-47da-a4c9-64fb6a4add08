<template>
  <div class="model-selector">
    <!-- <div class="selector-header">
      <h4 class="selector-title">模型选择</h4>
    </div> -->

    <!-- 模型列表 -->
    <div class="model-list">
      <div v-if="availableModels.length > 0" class="models-container">
        <div
          v-for="model in availableModels"
          :key="model.id"
          class="model-card"
          :class="{
            'selected': selectedModel === model.id,
            'disabled': model.disabled
          }"
          @click="selectModel(model)"
        >
          <div class="model-header">
            <h6 class="model-name">{{ model.name }}</h6>
            <div class="model-status">
              <span class="status-badge pro">{{ model.badge }}</span>
              <el-icon v-if="selectedModel === model.id" class="selected-icon">
                <Check />
              </el-icon>
            </div>
          </div>
          <p class="model-desc">{{ model.description }}</p>
          <div class="model-features">
            <span class="feature-label">特色功能:</span>
            <span class="feature-value">{{ model.features.join('、') }}</span>
          </div>
          <div class="model-specs">
            <span class="spec">最大参考图片: {{ model.maxImages }}张</span>
            <span class="spec">{{ model.imageRequired ? '图片必需' : '图片非必需' }}</span>
          </div>
        </div>
      </div>
      <div v-else class="loading-placeholder">
        <p>正在加载模型...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { Check } from '@element-plus/icons-vue'
import { getAvailableModels } from '@/api/creation.js'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  creationMode: {
    type: String,
    default: 'text-to-video'
  },
  type: {
    type: String,
    default: 'image' // 'image' | 'video'
  }
})

const emit = defineEmits(['update:modelValue', 'model-change'])

const selectedModel = ref(props.modelValue)
const availableModels = ref([])
const loading = ref(false)

// 当前选择的模型信息
const selectedModelInfo = computed(() => {
  return availableModels.value.find(m => m.id === selectedModel.value)
})

// 选择模型
const selectModel = (model) => {
  if (model && !model.disabled) {
    selectedModel.value = model.id
    emit('update:modelValue', model.id)
    emit('model-change', model)
  }
}

// 获取模型列表
const fetchModels = async () => {
  try {
    loading.value = true
    const response = await getAvailableModels(props.type)

    if (response.data && response.data.success) {
      availableModels.value = response.data.data || []

      // 如果没有选择模型，自动选择第一个模型
      if (!selectedModel.value && availableModels.value.length > 0) {
        selectModel(availableModels.value[0])
      }
    }
  } catch (error) {
    console.error('获取模型列表失败:', error)
    // 使用默认模型数据
    availableModels.value = getDefaultModels()

    // 自动选择第一个模型
    if (!selectedModel.value && availableModels.value.length > 0) {
      selectModel(availableModels.value[0])
    }
  } finally {
    loading.value = false
  }
}

// 默认模型数据（作为后备）
const getDefaultModels = () => {
  return [
      {
        id: 'doubao-seedance-1-0-pro-250528',
        name: 'SeedancePro',
        description: '支持文生视频或文+首帧生视频',
        badge: 'pro-edit',
        maxImages: 1,
        imageRequired: false,
        features: ['文生视频', '文+首帧生视频'],
        disabled: false
      },
      {
        id: 'doubao-seedance-1-0-lite-i2v-250428',
        name: 'SeedanceLite',
        description: '必传文+首帧或尾帧生视频',
        badge: 'max-edit',
        maxImages: 2,
        imageRequired: false,
        features: ['文+首帧或尾帧生视频'],
        disabled: false
      }
    ]
}

// 监听外部模型变化
watch(() => props.modelValue, (newValue) => {
  selectedModel.value = newValue
})

onMounted(() => {
  fetchModels()
})
</script>

<style scoped>
.model-selector {
  background: white;
  border-radius: 8px;
  /* border: 1px solid #e5e7eb; */
  overflow: hidden;
  padding: 8px;
}

.selector-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.selector-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.model-list {
}

.models-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.model-card {
  display: flex;
  flex-direction: column;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  gap: 4px;
}

.model-card:hover:not(.disabled) {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.model-card.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.model-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.model-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.model-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.model-desc {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 0 0;
  text-align: left;
}

.model-specs {
  display: flex;
  gap: 12px;
}

.spec {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
}

.model-features {
  display: flex;
  align-items: center;
  gap: 8px;
}

.feature-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.feature-value {
  font-size: 12px;
  color: #1f2937;
}

.status-badge {
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  background: #fef3c7;
  color: #92400e;
}

.selected-icon {
  color: #3b82f6;
  font-size: 18px;
}

.loading-placeholder {
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;
  font-size: 14px;
}

/* 暗色模式支持 */
body.dark .model-selector {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
}

body.dark .selector-header {
  background: var(--bg-quaternary);
  border-color: var(--border-color);
}

body.dark .selector-title {
  color: var(--text-primary);
}

body.dark .model-card {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
}

body.dark .model-card.selected {
  background: rgba(59, 130, 246, 0.1);
}

body.dark .model-name {
  color: var(--text-primary);
}

body.dark .model-desc {
  color: var(--text-secondary);
}

body.dark .feature-label {
  color: var(--text-secondary);
}

body.dark .feature-value {
  color: var(--text-primary);
}

body.dark .loading-placeholder {
  color: var(--text-secondary);
}
</style>
