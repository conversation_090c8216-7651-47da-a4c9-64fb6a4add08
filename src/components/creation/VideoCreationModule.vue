<template>
  <div class="video-creation-module">
    
    <!-- 创意输入区域 -->
    <div class="creative-input-section">
      <VideoCreativeInput
        v-model="promptText"
        :placeholder="inputPlaceholder"
        :is-creating="isGenerating"
        :uploaded-files="uploadedFiles"
        @update:uploadedFiles="handleFilesUpdate"
        @start-creation="handleStartGeneration"
        @open-setting="handleOpenSetting"
        :suggestions="suggestions"
        :is-loading-suggestions="isLoadingSuggestions"
        @refresh-suggestions="refreshSuggestions"
        :aspect-ratio="aspectRatio"
        :aspect-ratio-options="aspectRatioOptions"
        :batch-size="batchSize"
        :edit-strength="editStrength"
        :seed="seed"
        :creation-mode="creationMode"
        :selected-model="selectedModel"
        :model-name="selectedModel"
        :video-duration="videoDuration"
        @update:videoDuration="videoDuration = $event"
        @update:aspectRatio="aspectRatio = $event"
        @update:batchSize="batchSize = $event"
        @update:editStrength="editStrength = $event"
        @update:seed="seed = $event"
        @update:selectedModel="selectedModel = $event"
      />
    </div>



    <!-- 生成历史 -->
    <div class="generation-history">
      <!-- <div class="history-header">
        <h3 class="history-title">生成历史</h3>
        <span class="history-count">{{ historyCount }}</span>
      </div> -->
      
      <div class="history-content">
        <div v-if="generationHistory.length === 0" class="empty-history">
          <!-- <el-icon class="empty-icon">
            <Picture />
          </el-icon> -->
          <!-- <p class="empty-text">暂无生成历史</p> -->
          <p class="empty-hint">开始创作你的第一个视频吧！</p>
        </div>
        
        <div v-else class="history-grid">
          <div 
            v-for="item in generationHistory" 
            :key="item.id"
            class="history-item"
            :class="{ 'generating': item.status === 'generating' }"
          >
            <div class="item-video">
              <img v-if="item.imageUrl" :src="item.imageUrl" :alt="item.prompt" />
              <div v-else class="generating-placeholder">
                <el-icon class="generating-icon">
                  <Loading />
                </el-icon>
                <span class="generating-text">生成中...</span>
              </div>
            </div>
            <div class="item-info">
              <p class="item-prompt">{{ item.prompt }}</p>
              <div class="item-meta">
                <span class="item-model">{{ item.modelName }}</span>
                <span class="item-time">{{ formatTime(item.createdAt) }}</span>
              </div>
            </div>
            <div class="item-actions">
              <el-button 
                type="text" 
                size="small"
                @click="handleImageClick(item.imageUrl)"
                v-if="item.imageUrl"
              >
                预览
              </el-button>
              <el-button 
                type="text" 
                size="small"
                @click="toggleFavorite(item)"
              >
                {{ item.isFavorite ? '取消收藏' : '收藏' }}
              </el-button>
              <el-button 
                type="text" 
                size="small"
                @click="deleteHistoryItem(item.id)"
                class="delete-btn"
              >
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { MagicStick, Picture, Loading } from '@element-plus/icons-vue'
import { format } from 'date-fns'

import VideoCreativeInput from '@/components/creation/VideoCreativeInput.vue'
import CreationModeSelector from './CreationModeSelector.vue'

import {
  generateImageFromText,
  generateImageFromImage,
  generateImageFromMultipleImages,
  getGenerationTaskStatus,
  getGenerationHistory,
  deleteGenerationRecord,
  toggleFavoriteRecord
} from '@/api/creation.js'

// 响应式数据
const creationMode = ref('text-to-video')
const promptText = ref('')
const selectedModel = ref('')
const aspectRatio = ref('16:9')
const batchSize = ref(1)
const editStrength = ref(0.8)
const seed = ref('')
const uploadedFiles = ref([])
const isGenerating = ref(false)
const videoDuration = ref(5)

const generationHistory = ref([])
const suggestions = ref([])
const isLoadingSuggestions = ref(false)

// 计算属性
const inputPlaceholder = computed(() => {
  const placeholders = {
    'text-to-video': '描述你想要生成的视频，例如：一只可爱的小猫在花园里玩耍',
    'video-edit': '描述你想要对视频进行的修改，例如：将背景改为海滩',
    'multi-fusion': '描述你想要融合生成的视频，例如：将这些元素组合成一个梦幻场景'
  }
  return placeholders[creationMode.value] || '请输入你的创意描述'
})

const aspectRatioOptions = computed(() => [
  { label: '1:1', value: '1:1', width: 1024, height: 1024 },
  { label: '16:9', value: '16:9', width: 1920, height: 1080 },
  { label: '9:16', value: '9:16', width: 1080, height: 1920 },
  { label: '4:3', value: '4:3', width: 1024, height: 768 },
  { label: '3:4', value: '3:4', width: 768, height: 1024 }
])

const historyCount = computed(() => generationHistory.value.length)

// 方法
const handleModeChange = (mode) => {
  console.log('创作模式变更:', mode)
}

const handleModelChange = (model) => {
  console.log('模型变更:', model)
}

const handleFilesUpdate = (files) => {
  uploadedFiles.value = files
}

const handleOpenSetting = (settingType) => {
  // 设置处理逻辑移到 ImageCreativeInput 组件中
  console.log('打开设置:', settingType)
}

const handleStartGeneration = async () => {
  ElMessage.warning('功能开发中，敬请期待')
  return
  if (!promptText.value.trim()) {
    ElMessage.warning('请输入创意描述')
    return
  }

  isGenerating.value = true
  
  try {
    let response
    const baseParams = {
      prompt: promptText.value,
      model: selectedModel.value,
      aspectRatio: aspectRatio.value,
      batchSize: batchSize.value,
      seed: seed.value || undefined
    }

    // 根据创作模式调用不同API
    if (creationMode.value === 'text-to-video') {
      response = await generateImageFromText(baseParams)
    } else if (creationMode.value === 'video-edit' && uploadedFiles.value.length > 0) {
      response = await generateImageFromImage({
        ...baseParams,
        sourceImageUrl: uploadedFiles.value[0].url,
        strength: editStrength.value
      })
    } else if (creationMode.value === 'multi-fusion' && uploadedFiles.value.length >= 2) {
      response = await generateImageFromMultipleImages({
        ...baseParams,
        sourceImageUrls: uploadedFiles.value.map(f => f.url)
      })
    }

    if (response?.data?.success) {
      const taskId = response.data.data.taskId
      
      // 添加到历史记录（生成中状态）
      const historyItem = {
        id: taskId,
        prompt: promptText.value,
        modelName: selectedModel.value,
        status: 'generating',
        createdAt: new Date(),
        isFavorite: false
      }
      generationHistory.value.unshift(historyItem)
      
      // 开始轮询任务状态
      pollTaskStatus(taskId)
      
      ElMessage.success('图片生成任务已提交')
    } else {
      throw new Error(response?.data?.errMessage || '生成失败')
    }
  } catch (error) {
    console.error('生成图片失败:', error)
    ElMessage.error(error.message || '生成图片失败，请重试')
  } finally {
    isGenerating.value = false
  }
}

// 轮询任务状态
const pollTaskStatus = async (taskId) => {
  const maxAttempts = 60 // 最多轮询60次（5分钟）
  let attempts = 0
  
  const poll = async () => {
    try {
      const response = await getGenerationTaskStatus(taskId)
      
      if (response?.data?.success) {
        const result = response.data.data
        
        if (result.status === 'completed') {
          // 更新历史记录
          const index = generationHistory.value.findIndex(item => item.id === taskId)
          if (index !== -1) {
            generationHistory.value[index] = {
              ...generationHistory.value[index],
              status: 'completed',
              imageUrl: result.imageUrl
            }
          }
          return
        } else if (result.status === 'failed') {
          // 处理失败情况
          const index = generationHistory.value.findIndex(item => item.id === taskId)
          if (index !== -1) {
            generationHistory.value[index].status = 'failed'
          }
          ElMessage.error('图片生成失败')
          return
        }
      }
      
      // 继续轮询
      attempts++
      if (attempts < maxAttempts) {
        setTimeout(poll, 5000) // 5秒后再次轮询
      } else {
        ElMessage.warning('生成超时，请稍后查看历史记录')
      }
    } catch (error) {
      console.error('轮询任务状态失败:', error)
    }
  }
  
  // 开始轮询
  setTimeout(poll, 3000) // 3秒后开始第一次轮询
}

const refreshSuggestions = () => {
  // 刷新创意建议
  isLoadingSuggestions.value = true
  // 模拟API调用
  setTimeout(() => {
    suggestions.value = [
      { name: '科幻风格', prompt: '未来科技感的城市景观，霓虹灯闪烁' },
      { name: '自然风光', prompt: '宁静的山谷，清澈的溪流，阳光透过树叶' },
      { name: '卡通风格', prompt: '可爱的动物角色，色彩鲜艳，Q版造型' }
    ]
    isLoadingSuggestions.value = false
  }, 1000)
}

const handleImageClick = (imageUrl) => {
  if (window.openImagePreview) {
    window.openImagePreview(imageUrl, '')
  }
}

const toggleFavorite = async (item) => {
  try {
    await toggleFavoriteRecord(item.id, !item.isFavorite)
    item.isFavorite = !item.isFavorite
    ElMessage.success(item.isFavorite ? '已收藏' : '已取消收藏')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const deleteHistoryItem = async (itemId) => {
  try {
    await deleteGenerationRecord(itemId)
    const index = generationHistory.value.findIndex(item => item.id === itemId)
    if (index !== -1) {
      generationHistory.value.splice(index, 1)
    }
    ElMessage.success('已删除')
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

const formatTime = (date) => {
  return format(new Date(date), 'MM-dd HH:mm')
}

const loadGenerationHistory = async () => {
  try {
    const response = await getGenerationHistory({ type: 'video' })
    if (response?.data?.success) {
      generationHistory.value = response.data.data || []
    }
  } catch (error) {
    console.error('加载历史记录失败:', error)
  }
}

onMounted(() => {
  loadGenerationHistory()
  refreshSuggestions()
})
</script>

<style scoped>
.video-creation-module {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.module-header {
  text-align: center;
  margin-bottom: 32px;
}

.main-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 42px;
  font-weight: 700;
  margin: 0 0 12px 0;
}

.title-icon {
  font-size: 36px;
  color: #6366f1;
}

.gradient-text {
  background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: text-shimmer 4s ease-in-out infinite;
}

@keyframes text-shimmer {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.creation-mode-section,
.creative-input-section {
  min-height: calc(300px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  /* padding: 120px 0; */
  min-width: 800px;
  /* flex: 2; */
  /* 占比 2/3 */
}


/* 平板设备样式 */
@media (max-width: 1024px) {
  .creative-input-section {
    min-width: 100%;
  }
}

/* 移动设备样式 */
@media (max-width: 768px) {
  .creative-input-section {
    flex: none;
    width: 100%;
  }

}

.generation-history {
  width: 100%;
  /* background: white; */
  border-radius: 12px;
  /* border: 1px solid #e5e7eb; */
  overflow: hidden;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.history-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.history-count {
  font-size: 14px;
  color: #6b7280;
}

.history-content {
  padding: 20px;
}

.empty-history {
  text-align: center;
  padding: 40px 20px;
}

.empty-icon {
  font-size: 48px;
  color: #d1d5db;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #6b7280;
  margin: 0 0 8px 0;
}

.empty-hint {
  font-size: 14px;
  color: #9ca3af;
  margin: 0;
}

.history-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.history-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.history-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.item-video {
  aspect-ratio: 16/9;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.item-video img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.generating-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #6b7280;
}

.generating-icon {
  font-size: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.item-info {
  padding: 12px;
}

.item-prompt {
  font-size: 14px;
  color: #1f2937;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #6b7280;
}

.item-actions {
  padding: 8px 12px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 8px;
}

.delete-btn {
  color: #ef4444;
}

/* 暗色模式支持 */
body.dark .video-creation-module {
  color: var(--text-primary);
}

body.dark .subtitle {
  color: var(--text-secondary);
}

body.dark .setting-card,
body.dark .generation-history {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
}

body.dark .setting-title,
body.dark .history-title {
  color: var(--text-primary);
}

body.dark .history-header {
  background: var(--bg-quaternary);
  border-color: var(--border-color);
}

body.dark .history-item {
  border-color: var(--border-color);
}

body.dark .item-prompt {
  color: var(--text-primary);
}

body.dark .item-meta {
  color: var(--text-secondary);
}

body.dark .item-actions {
  border-color: var(--border-color);
}

/* 比例选择器样式 */
.ratio-selector {
  width: 100%;
}

.ratio-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.ratio-option {
  padding: 8px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  color: #374151;
  background: white;
}

.ratio-option:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.ratio-option.active {
  border-color: #3b82f6;
  background: #3b82f6;
  color: white;
}

body.dark .ratio-option {
  background: var(--bg-quaternary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

body.dark .ratio-option:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

body.dark .ratio-option.active {
  background: #3b82f6;
  color: white;
}
</style>
