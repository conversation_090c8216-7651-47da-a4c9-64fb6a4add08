<template>
  <div class="audio-creation-module">
      <!-- 主要内容区域 -->
      <div class="module-content">
        <!-- 上方：音频生成面板 -->
        <div class="generation-section">
          <AudioCreativeInput
            v-model="audioForm.text"
            :selectedVoiceId="selectedVoiceId"
            :voiceName="getVoiceName"
            :isCreating="isGenerating"
            :voices="voices"
            :isLoadingVoices="isLoadingVoices"
            :emotion="audioForm.emotion"
            :speed="audioForm.speed"
            :volume="audioForm.vol"
            :pitch="audioForm.pitch"
            :uploadedFiles="uploadedFiles"
            placeholder="请输入要转换为语音的文本内容..."
            @start-creation="generateAudio"
            @update:selectedVoiceId="handleVoiceSelect"
            @refresh-voices="refreshVoices"
            @update:emotion="(val) => audioForm.emotion = val"
            @update:speed="(val) => audioForm.speed = val"
            @update:volume="(val) => audioForm.vol = val"
            @update:pitch="(val) => audioForm.pitch = val"
            @update:uploadedFiles="(files) => uploadedFiles = files"
          />
        </div>

        <!-- 下方：音频列表 -->
        <div class="audio-list-section">
<!--          <div class="panel-header">-->
<!--            <h2 class="panel-title">已生成的音频</h2>-->
<!--          </div>-->

          <div class="audio-list-content">
            <div v-if="audioList.length > 0" class="audio-items">
              <div v-for="(audio, index) in audioList" :key="audio.id" class="audio-item">
                <div class="audio-info">
                  <div class="audio-header">
                    <span class="audio-name">音频-{{ index + 1 }}</span>
                    <span class="audio-duration">{{ formatDuration(audio.duration) }}</span>
                  </div>
                  <div class="audio-text">{{ audio.text }}</div>
                </div>
                <div class="audio-actions">
                  <button class="action-btn play-btn" @click="playAudio(audio)">
                    <el-icon>
                      <VideoPlay v-if="!isPlaying(audio.id)" />
                      <VideoPause v-else />
                    </el-icon>
                  </button>
                  <button class="action-btn download-btn" @click="downloadAudio(audio)">
                    <el-icon>
                      <Download />
                    </el-icon>
                  </button>
                  <button class="action-btn delete-btn" @click="deleteAudio(index)">
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </button>
                </div>
              </div>
            </div>
            <div v-else class="empty-list">
              <el-icon class="empty-icon">
                <Microphone />
              </el-icon>
              <p class="empty-text">暂无音频文件</p>
              <p class="empty-hint">开始创作你的第一个音频吧！</p>
            </div>
          </div>
        </div>
      </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Microphone, 
  ArrowDown, 
  VideoPlay, 
  VideoPause, 
  Download, 
  Delete 
} from '@element-plus/icons-vue'
import AudioCreativeInput from './AudioCreativeInput.vue'
import { createCanvasShotAudio, getVoiceList } from '@/api/auth.js'

const showVoiceSelector = ref(false)
const selectedVoiceId = ref(null)
const isGenerating = ref(false)
const isLoadingVoices = ref(false)
const voices = ref([])
const audioList = ref([])
const uploadedFiles = ref([])

// 当前播放状态
const currentAudio = ref(null)
const currentPlayingId = ref(null)

// 音频表单数据
const audioForm = reactive({
  text: '',
  emotion: undefined,
  speed: 1.0,
  vol: 1.0,
  pitch: 0
})

// 情感选项
const emotionLabels = {
  undefined: "无",
  "happy": "开心",
  "sad": "悲伤", 
  "angry": "愤怒",
  "fearful": "恐惧",
  "disgusted": "厌恶",
  "surprised": "惊讶",
  "neutral": "平静"
}

// 判断是否正在播放
const isPlaying = (audioId) => {
  return currentPlayingId.value === audioId && currentAudio.value && !currentAudio.value.paused
}

// 获取音色名称
const getVoiceName = computed(() => {
  if (selectedVoiceId.value && voices.value.length > 0) {
    const voice = voices.value.find(v => v.id == selectedVoiceId.value)
    return voice ? voice.name : '请选择音色'
  }
  return '请选择音色'
})

// 处理音色选择
const handleVoiceSelect = (voiceId) => {
  selectedVoiceId.value = voiceId
  showVoiceSelector.value = false
}

// 获取音色列表
const fetchVoices = async () => {
  try {
    isLoadingVoices.value = true;
    const response = await getVoiceList();
    if (response.success) {
      // 处理音色数据
      voices.value = response.data;
      console.log('获取音色列表成功:', voices.value.length);
    } else {
      console.error('获取音色列表失败:', response.data?.errMessage);
      ElMessage.error('获取音色列表失败，请稍后重试');
    }
  } catch (error) {
    console.error('获取音色列表异常:', error);
    ElMessage.error('获取音色列表失败，请稍后重试');
  } finally {
    isLoadingVoices.value = false;
  }
};

// 刷新音色列表（兼容现有调用）
const refreshVoices = () => {
  fetchVoices();
}

// 处理标签页切换
const handleTabChange = (tab) => {
  console.log(`切换到标签页: ${tab.name}`)
}

// 生成音频
const generateAudio = async () => {
  if (isGenerating.value) return
  
  if (!audioForm.text.trim()) {
    ElMessage.warning('请输入文本内容')
    return
  }
  
  if (!selectedVoiceId.value) {
    ElMessage.warning('请选择音色')
    return
  }

  try {
    isGenerating.value = true
    
    // 模拟API调用
    const mockAudio = {
      id: Date.now(),
      text: audioForm.text,
      voiceId: selectedVoiceId.value,
      duration: Math.floor(Math.random() * 30000) + 5000, // 5-35秒
      audioUrl: 'https://example.com/audio.mp3', // 模拟音频URL
      createdAt: new Date()
    }
    
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    audioList.value.unshift(mockAudio)
    
    // 重置表单
    audioForm.text = ''
    audioForm.emotion = undefined
    audioForm.speed = 1.0
    audioForm.vol = 1.0
    audioForm.pitch = 0
    
    ElMessage.success('音频生成成功')
  } catch (error) {
    console.error('生成音频失败:', error)
    ElMessage.error('生成音频失败，请稍后重试')
  } finally {
    isGenerating.value = false
  }
}

// 播放音频
const playAudio = (audio) => {
  if (isPlaying(audio.id)) {
    currentAudio.value.pause()
    currentAudio.value = null
    currentPlayingId.value = null
    return
  }

  if (currentAudio.value) {
    currentAudio.value.pause()
    currentAudio.value = null
    currentPlayingId.value = null
  }

  // 这里应该使用真实的音频URL
  // const audioElement = new Audio(audio.audioUrl)
  // audioElement.play()
  // currentAudio.value = audioElement
  // currentPlayingId.value = audio.id
  
  // 模拟播放
  currentPlayingId.value = audio.id
  setTimeout(() => {
    currentPlayingId.value = null
  }, 3000)
}

// 下载音频
const downloadAudio = (audio) => {
  ElMessage.info('下载功能开发中...')
}

// 删除音频
const deleteAudio = (index) => {
  audioList.value.splice(index, 1)
  ElMessage.success('音频已删除')
}

// 格式化时长
const formatDuration = (duration) => {
  if (!duration) return '00:00'
  
  const seconds = Math.floor(duration / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  
  return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`
}

// 组件挂载
onMounted(() => {
  // 获取音色列表
  fetchVoices()
})

// 组件卸载前清理
onBeforeUnmount(() => {
  if (currentAudio.value) {
    currentAudio.value.pause()
    currentAudio.value = null
  }
})
</script>

<style scoped>
.audio-creation-module {
  width: 100%;
  height:  100%;
}

.module-container {
  height:  100%;
}

body.dark .module-container {
  
}

.tab-content{
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* 标题区域 */
.module-header {
  text-align: center;
  padding: 40px 20px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.module-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 10px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.module-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 300;
}

/* 主要内容区域 */
.module-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  justify-content: center;
}

/* 生成区域 */
.generation-section {
  min-height: calc(300px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 800px;
}

body.dark .generation-section {

}



/* 音频列表区域 */
.audio-list-section {
  flex: 1;
  padding: 24px;
  min-height: 300px;
}

body.dark .audio-list-section {
  
}

.panel-header {
  margin-bottom: 20px;
}

.panel-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

body.dark .panel-title {
  color: #f1f5f9;
}

/* Tab 样式 */
.generation-tabs {
  
}

.generation-tabs :deep(.el-tabs__header) {
  margin: 0 0 0px 0;
}

.generation-tabs :deep(.el-tabs__item) {
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
}

.generation-tabs :deep(.el-tabs__item.is-active) {
  color: #667eea;
  font-weight: 600;
}

.generation-tabs :deep(.el-tabs__active-bar) {
  background: linear-gradient(90deg, #667eea, #764ba2);
  height: 3px;
}



/* 音频列表样式 */
.audio-list-content {
  max-height: 600px;
  overflow-y: auto;
}

.audio-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.audio-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.audio-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

body.dark .audio-item {
  background: #374151;
  border-color: #4b5563;
}

body.dark .audio-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.audio-info {
  flex: 1;
  min-width: 0;
}

.audio-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.audio-name {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

body.dark .audio-name {
  color: #f1f5f9;
}

.audio-duration {
  font-size: 12px;
  color: #64748b;
  background: rgba(100, 116, 139, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
}

body.dark .audio-duration {
  color: #94a3b8;
  background: rgba(148, 163, 184, 0.1);
}

.audio-text {
  font-size: 13px;
  color: #64748b;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

body.dark .audio-text {
  color: #94a3b8;
}

.audio-actions {
  display: flex;
  gap: 8px;
  margin-left: 16px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
}

.play-btn {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.play-btn:hover {
  background: rgba(34, 197, 94, 0.2);
  transform: scale(1.1);
}

.download-btn {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.download-btn:hover {
  background: rgba(59, 130, 246, 0.2);
  transform: scale(1.1);
}

.delete-btn {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.delete-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.1);
}

/* 空状态 */
.empty-list {
  text-align: center;
  padding: 60px 20px;
  color: #64748b;
}

body.dark .empty-list {
  color: #94a3b8;
}

.empty-icon {
  font-size: 48px;
  color: #cbd5e1;
  margin-bottom: 16px;
}

body.dark .empty-icon {
  color: #64748b;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.empty-hint {
  font-size: 14px;
  opacity: 0.7;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .module-title {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .audio-creation-module {
    padding: 10px;
  }

  .generation-section,
  .audio-list-section {
    padding: 16px;
  }

  .module-title {
    font-size: 1.8rem;
  }

  .audio-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .audio-actions {
    margin-left: 0;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .module-header {
    padding: 30px 15px 15px;
  }

  .generation-section,
  .audio-list-section {
    padding: 12px;
  }
}
</style>
