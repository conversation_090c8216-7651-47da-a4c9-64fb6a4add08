<template>
  <div class="creation-mode-selector">
    <div class="mode-header">
      <h3 class="mode-title">智能选择: {{ currentModeName }}</h3>
      <p class="mode-description">{{ currentModeDescription }}</p>
    </div>

    <div class="mode-options">
      <div 
        v-for="mode in availableModes" 
        :key="mode.id"
        class="mode-option"
        :class="{ 'active': selectedMode === mode.id, 'disabled': mode.disabled }"
        @click="selectMode(mode.id)"
      >
        <div class="mode-icon">
          <el-icon>
            <component :is="mode.icon" />
          </el-icon>
        </div>
        <div class="mode-content">
          <h4 class="mode-name">{{ mode.name }}</h4>
          <p class="mode-desc">{{ mode.description }}</p>
          <div class="mode-features">
            <span v-for="feature in mode.features" :key="feature" class="feature-tag">
              {{ feature }}
            </span>
          </div>
        </div>
        <div class="mode-meta">
          <span class="max-images">最大参考图片: {{ mode.maxImages }}张</span>
        </div>
      </div>
    </div>

    <div class="mode-tips" v-if="tips.length > 0">
      <h4 class="tips-title">使用技巧</h4>
      <ul class="tips-list">
        <li v-for="tip in tips" :key="tip">{{ tip }}</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Document, Picture, PictureFilled, Connection } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: 'text-to-image'
  },
  uploadedImagesCount: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['update:modelValue', 'mode-change'])

const selectedMode = ref(props.modelValue)

// 可用模式配置
const availableModes = computed(() => [
  {
    id: 'text-to-image',
    name: 'FLUX Kontext [pro-text]',
    description: '专业级文本到图像生成，纯文本创作',
    icon: Document,
    maxImages: 4,
    features: ['纯文本生成', '专业质量', '创意自由'],
    disabled: false
  },
  {
    id: 'image-edit',
    name: 'FLUX Edit [pro-edit]',
    description: '基于参考图片的智能编辑和变换',
    icon: Picture,
    maxImages: 1,
    features: ['图片编辑', '风格转换', '细节优化'],
    disabled: props.uploadedImagesCount === 0
  },
  {
    id: 'multi-fusion',
    name: 'FLUX Fusion [pro-fusion]',
    description: '多图融合创作，组合多个元素',
    icon: Connection,
    maxImages: 4,
    features: ['多图融合', '元素组合', '创意合成'],
    disabled: props.uploadedImagesCount < 2
  }
])

// 当前模式信息
const currentMode = computed(() => {
  return availableModes.value.find(mode => mode.id === selectedMode.value)
})

const currentModeName = computed(() => currentMode.value?.name || '')
const currentModeDescription = computed(() => currentMode.value?.description || '')

// 使用技巧
const tips = computed(() => {
  const baseTips = [
    '系统会根据上传图片数量智能选择模型',
    '无图片：纯文本创作模式',
    '单图片：图片编辑模式',
    '多图片：多图融合模式',
    '点击"高级"可手动选择其他模型',
    '详细描述能获得更好的生成效果'
  ]
  
  if (selectedMode.value === 'text-to-image') {
    return [...baseTips, '支持中英文提示词，建议使用具体的描述']
  } else if (selectedMode.value === 'image-edit') {
    return [...baseTips, '上传高质量参考图片能获得更好效果', '支持风格转换和局部编辑']
  } else if (selectedMode.value === 'multi-fusion') {
    return [...baseTips, '建议上传风格相近的图片', '可以调整各图片的融合权重']
  }
  
  return baseTips
})

// 选择模式
const selectMode = (modeId) => {
  const mode = availableModes.value.find(m => m.id === modeId)
  if (mode && !mode.disabled) {
    selectedMode.value = modeId
    emit('update:modelValue', modeId)
    emit('mode-change', mode)
  }
}

// 监听上传图片数量变化，自动切换模式
watch(() => props.uploadedImagesCount, (newCount) => {
  if (newCount === 0) {
    selectMode('text-to-image')
  } else if (newCount === 1) {
    selectMode('image-edit')
  } else if (newCount >= 2) {
    selectMode('multi-fusion')
  }
}, { immediate: true })

// 监听外部模式变化
watch(() => props.modelValue, (newValue) => {
  selectedMode.value = newValue
})
</script>

<style scoped>
.creation-mode-selector {
  padding: 20px;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.mode-header {
  margin-bottom: 20px;
  text-align: center;
}

.mode-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.mode-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.mode-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.mode-option {
  display: flex;
  align-items: center;
  padding: 16px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mode-option:hover:not(.disabled) {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.mode-option.active {
  border-color: #3b82f6;
  background: #eff6ff;
}

.mode-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f9fafb;
}

.mode-icon {
  margin-right: 16px;
  color: #3b82f6;
}

.mode-icon .el-icon {
  font-size: 24px;
}

.mode-content {
  flex: 1;
}

.mode-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.mode-desc {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 8px 0;
}

.mode-features {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.feature-tag {
  padding: 2px 8px;
  background: #dbeafe;
  color: #1e40af;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.mode-meta {
  margin-left: 16px;
  text-align: right;
}

.max-images {
  font-size: 12px;
  color: #6b7280;
}

.mode-tips {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.tips-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.tips-list {
  margin: 0;
  padding-left: 20px;
}

.tips-list li {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 4px;
  line-height: 1.4;
}

.tips-list li:last-child {
  margin-bottom: 0;
}

/* 暗色模式支持 */
body.dark .creation-mode-selector {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

body.dark .mode-title {
  color: var(--text-primary);
}

body.dark .mode-description {
  color: var(--text-secondary);
}

body.dark .mode-option {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
}

body.dark .mode-option.active {
  background: rgba(59, 130, 246, 0.1);
}

body.dark .mode-option.disabled {
  background: var(--bg-quaternary);
}

body.dark .mode-name {
  color: var(--text-primary);
}

body.dark .mode-desc {
  color: var(--text-secondary);
}

body.dark .max-images {
  color: var(--text-secondary);
}

body.dark .mode-tips {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
}

body.dark .tips-title {
  color: var(--text-primary);
}

body.dark .tips-list li {
  color: var(--text-secondary);
}
</style>
