<template>
  <div class="creation-type-selector">
    <DropdownPanel 
      v-model="showSelector" 
      position="bottom" 
      align="center" 
      closeOnClickOutside 
      :zIndex="1200" 
      :maxHeight="300" 
      dropdown-id="creation-type-selector"
    >
      <template #trigger>
        <div class="setting-item" :class="{ 'setting-changed': typeChanged }">
          <el-icon>
            <component :is="currentTypeIcon" />
          </el-icon>
          <span class="setting-text">{{ currentTypeName }}</span>
        </div>
      </template>
      
      <div class="dropdown-content-wrapper">
        <div class="creation-types">
          <!-- <div class="types-header">
            <h3>选择创作类型</h3>
          </div> -->
          
          <div class="types-list">
            <div 
              v-for="type in creationTypes" 
              :key="type.id"
              class="type-item"
              :class="{ 'active': type.id === currentType, 'disabled': type.disabled }"
              @click="selectType(type)"
            >
              <!-- <div class="type-icon">
                <el-icon>
                  <component :is="type.icon" />
                </el-icon>
              </div> -->
              <div class="type-info">
                <div class="type-name">{{ type.name }}</div>
                <div class="type-description">{{ type.description }}</div>
              </div>
              <div class="type-status" v-if="type.id === currentType">
                <el-icon class="check-icon">
                  <Check />
                </el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DropdownPanel>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Picture, VideoCamera, Microphone, Check, Document } from '@element-plus/icons-vue'
import DropdownPanel from '../parent/DropdownPanel.vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const route = useRoute()

const props = defineProps({
  currentType: {
    type: String,
    default: 'story' // 'image', 'video', 'audio'
  }
})

const emit = defineEmits(['type-change'])

// 显示状态
const showSelector = ref(false)
const typeChanged = ref(false)

// 创作类型配置
const creationTypes = [
  {
    id: 'story',
    name: '故事生成',
    description: '文本生成故事、故事编辑、故事融合',
    icon: Document,
    route: '/inputSection',
    disabled: false
  },
  {
    id: 'image',
    name: '图片生成',
    description: '文本生成图片、图片编辑、多图融合',
    icon: Picture,
    route: '/image-studio',
    disabled: true
  },
  {
    id: 'video', 
    name: '视频生成',
    description: '文本生成视频、图片转视频',
    icon: VideoCamera,
    route: '/video-studio',
    disabled: true
  },
  {
    id: 'audio',
    name: '音频生成', 
    description: '文本转语音、音频生成',
    icon: Microphone,
    route: '/audio-studio',
    disabled: true
  }
]

// 当前类型信息
const currentTypeInfo = computed(() => {
  return creationTypes.find(type => type.id === props.currentType) || creationTypes[0]
})

const currentTypeName = computed(() => currentTypeInfo.value.name)
const currentTypeIcon = computed(() => currentTypeInfo.value.icon)

// 选择类型
const selectType = (type) => {

  // 图片、视频、音频暂未开放
  if (type.disabled) {
    ElMessage.warning('功能暂未开放，敬请期待')
    showSelector.value = false
    return
  }

  if (type.id !== props.currentType) {
    // 触发动效
    typeChanged.value = true
    setTimeout(() => {
      typeChanged.value = false
    }, 2000)
    
    // 发出事件
    emit('type-change', type)
    
    // 路由跳转
    if (type.route && route.path !== type.route) {
      router.push(type.route)
    }
  }
  
  showSelector.value = false
}

// 监听当前类型变化
watch(() => props.currentType, (newType, oldType) => {
  if (oldType && newType !== oldType) {
    typeChanged.value = true
    setTimeout(() => {
      typeChanged.value = false
    }, 2000)
  }
})
</script>

<style scoped>
.creation-type-selector {
  display: inline-block;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  border-radius: 8px;
  font-size: 12px;
  background-color: rgba(64, 158, 255, 0.1);
  color: #409eff;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(64, 158, 255, 0.2);
  cursor: pointer;
  position: relative;
}

/* .setting-item::after {
  content: '';
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgba(64, 158, 255, 0.5);
  opacity: 0;
  transition: opacity 0.3s;
} */

.setting-item:hover {
  background-color: rgba(64, 157, 250, 0.255);
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.273);
}

.setting-item:hover::after {
  opacity: 1;
}

body.dark .setting-item {
  background-color: rgba(64, 158, 255, 0.15);
  color: #67a9ff;
  border-color: rgba(64, 158, 255, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

body.dark .setting-item:hover {
  background-color: rgba(64, 158, 255, 0.25);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.setting-text {
  max-width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.setting-item .el-icon {
  font-size: 14px;
}
@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.setting-text {
  font-weight: 500;
  white-space: nowrap;
}

.dropdown-content-wrapper {
  padding: 0;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(64, 158, 255, 0.15);
  overflow: hidden;
  transition: all 0.3s;
}

.creation-types {
  width: 100%;
}

.types-header {
  padding: 16px 20px 12px;
  border-bottom: 1px solid #f3f4f6;
  background: #f9fafb;
}

.types-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.types-list {
  padding: 8px 0;
}

.type-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.type-item:hover {
  background: #f9fafb;
}

.type-item.active {
  background: linear-gradient(135deg, #eff6ff 0%, #f0f9ff 100%);
}

.type-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.type-icon {
  /* width: 40px;
  height: 40px; */
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); */
  color: #6b7280;
  transition: all 0.3s ease;
  font-size: 20px;
}

.type-item.active .type-icon {
  /* background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); */
  /* color: white; */
}

.type-info {
  flex: 1;
}

.type-name {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 2px;
}

.type-description {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.type-status {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  color: #10b981;
  font-size: 16px;
}

/* 暗色模式 */
body.dark .setting-item {
  background: rgba(31, 41, 55, 0.9);
  border-color: #374151;
  color: #e5e7eb;
}

body.dark .setting-item:hover {
  background: rgba(31, 41, 55, 1);
  border-color: #4b5563;
}

body.dark .dropdown-content-wrapper {
  background: rgba(64, 158, 255, 0.08);
  border-color: rgba(64, 158, 255, 0.25);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

body.dark .types-header {
  background: #111827;
  border-color: #374151;
}

body.dark .types-header h3 {
  color: #f9fafb;
}

body.dark .type-item:hover {
  background: #374151;
}

body.dark .type-item.active {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  border-left-color: #60a5fa;
}

body.dark .type-icon {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  color: #9ca3af;
}

body.dark .type-item.active .type-icon {
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
  color: white;
}

body.dark .type-name {
  color: #f9fafb;
}

body.dark .type-description {
  color: #9ca3af;
}
</style>
