<template>
  <div class="creative-input">
    <div class="section-header">
      <h2 class="title-with-icon">
        <el-icon class="title-icon">
          <Microphone />
        </el-icon>
        <span class="gradient-text">AI 音频工坊</span>
      </h2>
    </div>

    <div class="input-wrapper">

      <!-- 文本输入 -->
      <el-input :model-value="modelValue" @update:model-value="updateValue" type="textarea"
        :autosize="{ minRows: 2, maxRows: 12 }" maxlength="10000" resize="none" :placeholder="placeholder"
        class="typewriter-input">
      </el-input>

      <div class="bt_content">
        <!-- 添加已选设置信息区域 -->
        <div class="selected-settings">

          <!-- 创作类型选择器 -->
          <CreationTypeSelector
            currentType="audio"
            @type-change="handleTypeChange"
          />

          <!-- 音色选择 DropdownPanel -->
          <DropdownPanel v-model="showVoiceSelector" position="bottom" align="center" closeOnClickOutside :width="900"
            :zIndex="1200" :maxHeight="420" dropdown-id="audio-voice-selector">
            <template #trigger>
              <div class="setting-item" :class="{ 'setting-changed': voiceChanged }">
                <el-icon>
                  <Microphone />
                </el-icon>
                <span class="setting-text">{{ getVoiceName }}</span>
              </div>
            </template>
            <div class="dropdown-content-wrapper">
              <VoiceSelector :voices="voices" :selectedVoice="selectedVoiceId ? Number(selectedVoiceId) : null" :isLoading="isLoadingVoices"
                @update:selectedVoice="handleVoiceSelect" @refresh-voices="refreshVoices" />
            </div>
          </DropdownPanel>

          <!-- 情感设置 DropdownPanel -->
          <DropdownPanel v-model="showEmotionSelector" position="bottom" align="center" closeOnClickOutside :width="80"
            :zIndex="1200" :maxHeight="300">
            <template #trigger>
              <div class="setting-item" :class="{ 'setting-changed': emotionChanged }">
                <el-icon>
                  <Operation />
                </el-icon>
                <span class="setting-text">情感 {{ getEmotionLabel }}</span>
              </div>
            </template>
            <div class="dropdown-content-wrapper">
              <div class="params-content">
                <!-- <div class="param-item">
                </div> -->
                <label class="param-label" @click="handleEmotionChange(undefined)">无</label>
                <label class="param-label" @click="handleEmotionChange('happy')">开心</label>
                <label class="param-label" @click="handleEmotionChange('sad')">悲伤</label>
                <label class="param-label" @click="handleEmotionChange('angry')">愤怒</label>
                <label class="param-label" @click="handleEmotionChange('fearful')">恐惧</label>
                <label class="param-label" @click="handleEmotionChange('disgusted')">厌恶</label>
                <label class="param-label" @click="handleEmotionChange('surprised')">惊讶</label>
                <label class="param-label" @click="handleEmotionChange('neutral')">平静</label>
              </div>
            </div>
          </DropdownPanel>

          <!-- 语速设置 DropdownPanel -->
          <DropdownPanel v-model="showSpeedSelector" position="bottom" align="center" closeOnClickOutside :width="220"
            :zIndex="1200" :maxHeight="200">
            <template #trigger>
              <div class="setting-item" :class="{ 'setting-changed': speedChanged }">
                <el-icon>
                  <Operation />
                </el-icon>
                <span class="setting-text">语速 {{ speed.toFixed(1) }}</span>
              </div>
            </template>
            <div class="dropdown-content-wrapper">
              <div class="params-content">
                <div class="param-item">
                  <label class="param-label">语速</label>
                  <el-slider :modelValue="speed" @update:modelValue="handleSpeedChange" :min="0.5" :max="2.0"
                    :step="0.1" :format-tooltip="(val) => `${val.toFixed(1)}`" />
                </div>
              </div>
            </div>
          </DropdownPanel>

          <!-- 音量设置 DropdownPanel -->
          <DropdownPanel v-model="showVolumeSelector" position="bottom" align="center" closeOnClickOutside :width="220"
            :zIndex="1200" :maxHeight="200">
            <template #trigger>
              <div class="setting-item" :class="{ 'setting-changed': volumeChanged }">
                <el-icon>
                  <Operation />
                </el-icon>
                <span class="setting-text">音量 {{ volume.toFixed(1) }}</span>
              </div>
            </template>
            <div class="dropdown-content-wrapper">
              <div class="params-content">
                <div class="param-item">
                  <label class="param-label">音量</label>
                  <el-slider :modelValue="volume" @update:modelValue="handleVolumeChange" :min="0.1" :max="10.0"
                    :step="0.1" :format-tooltip="(val) => `${val.toFixed(1)}`" />
                </div>
              </div>
            </div>
          </DropdownPanel>

          <!-- 语调设置 DropdownPanel -->
          <DropdownPanel v-model="showPitchSelector" position="bottom" align="center" closeOnClickOutside :width="220"
            :zIndex="1200" :maxHeight="200">
            <template #trigger>
              <div class="setting-item" :class="{ 'setting-changed': pitchChanged }">
                <el-icon>
                  <Operation />
                </el-icon>
                <span class="setting-text">语调 {{ pitch > 0 ? '+' : '' }}{{ pitch }}</span>
              </div>
            </template>
            <div class="dropdown-content-wrapper">
              <div class="params-content">
                <div class="param-item">
                  <label class="param-label">语调</label>
                  <el-slider :modelValue="pitch" @update:modelValue="handlePitchChange" :min="-12" :max="12" :step="1"
                    :format-tooltip="(val) => `${val}`" />
                </div>
              </div>
            </div>
          </DropdownPanel>
        </div>

        <el-button type="primary" :disabled="!isFormValid" @click="handleStartCreation" class="send-button"
          :loading="props.isCreating" round>
          <el-icon class="send-icon">
            <Position />
          </el-icon>
        </el-button>

      </div>
    </div>

  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Microphone, Position, Operation } from '@element-plus/icons-vue'
import DropdownPanel from '../parent/DropdownPanel.vue'
import VoiceSelector from '../selector/VoiceSelector.vue'
import CreationTypeSelector from './CreationTypeSelector.vue'

const router = useRouter()

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  selectedVoiceId: {
    type: String,
    default: null
  },
  voiceName: {
    type: String,
    default: ''
  },
  isCreating: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: "请输入要转换为语音的文本内容..."
  },
  conversationId: {
    type: String,
    default: ''
  },
  // 音频参数相关 props
  emotion: {
    type: String,
    default: undefined
  },
  speed: {
    type: Number,
    default: 1.0
  },
  volume: {
    type: Number,
    default: 1.0
  },
  pitch: {
    type: Number,
    default: 0
  },
  // 音色相关 props
  voices: {
    type: Array,
    default: () => []
  },
  isLoadingVoices: {
    type: Boolean,
    default: false
  }
})

// 跟踪上一次的设置值，用于检测变化
const prevSelectedVoiceId = ref(props.selectedVoiceId)

// 设置项动效状态
const voiceChanged = ref(false)
const emotionChanged = ref(false)
const speedChanged = ref(false)
const volumeChanged = ref(false)
const pitchChanged = ref(false)

// DropdownPanel 显示状态
const showVoiceSelector = ref(false)
const showEmotionSelector = ref(false)
const showSpeedSelector = ref(false)
const showVolumeSelector = ref(false)
const showPitchSelector = ref(false)

// 监听设置项变化
watch(() => props.selectedVoiceId, (newVal, oldVal) => {
  if (oldVal !== null && newVal !== oldVal) {
    voiceChanged.value = true
    setTimeout(() => {
      voiceChanged.value = false
    }, 2000) // 2秒后移除动效
  }
  prevSelectedVoiceId.value = newVal
}, { immediate: true })

// 监听参数变化
watch(() => props.emotion, (newVal, oldVal) => {
  if (oldVal !== undefined && newVal !== oldVal) {
    emotionChanged.value = true
    setTimeout(() => {
      emotionChanged.value = false
    }, 2000)
  }
}, { immediate: true })

watch(() => props.speed, (newVal, oldVal) => {
  if (oldVal !== undefined && newVal !== oldVal) {
    speedChanged.value = true
    setTimeout(() => {
      speedChanged.value = false
    }, 2000)
  }
}, { immediate: true })

watch(() => props.volume, (newVal, oldVal) => {
  if (oldVal !== undefined && newVal !== oldVal) {
    volumeChanged.value = true
    setTimeout(() => {
      volumeChanged.value = false
    }, 2000)
  }
}, { immediate: true })

watch(() => props.pitch, (newVal, oldVal) => {
  if (oldVal !== undefined && newVal !== oldVal) {
    pitchChanged.value = true
    setTimeout(() => {
      pitchChanged.value = false
    }, 2000)
  }
}, { immediate: true })

const emit = defineEmits([
  'update:modelValue',
  'start-creation',
  // 音频参数相关事件
  'update:emotion',
  'update:speed',
  'update:volume',
  'update:pitch',
  'update:selectedVoiceId',
  'refresh-voices',
  'type-change'
])





const updateValue = (value) => {
  emit('update:modelValue', value)
}

const isFormValid = computed(() => {
  return props.modelValue.trim().length > 0
})

// 添加音色名称计算属性
const getVoiceName = computed(() => {
  if (props.selectedVoiceId && props.voices.length > 0) {
    const voice = props.voices.find(v => v.id == props.selectedVoiceId)
    return voice ? voice.name : '请选择音色'
  }
  return props.voiceName || '请选择音色'
})

// 添加情感标签计算属性
const getEmotionLabel = computed(() => {
  const emotionLabels = {
    undefined: "无",
    "happy": "开心",
    "sad": "悲伤",
    "angry": "愤怒",
    "fearful": "恐惧",
    "disgusted": "厌恶",
    "surprised": "惊讶",
    "neutral": "平静"
  }
  return emotionLabels[props.emotion] || '情感'
})

// 判断是否有选中的设置
const hasSelectedSettings = computed(() => {
  return props.selectedVoiceId || props.emotion || props.speed !== 1.0 || props.volume !== 1.0 || props.pitch !== 0
})

const handleStartCreation = () => {
  if (isFormValid.value) {
    emit('start-creation')
  }
}

// 处理音色选择
const handleVoiceSelect = (voiceId) => {
  emit('update:selectedVoiceId', voiceId)
  showVoiceSelector.value = false
}

// 刷新音色列表
const refreshVoices = () => {
  emit('refresh-voices')
}

// 处理参数变化
const handleEmotionChange = (emotion) => {
  emit('update:emotion', emotion)
  showEmotionSelector.value = false
}

const handleSpeedChange = (speed) => {
  emit('update:speed', speed)
}

const handleVolumeChange = (volume) => {
  emit('update:volume', volume)
}

const handlePitchChange = (pitch) => {
  emit('update:pitch', pitch)
}

// 处理创作类型变化
const handleTypeChange = (type) => {
  emit('type-change', type)
}
</script>

<style scoped>
.creative-input {
  margin-top: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-height: 100%;
  padding: 0 20px;
  box-sizing: border-box;
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 16px;
  border-radius: 22px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03);
  background-color: #f9fafcc7;
  gap: 16px;
}

.bt_content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

.upload-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.custom-upload-btn {
  background-color: #ffffff;
  border: 1px solid #40a0ff46;
  border-radius: 50%;
  color: #40a0ff78;
  width: 30px;
  height: 30px;
  padding: 0;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.15);
}

.custom-upload-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.25);
  background-color: #f0f9ff;
}

.custom-upload-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.15);
}

.custom-upload-btn .el-icon {
  font-size: 16px;
}

body.dark .custom-upload-btn {
  background-color: #1a1a1a;
  border-color: #409eff;
  color: #67a9ff;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

body.dark .custom-upload-btn:hover {
  background-color: #2a2a2a;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.send-button {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  gap: 6px;
  transition: all 0.3s;
}

.send-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.send-icon {
  font-size: 16px;
}

body.dark .send-button {
  background-color: var(--color-primary, #409eff);
}

.input-wrapper:hover {
  border-color: #409eff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

body.dark .input-wrapper {
  border-color: var(--border-color);
  background-color: var(--bg-secondary);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

body.dark .input-wrapper:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.section-header {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 24px;
  width: 100%;
}

.section-header h2 {
  font-size: 42px;
  font-weight: 700;
  color: #303133;
  margin: 0;
  position: relative;
  padding-bottom: 10px;
  transition: color 0.3s;
  letter-spacing: -0.5px;
}

.title-with-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  position: relative;
}

.title-icon {
  font-size: 28px;
  color: #6366f1;
  animation: icon-glow 2s ease-in-out infinite alternate;
}

.gradient-text {
  background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  display: inline-block;
  animation: text-shimmer 4s ease-in-out infinite;
}

body.dark .gradient-text {
  background: linear-gradient(135deg, #818cf8, #a78bfa, #f472b6);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
}

.title-with-icon::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
  border-radius: 3px;
  animation: line-expand 0.6s ease-out forwards;
}

@keyframes icon-glow {
  0% {
    text-shadow: 0 0 5px rgba(99, 102, 241, 0.3);
    transform: scale(1);
  }

  100% {
    text-shadow: 0 0 15px rgba(99, 102, 241, 0.6);
    transform: scale(1.1);
  }
}

@keyframes text-shimmer {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

@keyframes line-expand {
  0% {
    width: 0;
    opacity: 0;
  }

  100% {
    width: 80px;
    opacity: 1;
  }
}

body.dark .section-header h2 {
  color: var(--text-primary);
}

body.dark .title-icon {
  color: #818cf8;
}

body.dark .title-with-icon::after {
  background: linear-gradient(90deg, #818cf8, #a78bfa, #f472b6);
}

:deep(.el-textarea__inner) {
  border: 1px solid transparent;
  padding: 8px;
  font-size: 16px;
  background-color: transparent;
  transition: all 0.3s;
  box-shadow: none;
  border-color: transparent;
}

:deep(.el-input__count) {
  bottom: 12px;
  right: 14px;
}

body.dark :deep(.el-textarea__inner) {
  /* border-color: transparent;
  background-color: transparent;
  box-shadow: none; */
  /* color: var(--text-primary); */
}

:deep(.el-textarea__inner:focus) {
  border-color: transparent;
  box-shadow: none;
  color: var(--text-primary);
}

body.dark :deep(.el-textarea__inner:focus) {
  border-color: transparent;
  box-shadow: none;
}

:deep(.el-textarea__word-count) {
  color: #909399;
  font-size: 14px;
  padding: 8px 12px;
  transition: color 0.3s;
}

body.dark :deep(.el-textarea__word-count) {
  color: var(--text-secondary);
}

/* 已选设置样式 */
.selected-settings {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  flex: 1;
  margin-right: 10px;
  justify-content: flex-start;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  border-radius: 8px;
  font-size: 12px;
  background-color: rgba(64, 158, 255, 0.1);
  color: #409eff;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(64, 158, 255, 0.2);
  cursor: pointer;
  position: relative;
}

.setting-item:hover {
  background-color: rgba(64, 157, 250, 0.255);
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.273);
}

body.dark .setting-item {
  background-color: rgba(64, 158, 255, 0.15);
  color: #67a9ff;
  border-color: rgba(64, 158, 255, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

body.dark .setting-item:hover {
  background-color: rgba(64, 158, 255, 0.25);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.setting-text {
  max-width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.setting-item .el-icon {
  font-size: 14px;
}

/* 设置项变化动效 */
@keyframes setting-highlight {
  0% {
    transform: scale(1);
  }

  25% {
    transform: scale(1.1);
  }

  50% {
    transform: scale(1);
    background-color: rgba(64, 158, 255, 0.3);
  }

  75% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

.setting-changed {
  animation: setting-highlight 0.8s ease-in-out;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
  background-color: rgba(64, 158, 255, 0.2);
}

body.dark .setting-changed {
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.7);
  background-color: rgba(64, 158, 255, 0.3);
}

/* multiple-file-container 样式 */
.multiple-file-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  padding: 8px 8px 0px 8px;
  width: 100%;
  box-sizing: border-box;
}

.multiple-file-item {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
  background-color: #ffffff;
  position: relative;
}

.multiple-file-item.is-uploading {
  border-color: #409eff;
}

.multiple-file-item.is-error {
  border-color: #f56c6c;
}

body.dark .multiple-file-item {
  border-color: var(--border-color);
  background-color: var(--bg-tertiary);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.multiple-file-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #c0c4cc;
}

.multiple-file-item:hover .multiple-file-item-info {
  opacity: 1;
}

body.dark .multiple-file-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: var(--border-color-light, #4c4c4c);
}

.multiple-file-item-preview {
  height: 60px;
  width: 60px;
  position: relative;
  overflow: hidden;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

body.dark .multiple-file-item-preview {
  background-color: var(--bg-quaternary, #2d3748);
}

.file-preview-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

body.dark .file-preview-icon {
  background-color: rgba(64, 158, 255, 0.2);
}

.file-preview-icon .el-icon {
  font-size: 20px;
}

.file-status-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  transition: background-color 0.3s;
}

body.dark .file-status-overlay {
  background-color: rgba(45, 55, 72, 0.8);
}

.error-icon {
  font-size: 24px;
  color: #f56c6c;
}

.multiple-file-item-info {
  width: 100%;
  position: absolute;
  bottom: 0px;
  left: 0px;
  padding: 4px 0px;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgb(0, 0, 0));
  opacity: 0;
}

.multiple-file-item-size {
  font-size: 10px;
  color: #ffffff;
  transition: color 0.3s;
}

body.dark .multiple-file-item-size {
  color: var(--text-tertiary);
}

.multiple-file-item-actions {
  position: absolute;
  top: 0px;
  right: 0px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.multiple-file-item:hover .multiple-file-item-actions {
  opacity: 1;
}

/* DropdownPanel 样式 */
.dropdown-content-wrapper {
  padding: 8px;
}

.params-content {
  display: flex;
  flex-direction: column;
}

.param-item {
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: center;
}

.param-label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  width: 60px;
  text-align: center;
  padding: 6px 0px;
  border-radius: 8px;
}

.param-label:hover {
  color: #409eff;
  background-color: #409eff10;
}

body.dark .param-label {
  color: var(--text-primary);
}

body.dark .param-label:hover {
  color: #409eff;
  background-color: #409eff10;
}

/* 自定义滚动条样式 */
:deep(.el-textarea__inner::-webkit-scrollbar) {
  width: 0px;
}

:deep(.el-textarea__inner::-webkit-scrollbar-thumb) {
  background-color: #6365f139;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark :deep(.el-textarea__inner::-webkit-scrollbar-thumb) {
  background-color: rgba(99, 102, 241, 0.2);
}

:deep(.el-textarea__inner::-webkit-scrollbar-track) {
  background-color: #f1f5f9;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark :deep(.el-textarea__inner::-webkit-scrollbar-track) {
  background-color: var(--bg-tertiary);
}

/* 打字机效果样式 */
.typewriter-input :deep(.el-textarea__inner) {
  caret-color: #409eff;
}

body.dark :deep(.el-textarea__inner:focus) {
  border-color: transparent;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-header h2 {
    font-size: 24px;
  }

  .selected-settings {
    gap: 6px;
  }

  .setting-item {
    padding: 1px 6px;
    font-size: 11px;
  }

  .setting-text {
    max-width: 80px;
  }

  .multiple-file-container {
    gap: 8px;
  }

  .multiple-file-item-preview {
    height: 70px;
  }
}

@media (max-width: 480px) {
  .section-header h2 {
    font-size: 20px;
  }

  .selected-settings {
    gap: 4px;
  }

  .setting-item {
    padding: 1px 4px;
    font-size: 10px;
  }

  .setting-text {
    max-width: 60px;
  }

  .multiple-file-container {
    gap: 6px;
  }

  .multiple-file-item-preview {
    height: 60px;
  }
}
</style>
