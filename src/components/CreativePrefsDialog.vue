<template>
  <div v-if="dialogVisible" class="custom-dialog-overlay" @click.self="handleCancel">
    <div class="custom-dialog" @click.stop>
      <div class="dialog-header">
        <h3 class="dialog-title">个性化创作喜好</h3>
        <button class="close-button" @click="handleCancel">×</button>
      </div>
      
      <div class="dialog-content">
        <p class="dialog-description">
          配置您的创作喜好，帮助我们为您提供更精准的创作服务
        </p>
        
        <div class="steps-container" :class="{'is-loading': loading}">
          <!-- 步骤进度条 -->
          <div class="steps-progress">
            <div class="steps-indicator">
              <div 
                v-for="(step, index) in steps" 
                :key="index"
                class="step-dot"
                :class="{ 'active': currentStep >= index, 'current': currentStep === index }"
                @click="goToStep(index)"
              >
                <span class="step-number">{{ index + 1 }}</span>
                <span class="step-label">{{ step.label }}</span>
              </div>
              <div class="progress-line">
                <div class="progress-line-inner" :style="{ width: progressWidth }"></div>
              </div>
            </div>
          </div>
          
          <!-- 步骤内容区域 -->
          <div class="steps-content-wrapper">
            <div class="steps-content">
              <transition name="fade" mode="out-in">
                <div v-if="currentStep === 0" class="step-panel" key="age">
                  <h4 class="step-title">请选择喜好年龄段</h4>
                  <div class="step-content">
                    <div class="options-grid age-grid">
                      <div 
                        v-for="item in ageOptions" 
                        :key="item.value" 
                        class="option-card"
                        :class="{ 'selected': creativePrefs.age === item.value }"
                        @click="selectOption('age', item.value)"
                      >
                        <span class="option-text">{{ item.label }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div v-else-if="currentStep === 1" class="step-panel" key="gender">
                  <h4 class="step-title">请选择喜好性别</h4>
                  <div class="step-content">
                    <div class="options-grid gender-grid">
                      <div 
                        v-for="item in genderOptions" 
                        :key="item.value" 
                        class="option-card gender-card"
                        :class="{ 'selected': creativePrefs.gender === item.value }"
                        @click="selectOption('gender', item.value)"
                      >
                        <div class="gender-icon">
                          <div v-if="item.value === '男性'" class="icon-male"></div>
                          <div v-else-if="item.value === '女性'" class="icon-female"></div>
                          <div v-else class="icon-both"></div>
                        </div>
                        <div class="option-label">{{ item.label }}</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div v-else-if="currentStep === 2" class="step-panel" key="job">
                  <h4 class="step-title">请选择喜好职业</h4>
                  <div class="step-content">
                    <div class="options-grid job-grid">
                      <div 
                        v-for="item in jobOptions" 
                        :key="item.value" 
                        class="option-card job-card"
                        :class="{ 'selected': creativePrefs.job === item.value }"
                        @click="selectOption('job', item.value)"
                      >
                        <div class="option-label">{{ item.label }}</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div v-else-if="currentStep === 3" class="step-panel" key="region">
                  <h4 class="step-title">请输入地区</h4>
                  <div class="step-content">
                    <div class="custom-input-container">
                      <div class="input-icon-wrapper">
                        <div class="location-icon"></div>
                      </div>
                      <input
                        type="text"
                        v-model="creativePrefs.region"
                        placeholder="请输入地区"
                        class="custom-input"
                      />
                    </div>
                    <p class="input-hint">例如：北京市、上海市、广州市等</p>
                  </div>
                </div>
              </transition>
            </div>
          </div>
          
          <!-- 步骤导航按钮 -->
          <div class="steps-navigation">
            <button 
              v-if="currentStep > 0" 
              class="nav-button prev-button" 
              @click="prevStep"
            >
              <span class="button-icon">←</span>
              <span class="button-text">上一步</span>
            </button>
            
            <div class="spacer"></div>
            
            <button 
              v-if="currentStep < steps.length - 1" 
              class="nav-button next-button" 
              @click="nextStep"
              :disabled="!canProceed"
            >
              <span class="button-text">下一步</span>
              <span class="button-icon">→</span>
            </button>
            
            <button 
              v-if="currentStep === steps.length - 1" 
              class="nav-button save-button" 
              @click="handleSave"
              :disabled="!canSave"
              :class="{ 'loading': saving }"
            >
              <span v-if="!saving" class="button-text">保存</span>
              <div v-else class="loading-spinner"></div>
            </button>
          </div>
          
          <!-- 加载遮罩 -->
          <div v-if="loading" class="loading-overlay">
            <div class="loading-spinner"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Location } from '@element-plus/icons-vue'
import { updateUserProfile, getUserProfile } from '@/api/auth'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  userData: {
    type: Object,
    default: () => ({
      age: '',
      gender: '',
      job: '',
      region: ''
    })
  }
})

const emit = defineEmits(['update:visible', 'saved'])

// 对话框可见状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 加载状态
const loading = ref(false)
const saving = ref(false)

// 创作喜好相关状态
const creativePrefs = ref({
  age: '',
  gender: '',
  job: '',
  region: ''
})

// 步骤管理
const steps = [
  { id: 'age', label: '年龄段', required: true },
  { id: 'gender', label: '性别', required: true },
  { id: 'job', label: '职业', required: true },
  { id: 'region', label: '地区', required: true }
]
const currentStep = ref(0)

// 计算进度条宽度
const progressWidth = computed(() => {
  const totalSteps = steps.length - 1
  const currentProgress = currentStep.value
  return `${(currentProgress / totalSteps) * 100}%`
})

// 控制导航按钮状态
const canProceed = computed(() => {
  const currentStepData = steps[currentStep.value]
  if (currentStepData.required) {
    return !!creativePrefs.value[currentStepData.id]
  }
  return true
})

const canSave = computed(() => {
  // 检查所有必填项是否已填写
  return steps.filter(step => step.required)
    .every(step => !!creativePrefs.value[step.id])
})

// 步骤导航方法
const nextStep = () => {
  if (currentStep.value < steps.length - 1 && canProceed.value) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const goToStep = (stepIndex) => {
  // 只允许导航到已完成步骤或当前步骤的下一步
  if (stepIndex <= currentStep.value + 1 && stepIndex >= 0 && stepIndex < steps.length) {
    // 如果是向前导航，需要确认当前步骤已完成
    if (stepIndex > currentStep.value && !canProceed.value) {
      return
    }
    currentStep.value = stepIndex
  }
}

// 选项选择方法
const selectOption = (field, value) => {
  creativePrefs.value[field] = value
  
  // 如果是最后一步，不自动前进
  if (field !== 'region' && canProceed.value) {
    // 添加短暂延迟，让用户看到选择效果
    setTimeout(() => {
      nextStep()
    }, 300)
  }
}

// 创作喜好选项
const ageOptions = [
  { value: '5-11岁', label: '5-11岁' },
  { value: '12-17岁', label: '12-17岁' },
  { value: '18-24岁', label: '18-24岁' },
  { value: '25-35岁', label: '25-35岁' },
  { value: '35-50岁', label: '35-50岁' },
  { value: '50岁以上', label: '50岁以上' }
]

const genderOptions = [
  { value: '男性', label: '男性' },
  { value: '女性', label: '女性' },
  { value: '男女皆可', label: '男女皆可' }
]

const jobOptions = [
  { value: '都市白领', label: '都市白领' },
  { value: '互联网人', label: '互联网人' },
  { value: '电竞玩家', label: '电竞玩家' },
  { value: '国潮青年', label: '国潮青年' },
  { value: '网文读者', label: '网文读者' },
  { value: '内容创作者', label: '内容创作者' },
  { value: '学生', label: '学生' },
  { value: '斜杠青年', label: '斜杠青年' },
  { value: '小镇青年', label: '小镇青年' },
  { value: '备考人', label: '备考人' },
]

// 获取用户资料
const fetchUserProfile = async () => {
  loading.value = true
  try {
    const response = await getUserProfile()
    if (response.data) {
      // 更新创作喜好表单数据
      creativePrefs.value = {
        age: response.data.age || '',
        gender: response.data.gender || '',
        job: response.data.job || '',
        region: response.data.region || ''
      }
      
      // 如果有已保存的数据，尝试跳转到第一个未完成的步骤
      const firstIncompleteStep = steps.findIndex(step => !creativePrefs.value[step.id])
      if (firstIncompleteStep !== -1) {
        currentStep.value = firstIncompleteStep
      }
    }
  } catch (error) {
    console.error('获取用户资料失败:', error)
    ElMessage.error('获取用户资料失败，将使用缓存数据')
    // 如果API请求失败，回退到使用props中的数据
    creativePrefs.value = {
      age: props.userData.age || '',
      gender: props.userData.gender || '',
      job: props.userData.job || '',
      region: props.userData.region || ''
    }
  } finally {
    loading.value = false
  }
}

// 监听对话框打开，初始化表单数据
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 重置当前步骤
    currentStep.value = 0
    // 直接从API获取最新的用户信息
    fetchUserProfile()
  }
})

// 取消按钮
const handleCancel = () => {
  dialogVisible.value = false
}

// 保存创作喜好
const handleSave = async () => {
  if (!canSave.value) {
    ElMessage.warning('请完成所有必填项')
    return
  }
  
  saving.value = true
  try {
    // 更新用户资料
    await updateUserProfile({
      age: creativePrefs.value.age,
      gender: creativePrefs.value.gender,
      job: creativePrefs.value.job,
      region: creativePrefs.value.region
    })

    // 显示保存成功动画
    showSaveSuccess()
    
    // 延迟关闭对话框，让用户看到成功动画
    setTimeout(() => {
      // 关闭对话框
      dialogVisible.value = false
      
      // 通知父组件保存成功，并传递更新后的数据
      emit('saved', { ...creativePrefs.value })
    }, 1000)
    
  } catch (error) {
    console.error('更新创作喜好失败:', error)
    ElMessage.error('更新创作喜好失败，请重试')
    saving.value = false
  }
}

// 显示保存成功动画
const showSaveSuccess = () => {
  // 这里可以添加成功动画的逻辑
  // 例如在保存按钮上显示成功图标等
  ElMessage.success('创作喜好已更新')
}
</script>

<style scoped>
/* 对话框基础样式 */
.custom-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

.custom-dialog {
  width: 550px;
  max-width: 90vw;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1), 0 4px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  animation: scaleIn 0.3s ease;
}

/* 对话框头部 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 对话框内容区域 */
.dialog-content {
  padding: 24px;
}

.dialog-description {
  font-size: 14px;
  color: #606266;
  margin-top: 0;
  margin-bottom: 28px;
  text-align: center;
}

/* 步骤容器 */
.steps-container {
  position: relative;
}

/* 步骤进度条 */
.steps-progress {
  margin-bottom: 32px;
}

.steps-indicator {
  display: flex;
  justify-content: space-between;
  position: relative;
  padding: 0 12px;
}

.step-dot {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1;
  position: relative;
}

.step-dot.active {
  background-color: #8b5cf6;
}

.step-dot.current {
  transform: scale(1.15);
  box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.2);
}

.step-number {
  font-size: 14px;
  font-weight: 600;
  color: #909399;
  transition: all 0.3s ease;
}

.step-dot.active .step-number {
  color: white;
}

.step-label {
  position: absolute;
  top: 100%;
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.step-dot.active .step-label {
  color: #8b5cf6;
  font-weight: 500;
}

.progress-line {
  position: absolute;
  height: 3px;
  background-color: #f0f0f0;
  top: 50%;
  left: 30px;
  right: 30px;
  transform: translateY(-50%);
  z-index: 0;
}

.progress-line-inner {
  height: 100%;
  background-color: #8b5cf6;
  transition: width 0.3s ease;
}

/* 步骤内容 */
.steps-content-wrapper {
  position: relative;
  min-height: 300px;
  margin-bottom: 24px;
}

.steps-content {
  position: relative;
}

.step-panel {
  width: 100%;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.step-title {
  font-size: 18px;
  font-weight: 600;
  margin-top: 10px;
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

/* 选项网格 */
.options-grid {
  display: grid;
  gap: 16px;
}

.age-grid {
  grid-template-columns: repeat(3, 1fr);
}

.gender-grid {
  grid-template-columns: repeat(3, 1fr);
}

.job-grid {
  grid-template-columns: repeat(3, 1fr);
  max-height: 320px;
  overflow-y: auto;
  padding-top: 20px;
}


/* 选项卡片 */
.option-card {
  padding: 16px;
  border-radius: 12px;
  background-color: #f5f5f5;
  border: 2px solid transparent;
  cursor: pointer;
  text-align: center;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  position: relative;
  overflow: hidden;
}

.job-card{
  height: 20px;
}

.option-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.option-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.option-card:hover::before {
  opacity: 1;
}

.option-card.selected {
  border-color: #6366f1;
  background-color: rgba(99, 102, 241, 0.05);
  transform: translateY(-4px);
  box-shadow: 0 6px 12px rgba(99, 102, 241, 0.2);
}

.option-card.selected::after {
  content: '✓';
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #6366f1;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.option-text {
  position: relative;
  z-index: 1;
  font-weight: 500;
}

/* 性别卡片特殊样式 */
.gender-card {
  flex-direction: column;
  height: 80px;
}

.gender-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.icon-male {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #409eff;
  position: relative;
}

.icon-male::after {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  border-left: 3px solid #409eff;
  border-bottom: 3px solid #409eff;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%) rotate(-45deg);
}

.icon-female {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #ff69b4;
  position: relative;
}

.icon-female::after {
  content: '';
  position: absolute;
  width: 3px;
  height: 10px;
  background-color: #ff69b4;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
}

.icon-both {
  position: relative;
  width: 32px;
  height: 24px;
}

.icon-both::before,
.icon-both::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.icon-both::before {
  background-color: #409eff;
  left: 0;
}

.icon-both::after {
  background-color: #ff69b4;
  right: 0;
}

.option-label {
  position: relative;
  z-index: 1;
  font-weight: 500;
}

/* 自定义输入框 */
.custom-input-container {
  position: relative;
  width: 100%;
  margin-bottom: 16px;
  box-sizing: border-box;
  overflow: hidden;
}

.input-icon-wrapper {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-icon {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #6366f1;
  position: relative;
}

.location-icon::after {
  content: '';
  position: absolute;
  width: 2px;
  height: 6px;
  background-color: #6366f1;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.custom-input {
  width: 100%;
  padding: 16px 16px 16px 48px;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.custom-input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.2);
}

.input-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  margin-left: 16px;
}

/* 步骤导航按钮 */
.steps-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.spacer {
  flex-grow: 1;
}

.nav-button {
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.button-icon {
  font-size: 18px;
  line-height: 1;
}

.prev-button {
  background-color: #f5f5f5;
  border: none;
  color: #606266;
}

.prev-button:hover {
  background-color: #e0e0e0;
}

.next-button {
  background-color: #6366f1;
  border: none;
  color: white;
}

.next-button:hover {
  background-color: #5a5ccf;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
}

.next-button:disabled {
  background-color: #c0c0c0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.save-button {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  color: white;
  padding: 12px 24px;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
}

.save-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(99, 102, 241, 0.3);
  background: linear-gradient(135deg, #5a5ccf, #7b4fd9);
}

.save-button:disabled {
  background: linear-gradient(135deg, #c0c0c0, #d0d0d0);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 加载动画 */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  backdrop-filter: blur(2px);
}

.loading-overlay .loading-spinner {
  width: 32px;
  height: 32px;
  border-width: 4px;
  border-color: rgba(99, 102, 241, 0.3);
  border-top-color: #6366f1;
}

/* 动画效果 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 步骤切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* 自定义滚动条 */
.job-grid::-webkit-scrollbar {
  width: 6px;
}

.job-grid::-webkit-scrollbar-track {
  background-color: #f0f0f0;
  border-radius: 3px;
}

.job-grid::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 3px;
}

.job-grid::-webkit-scrollbar-thumb:hover {
  background-color: #a0a0a0;
}

/* 暗黑模式适配 */
body.dark .custom-dialog {
  background-color: #1a1a1a;
}

body.dark .dialog-description {
  color: #a0a0a0;
}

body.dark .step-title {
  color: #e0e0e0;
}

body.dark .step-dot {
  background-color: #2a2a2a;
}

body.dark .progress-line {
  background-color: #2a2a2a;
}

body.dark .step-number {
  color: #a0a0a0;
}

body.dark .step-label {
  color: #a0a0a0;
}

body.dark .option-card {
  background-color: #2a2a2a;
}

body.dark .option-card:hover {
  background-color: #333333;
}

body.dark .option-card.selected {
  background-color: rgba(99, 102, 241, 0.2);
}

body.dark .custom-input {
  background-color: #2a2a2a;
  border-color: #444;
  color: #e0e0e0;
}

body.dark .input-hint {
  color: #a0a0a0;
}

body.dark .steps-navigation {
  border-top-color: #2a2a2a;
}

body.dark .prev-button {
  background-color: #2a2a2a;
  color: #e0e0e0;
}

body.dark .prev-button:hover {
  background-color: #333333;
}

body.dark .job-grid::-webkit-scrollbar-track {
  background-color: #2a2a2a;
}

body.dark .job-grid::-webkit-scrollbar-thumb {
  background-color: #444;
}

body.dark .job-grid::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}

body.dark .loading-overlay {
  background-color: rgba(26, 26, 26, 0.7);
}
</style> 