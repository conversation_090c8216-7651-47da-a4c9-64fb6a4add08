<template>
  <div class="actors-selection">
    <div class="section-header">
      <h3>添加演员</h3>
      <el-button link class="view-all">查看全部角色</el-button>
    </div>

    <div class="actors-grid">
      <div v-for="(actor, index) in actors" :key="index" class="actor-item"
        :class="{ active: selectedActors.includes(actor.id) }" @click="toggleActor(actor.id)">
        <div class="actor-avatar" :style="{ backgroundImage: `url(${actor.avatar || ''})` }">
          <div class="actor-icon" v-if="!actor.avatar">{{ actor.shortName }}</div>
          <div class="actor-select-indicator" v-if="selectedActors.includes(actor.id)">
            <i class="el-icon-check"></i>
          </div>
        </div>
        <div class="actor-info">
          <div class="actor-name">{{ actor.name }}</div>
          <!-- <div class="actor-role">{{ actor.role }}</div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  actors: {
    type: Array,
    required: true
  },
  selectedActors: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:selectedActors'])

const toggleActor = (actorId) => {
  const selected = [...props.selectedActors]
  const index = selected.indexOf(actorId)
  
  if (index > -1) {
    selected.splice(index, 1)
  } else {
    selected.push(actorId)
  }
  
  emit('update:selectedActors', selected)
}
</script>

<style scoped>
.actors-selection {
  margin-bottom: 24px;
  width: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3 {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  transition: color 0.3s;
}

body.dark .section-header h3 {
  color: var(--text-primary);
}

.actors-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  margin-bottom: 30px;
  overflow-x: auto;
  padding-bottom: 5px;
  scrollbar-width: thin;
}

.actors-grid::-webkit-scrollbar {
  height: 6px;
}

.actors-grid::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
  border-radius: 6px;
}

.actor-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

body.dark .actor-item {
  background-color: var(--bg-card);
  box-shadow: 0 2px 12px var(--shadow-light);
}

.actor-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

body.dark .actor-item:hover {
  box-shadow: 0 4px 16px var(--shadow-color);
}

.actor-item.active {
  border: 2px solid #409eff;
  transform: translateY(-10px);
}

.actor-avatar {
  height: 140px;
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  transition: background-color 0.3s;
}

body.dark .actor-avatar {
  background-color: var(--bg-tertiary);
}

.actor-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #e6f1ff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

body.dark .actor-icon {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.actor-select-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.actor-info {
  padding: 12px;
}

.actor-name {
  font-weight: 500;
  color: #303133;
  font-size: 15px;
  text-align: left;
  transition: color 0.3s;
}

body.dark .actor-name {
  color: var(--text-primary);
}

.actor-role {
  color: #909399;
  font-size: 13px;
  margin-top: 4px;
  transition: color 0.3s;
}

body.dark .actor-role {
  color: var(--text-secondary);
}

@media (max-width: 1400px) {
  .actors-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

@media (max-width: 1200px) {
  .actors-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 868px) {
  .actors-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 668px) {
  .actors-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .actors-grid {
    grid-template-columns: repeat(1, 1fr);
  }
}
</style> 