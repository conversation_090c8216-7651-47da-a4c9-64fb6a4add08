<template>
  <div class="video-works-grid">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="videos-grid skeleton-grid">
        <div class="skeleton-card" v-for="i in columnCount * 3" :key="i">
          <div class="skeleton-image"></div>
          <div class="skeleton-content">
            <div class="skeleton-header">
              <div class="skeleton-title"></div>
            </div>
            <div class="skeleton-desc" style="width: 70%"></div>
          </div>
        </div>
      </div>
      <div class="loading-text">
        <el-icon class="loading-icon">
          <Loading />
        </el-icon>
        <span>{{ loadingText }}</span>
      </div>
    </div>

    <!-- 空状态 -->
    <!-- <div v-else-if="filteredVideos.length === 0" class="empty-state final-empty-state"> -->
      <!-- <el-icon class="empty-icon">
        <VideoPlay />
      </el-icon> -->
      <!-- <div class="empty-text">{{ emptyText }}</div> -->
    <!-- </div> -->

    <!-- 视频列表 - 瀑布流布局 -->
    <div v-else class="waterfall-container">
      <vue-grid-waterfall :data-list="filteredVideos" :columns="waterfallColumns" @getMoreData="handleLoadMore"
        :loading="loadingMore" :bottom="20" class="videos-waterfall">
        <template #slot-scope="{ slotProps }">
          <el-card class="video-card waterfall-card" shadow="hover" @click="handleVideoClick(slotProps.data)">
            <div class="video-card-content">
              <!-- 操作按钮区域 -->
              <div class="video-actions" v-if="showActions">
                <el-tooltip content="做同款" placement="top">
                  <el-button link @click.stop="handleMakeSimilar(slotProps.data)" class="make-similar-btn">
                    做同款
                  </el-button>
                </el-tooltip>
              </div>

              <!-- 视频封面区域 -->
              <div class="video-image-container" :style="{ aspectRatio: slotProps.data.aspectRatio }">
                <img v-if="getCoverImage(slotProps.data)"
                  :src="getCoverImage(slotProps.data) + '?x-oss-process=image/resize,w_280'" alt="作品封面"
                  @error="handleImageError($event, slotProps.data)" @load="handleImageLoaded(slotProps.data)"
                  class="video-image" />

                <div class="no-image-placeholder" v-if="!getCoverImage(slotProps.data) || slotProps.data.imageError">
                  <el-icon>
                    <VideoPlay />
                  </el-icon>
                </div>

                <!-- 播放按钮覆盖层 -->
                <div class="play-overlay">
                  <el-icon class="play-icon">
                    <VideoPlay />
                  </el-icon>
                </div>

                <!-- 视频时长显示 -->
                <div class="video-duration" v-if="slotProps.data.videoDuration && showDuration">
                  {{ formatDuration(slotProps.data.videoDuration) }}
                </div>
              </div>

              <!-- 信息区域 -->
              <div class="video-info">
                <!-- 创作者信息 -->
                <div class="creator-info">
                  <div class="creator-avatar" v-if="showCreator">
                    <img v-if="slotProps.data.userAvatar"
                      :src="slotProps.data.userAvatar + '?x-oss-process=image/resize,w_32'"
                      :alt="slotProps.data.userNickname" @error="handleAvatarError" />
                    <el-icon v-else class="default-avatar">
                      <User />
                    </el-icon>
                  </div>
                  <div class="video-header">
                    <h3 class="video-title">{{ getVideoTitle(slotProps.data) }}</h3>
                  </div>
                </div>

                <div class="video-meta" v-if="showMeta">
                  <span class="video-date">{{ formatDate(slotProps.data.shareTime) }}</span>
                  <span v-if="slotProps.data.ratio" class="video-resolution">{{ slotProps.data.ratio }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </template>
      </vue-grid-waterfall>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { VideoPlay, Loading, User } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getVideoRenderSharedList } from '@/api/auth.js'
import VueGridWaterfall from './VueGridWaterfall.vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// Props 定义
const props = defineProps({
  // 数据源配置
  apiFunction: {
    type: Function,
    default: getVideoRenderSharedList
  },
  apiParams: {
    type: Object,
    default: () => ({})
  },
  // 显示配置
  showActions: {
    type: Boolean,
    default: true
  },
  showCreator: {
    type: Boolean,
    default: true
  },
  showDuration: {
    type: Boolean,
    default: true
  },
  showMeta: {
    type: Boolean,
    default: true
  },
  // 文本配置
  loadingText: {
    type: String,
    default: '加载视频列表...'
  },
  emptyText: {
    type: String,
    default: '暂无视频作品'
  },
  // 布局配置
  pageSize: {
    type: Number,
    default: 30
  },
  // 自定义标题生成函数
  titleGenerator: {
    type: Function,
    default: null
  }
})

// Emits 定义
const emit = defineEmits(['video-click', 'share-video', 'load-more', 'make-similar'])

// 响应式数据
const loading = ref(true)
const loadingMore = ref(false)
const videos = ref([])
const hasMore = ref(false)
const columnCount = ref(12)
const waterfallColumns = ref(3)
const pageIndex = ref(1)

// 计算属性
const filteredVideos = computed(() => {
  return videos.value
})

// 处理图片加载错误
const handleImageError = (_, video) => {
  if (video) {
    video.imageError = true
    video.imageLoading = false
  }
}

// 处理图片加载完成
const handleImageLoaded = (video) => {
  if (video) {
    video.imageLoading = false
  }
}

// 获取封面图片
const getCoverImage = (video) => {
  return video.canvasCoverImage || video.firstFrameUrl
}

// 处理头像错误
const handleAvatarError = (event) => {
  event.target.style.display = 'none'
}

// 获取视频标题
const getVideoTitle = (video) => {
  if (props.titleGenerator) {
    return props.titleGenerator(video)
  }
  return video.canvasName || `视频作品 #${video.id}`
}

// 加载视频列表
const loadVideos = async (isLoadMore = false) => {
  try {
    if (!isLoadMore) {
      loading.value = true
    } else {
      loadingMore.value = true
    }

    const response = await props.apiFunction({
      pageIndex: pageIndex.value,
      pageSize: props.pageSize,
      ...props.apiParams
    })

    if (response && response.success && response.data) {
      if (response.data.length > 0) {
        // 转换数据格式以适配瀑布流组件
        const newVideos = response.data.map((item) => {
          // 处理视频比例，根据ratio字段或默认为16:9
          const ratio = item.ratio || '16:9'
          const [width, height] = ratio.split(':').map(Number)
          const aspectRatio = width / height

          // 根据比例计算瀑布流中的高度（基础宽度为220px）
          const baseWidth = 220
          const imageHeight = baseWidth / aspectRatio
          // 添加额外的内容高度（标题、日期、状态标签、内边距等）
          const contentHeight = 112
          const calculatedHeight = imageHeight + contentHeight

          return {
            ...item,
            imageLoading: true, // 添加图片加载状态
            imageError: false, // 添加图片错误状态
            aspectRatio: aspectRatio, // 计算后的宽高比
            height: calculatedHeight + 'px' // 瀑布流需要的高度
          }
        })

        // 如果是加载更多，则追加数据，否则替换数据
        if (isLoadMore) {
          videos.value = [...videos.value, ...newVideos]
        } else {
          videos.value = newVideos
        }

        // 判断是否还有更多数据
        hasMore.value = response.data.length === props.pageSize
      } else if (!isLoadMore) {
        videos.value = []
        hasMore.value = false
      } else {
        hasMore.value = false
      }
    } else {
      if (!isLoadMore) {
        videos.value = []
      }
      hasMore.value = false
    }
  } catch (error) {
    console.error('加载视频列表失败:', error)
    ElMessage.error('加载视频列表失败')
    if (!isLoadMore) {
      videos.value = []
    }
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 加载更多视频
const handleLoadMore = async () => {
  if (loading.value || loadingMore.value || !hasMore.value) return

  pageIndex.value++
  await loadVideos(true)
  emit('load-more', { pageIndex: pageIndex.value, hasMore: hasMore.value })
}

// 根据窗口宽度更新骨架屏数量和瀑布流列数
const updateSkeletonCount = () => {
  const width = window.innerWidth
  if (width < 520) {
    columnCount.value = 6
    waterfallColumns.value = 1
  } else if (width < 720) {
    columnCount.value = 8
    waterfallColumns.value = 2
  } else if (width < 980) {
    columnCount.value = 9
    waterfallColumns.value = 3
  } else if (width < 1280) {
    columnCount.value = 12
    waterfallColumns.value = 4
  } else if (width < 1600) {
    columnCount.value = 15
    waterfallColumns.value = 5
  } else if (width < 2000) {
    columnCount.value = 18
    waterfallColumns.value = 6
  } else {
    columnCount.value = 21
    waterfallColumns.value = 7
  }
}

// 格式化时长（毫秒转为 mm:ss 格式）
const formatDuration = (duration) => {
  if (!duration) return '00:00'
  const seconds = Math.floor(duration / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()

  // 获取今天的开始时间（00:00:00）
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  // 获取昨天的开始时间（00:00:00）
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)

  // 检查是否是今天
  if (date >= today) {
    return `今天 ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
  }

  // 检查是否是昨天
  if (date >= yesterday && date < today) {
    return `昨天 ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
  }

  // 其他日期显示完整日期
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 处理视频点击
const handleVideoClick = (video) => {
  if (video.shareCode) {
    let routeData = router.resolve({
      path: '/videoShare',
      query: { shareCode: video.shareCode }
    });
    window.open(routeData.href, '_blank');
  } else if (video.videoUrl) {
    window.open(video.videoUrl, '_blank')
  } else if (video.shareUrl) {
    window.open(video.shareUrl, '_blank')
  } else {
    ElMessage.warning('视频暂不可用')
  }
  emit('video-click', video)
}

// 处理分享视频
const handleShareVideo = (video) => {
  if (video.shareUrl) {
    navigator.clipboard.writeText(video.shareUrl).then(() => {
      ElMessage.success('分享链接已复制到剪贴板')
    }).catch(() => {
      ElMessage.error('复制失败，请手动复制链接')
    })
  } else {
    ElMessage.warning('该视频暂无分享链接')
  }
  emit('share-video', video)
}

// 处理做同款
const handleMakeSimilar = (video) => {
  emit('make-similar', video)
}

// 重新加载数据的方法（供外部调用）
const reload = () => {
  pageIndex.value = 1
  videos.value = []
  loadVideos()
}

// 监听 apiParams 变化，重新加载数据
watch(() => props.apiParams, () => {
  reload()
}, { deep: true })

// 组件挂载时加载数据
onMounted(() => {
  loadVideos()
  updateSkeletonCount()
  window.addEventListener('resize', updateSkeletonCount)
})

// 暴露方法给父组件
defineExpose({
  reload,
  loadMore: handleLoadMore
})
</script>

<style scoped>
.video-works-grid {
  width: 100%;
}

.loading-state {
  margin: 40px 0;
  min-height: 400px;
}

.loading-text {
  text-align: center;
  color: #64748b;
  margin-top: 30px;
  font-size: 14px;
  transition: color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-text .loading-icon {
  animation: rotate 1.5s linear infinite;
  color: #6366f1;
  font-size: 20px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 0;
}

/* 特殊空状态样式 */
.final-empty-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
  height: calc(100vh - 160px);
  justify-content: center;
  text-align: center;
  border-radius: 12px;
  z-index: 1;
}

.final-empty-state .empty-icon {
  font-size: 64px;
  color: #6365f1ca;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: floating 3s ease-in-out infinite;
}

.final-empty-state .empty-text {
  font-size: 22px;
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 10px;
}

body.dark .final-empty-state .empty-text {
  color: #e5e7eb;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0);
    scale: 1;
  }
  50% {
    transform: translateY(-15px);
    scale: 1.05;
  }
}

.waterfall-container {
  width: 100%;
  margin-bottom: 20px;
}

.videos-waterfall {
  width: 100%;
}

.videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 4px;
  margin-bottom: 20px;
  width: 100%;
}

.video-card {
  cursor: pointer;
  transition: transform 0.3s ease, background-color 0.3s, box-shadow 0.3s;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  --el-card-padding: 0px;
  overflow: hidden;
  background-color: --white;
  border-radius: 8px;
  box-shadow: 0 4px 14px #7b7dfc52;
  animation: card-appear 0.5s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.waterfall-card {
  margin-bottom: 4px;
  opacity: 1;
  transform: translateY(0);
  animation: none;
  height: auto !important;
  min-height: auto;
}

@keyframes card-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.video-card:hover {
  transform: scale(1.03);
  z-index: 2;
  box-shadow: 0 8px 20px rgba(123, 125, 252, 0.4);
}

.video-card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.video-image-container {
  position: relative;
  overflow: hidden;
  background-color: #f1f5f9;
  transition: background-color 0.3s;
  margin: 0;
}

.video-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;
  transition: 0.8s all;
}

.video-image:hover {
  object-position: left top;
}

.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-card:hover .play-overlay {
  opacity: 1;
}

.play-icon {
  color: white;
  font-size: 24px;
}

.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.video-info {
  flex: 1;
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0;
  gap: 4px;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
}

.video-title {
  font-size: 13px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  text-align: left;
  flex: 1;
  min-width: 0;
  transition: color 0.3s;
}

body.dark .video-title {
  color: #ffffffd8;
}

/* 创作者信息 */
.creator-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.creator-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.creator-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-avatar {
  font-size: 12px;
  color: #64748b;
}

.creator-name {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  gap: 8px;
}

.video-date,
.video-resolution {
  font-size: 11px;
  color: #9eafc6;
  text-align: left;
  transition: color 0.3s;
  line-height: 1.3;
}

.video-resolution {
  background: #f1f5f9;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
}
body.dark .video-resolution {
  background: #273449;
  color: #e5e7eb;
}

.video-actions {
  display: flex;
  gap: 0px;
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease;
  /* background-color: rgba(255, 255, 255, 0.8); */
  border-radius: 4px;
  padding: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.video-card:hover .video-actions {
  opacity: 1;
}

body.dark .video-actions {
  background-color: rgba(30, 41, 59, 0.8);
}

.video-actions .el-button {
  color: #64748b;
  padding: 4px 8px;
  transition: color 0.3s, background-color 0.3s;
}

.video-actions .el-button:hover {
  color: #3b82f6;
  background-color: #f1f5f9;
}

body.dark .video-actions .el-button:hover {
  background-color: var(--bg-hover);
}

.video-actions .el-icon {
  font-size: 16px;
}

/* 做同款按钮样式 */
.make-similar-btn {
  background: linear-gradient(135deg, #6366f1, #8b5cf6) !important;
  color: white !important;
  border-radius: 6px !important;
  padding: 6px 12px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3) !important;
  transition: all 0.3s ease !important;
}

.make-similar-btn:hover {
  background: linear-gradient(135deg, #5855eb, #7c3aed) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4) !important;
}

body.dark .make-similar-btn {
  background: linear-gradient(135deg, #6366f1, #8b5cf6) !important;
  color: white !important;
}

body.dark .make-similar-btn:hover {
  background: linear-gradient(135deg, #5855eb, #7c3aed) !important;
}

/* 图片占位符样式 */
.no-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #64748b8d;
  font-size: 16px;
  gap: 12px;
}

body.dark .no-image-placeholder {
  background-color: var(--bg-tertiary);
  color: var(--text-tertiary);
}

.no-image-placeholder .el-icon {
  font-size: 36px;
  opacity: 0.2;
}

/* 旋转动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 骨架屏样式 */
.skeleton-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  width: 100%;
}

.skeleton-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  animation: skeleton-pulse 1.5s infinite ease-in-out;
}

body.dark .skeleton-card {
  background: rgba(30, 41, 59, 0.8);
}

@keyframes skeleton-pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.skeleton-image {
  width: 100%;
  height: 130px;
  background: linear-gradient(110deg, #eff1f5 30%, #e4e8f0 50%, #eff1f5 70%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite ease-in-out;
}

body.dark .skeleton-image {
  background: linear-gradient(110deg, #1e293b 30%, #273449 50%, #1e293b 70%);
  background-size: 200% 100%;
}

.skeleton-content {
  padding: 15px;
}

.skeleton-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.skeleton-title {
  height: 24px;
  width: 70%;
  background: linear-gradient(110deg, #eff1f5 30%, #e4e8f0 50%, #eff1f5 70%);
  background-size: 200% 100%;
  border-radius: 4px;
  animation: skeleton-loading 1.5s infinite ease-in-out;
}

.skeleton-desc {
  height: 16px;
  width: 100%;
  margin-bottom: 8px;
  background: linear-gradient(110deg, #eff1f5 30%, #e4e8f0 50%, #eff1f5 70%);
  background-size: 200% 100%;
  border-radius: 4px;
  animation: skeleton-loading 1.5s infinite ease-in-out;
}

body.dark .skeleton-title,
body.dark .skeleton-desc {
  background: linear-gradient(110deg, #1e293b 30%, #273449 50%, #1e293b 70%);
  background-size: 200% 100%;
}

@keyframes skeleton-loading {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .video-works-grid {
    padding: 10px;
  }

  .video-card:hover {
    transform: scale(1.02);
  }
}
</style>
