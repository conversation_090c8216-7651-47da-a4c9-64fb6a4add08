<template>
   <!-- :class="[`ratio-${aspectRatio.replace(':', '-')}`]" -->
  <div class="story-player" ref="storyPlayerRef">
    <div class="player-container">
      <!-- 幻灯片展示区 -->
      <div class="slide-display" :class="{ 'playing': isPlaying }" @mousemove="showControls">
        <!-- 封面显示 -->
        <div v-if="!isPlaying && isShowingCover" class="cover-display" @click="startStory">
          <img :src="coverImage" alt="封面" class="cover-image" />
          <div class="cover-content">
            <h1 class="cover-title">{{ coverTitle }}</h1>
            <p class="cover-subtitle2">{{ coverSubtitle2 }}</p>
            <p class="cover-subtitle">{{ coverSubtitle }}</p>
          </div>
        </div>

        <!-- 生成视频按钮 -->
        <!-- <div class="generate-video-btn" v-if="showGenerateVideo && !isShowingCover">
          <el-button type="primary" class="generate-button video-button" @click="$emit('generate-video')">
            <el-icon><video-camera /></el-icon>
            生成视频
          </el-button>
        </div> -->

        <!-- 我要创作 -->
        <div class="generate-home-btn" v-if="showGenerateHome">
          <el-button type="primary" class="generate-button home-button" @click="$emit('generate-home')">
            我要创作
          </el-button>
        </div>

        <!-- 分享按钮 -->
        <!-- <div class="generate-video-share" v-if="showGenerateHome">
          <el-button type="primary" class="generate-button share-button" @click="$emit('generate-share')">
            <el-icon>
              <Share />
            </el-icon>
            分享链接
          </el-button>
        </div> -->

        <!-- 历史记录 -->
        <div class="generate-video-share" v-if="showGenerateShare && !isShowingCover">
          <el-button type="primary" class="generate-button share-button" @click="$emit('generate-share')">
            <el-icon>
              <Clock />
            </el-icon>
            历史记录
          </el-button>
        </div>

        <!-- 使用v-for循环显示所有分镜图片 -->
        <div class="slide-image" v-if="!isShowingCover">
          <template v-for="(storyboard, index) in props.storyboards?.shots" :key="'img-' + index">
            <img :src="storyboard.scene_image" :alt="'img-' + index" v-show="(index === activeSceneIndex)"
              :style="{ objectFit: objectFitStyle }" :class="{
                // 'zooming': isPlaying && index === activeSceneIndex,
                'slide-active': index === activeSceneIndex,
                'camera-zoom-in': isPlaying && index === activeSceneIndex && storyboard.camera_effect === 'zoom-in',
                'camera-zoom-out': isPlaying && index === activeSceneIndex && storyboard.camera_effect === 'zoom-out',
                'camera-pan-right': isPlaying && index === activeSceneIndex && storyboard.camera_effect === 'pan-right',
                'camera-pan-left': isPlaying && index === activeSceneIndex && storyboard.camera_effect === 'pan-left',
                'camera-tilt-up': isPlaying && index === activeSceneIndex && storyboard.camera_effect === 'tilt-up',
                'camera-tilt-down': isPlaying && index === activeSceneIndex && storyboard.camera_effect === 'tilt-down'
              }" />
          </template>

          <!-- 图片加载指示器 -->
          <div class="image-loading-overlay" v-if="isImageLoading">
            <el-icon class="loading-icon">
              <loading />
            </el-icon>
          </div>
        </div>

        <!-- 标题和字幕 -->
        <div class="slide-overlay" v-if="!isShowingCover">
          <div class="subtitle-container" v-if="isPlaying">
            <transition name="fade">
              <div class="subtitle-text" v-if="currentSubtitle">{{ displayedSubtitle }}</div>
            </transition>
          </div>
        </div>

        <!-- 大型播放按钮 -->
        <transition name="fade">
          <div class="play-button-large" v-if="!isPlaying && !isShowingCover" @click="togglePlayPause()">
            <transition name="fade">
              <h2 class="story-title" v-if="!isPlaying">{{ coverTitle || '' }}</h2>
            </transition>
            <p class="cover-subtitle2">{{ coverSubtitle2 }}</p>
            <div class="play-icon">
              <el-icon><video-play /></el-icon>
            </div>
          </div>
        </transition>

        <!-- 播放控制栏 -->
        <div class="player-controls"
          :class="{ 'visible': (isPlaying && !isShowingCover) && (isControlsVisible || isControlsHovered) }"
          @mouseenter="keepControlsVisible" @mouseleave="leaveControls">
          <div class="progress-info">
            <span>{{ activeSceneIndex + 1 }}/{{ props.storyboards?.shots.length }}</span>
            <!-- <span class="progress-status">{{ progressStatus }}</span> -->
          </div>
          <div class="playback-controls">
            <el-button class="control-btn" :disabled="activeSceneIndex <= 0" @click="playPrevScene">
              <el-icon>
                <ArrowLeft />
              </el-icon>
            </el-button>
            <el-button class="control-btn play-pause-btn" @click="togglePlayPause">
              <el-icon v-if="isPlaying">
                <VideoPause />
              </el-icon>
              <el-icon v-else>
                <VideoPlay />
              </el-icon>
            </el-button>
            <el-button class="control-btn" :disabled="activeSceneIndex >= props.storyboards?.shots.length - 1"
              @click="playNextScene">
              <el-icon>
                <ArrowRight />
              </el-icon>
            </el-button>
            <el-button class="control-btn" @click="restartPlayback">
              <el-icon>
                <RefreshLeft />
              </el-icon>
            </el-button>

            <!-- 倍速按钮 -->
            <el-dropdown trigger="click" @command="changePlaybackRate" class="speed-dropdown">
              <el-button class="control-btn speed-btn">
                {{ playbackRate }}x
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-for="rate in playbackRates" :key="rate" :command="rate"
                    :class="{ 'active-rate': playbackRate === rate }">
                    {{ rate }}x
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 全屏按钮 - 在非移动设备上显示 -->
            <el-button class="control-btn fullscreen-btn" @click="toggleFullscreen">
              <el-icon v-if="isFullscreen">
                <Close />
              </el-icon>
              <el-icon v-else>
                <FullScreen />
              </el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 音频元素（隐藏） -->
    <audio ref="audioPlayer" @ended="onAudioEnded" @timeupdate="updateCurrentTime" style="display: none;"></audio>

  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed, onBeforeUnmount } from 'vue'
import { VideoPlay, VideoPause, Picture, VideoCamera, Loading, ArrowLeft, RefreshLeft, ArrowRight, Share, Clock, Close, FullScreen } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { isWechatBrowser as checkIsWechatBrowser } from '@/utils/wxShare.js'

const props = defineProps({
  creativeText: {
    type: String,
    default: ''
  },
  styleName: {
    type: String,
    default: ''
  },
  videoProgress: {
    type: Number,
    default: 0
  },
  volumeLevel: {
    type: Number,
    default: 80
  },
  showShareOptions: {
    type: Boolean,
    default: false
  },
  showGenerateVideo: {
    type: Boolean,
    default: false
  },
  showGenerateHome: {
    type: Boolean,
    default: false
  },
  showGenerateShare: {
    type: Boolean,
    default: false
  },
  storyboards: {
    type: Object,
    default: null
  },
  showCover: {
    type: Boolean,
    default: true
  },
  aspectRatio: {
    type: String,
    default: '16:9'
  },
  imageSize: {
    type: String,
    default: '16:9'
  }
})

const emit = defineEmits([
  'update:videoProgress',
  'update:volumeLevel',
  'generate-video',
  'generate-share',
  'update:showCover',
  'update:isPlaying',
  'story-completed'
])

// 响应式数据
const activeSceneIndex = ref(0)
const isPlaying = ref(false)
const audioPlayer = ref(null)
const currentTime = ref(0)
const audioDurations = ref([]) // 存储每个音频的实际时长
const totalDuration = ref(0) // 总时长
const imageCache = ref({}) // 缓存已加载的图片
const nextImagePreloaded = ref(false) // 下一张图片是否已预加载
// 控制栏显示状态
const isControlsVisible = ref(false)
const isControlsHovered = ref(false)
const controlsTimer = ref(null)
// 无音频分镜自动切换的定时器
const noAudioTimer = ref(null)

// 窗口宽高比和图片适配样式
const isWindowLandscape = ref(true) // 默认为横屏
const objectFitStyle = ref('cover') // 默认为cover

// 播放倍速相关
const playbackRate = ref(1.0) // 当前播放速度
const playbackRates = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0] // 可选播放速度
// 切换播放速度
const changePlaybackRate = (rate) => {
  playbackRate.value = rate

  // 设置音频播放速度
  if (audioPlayer.value) {
    audioPlayer.value.playbackRate = rate
  }

  // 设置背景音乐播放速度
  if (backgroundMusicPlayer.value) {
    backgroundMusicPlayer.value.playbackRate = rate
    
    // 对于iOS设备，确保在速度变化后音量设置仍然有效
    if (isIOS.value && props.storyboards?.background_music && audioContext.value) {
      // 重新设置音量，确保在速度变化后仍然保持正确的音量
      setGainVolume(props.storyboards?.background_music.volume)
    }
  }

  // 调整视觉效果持续时间（如果正在播放）
  if (isPlaying.value && activeSceneIndex.value < props.storyboards?.shots.length) {
    const currentScene = props.storyboards?.shots[activeSceneIndex.value]
    const baseDuration = currentScene.duration_seconds || 10
    const adjustedDuration = baseDuration / rate

    // 更新CSS变量，控制动画速度
    document.documentElement.style.setProperty('--duration', `${adjustedDuration}s`)
  }

  ElMessage.success(`播放速度已调整为 ${rate}x`)
}

// 新增响应式变量
const isShowingCover = ref(props.showCover)
const backgroundMusicPlayer = ref(null)
const audioContext = ref(null)
const gainNode = ref(null)
const isIOS = ref(false)

// 为旁白音频单独创建音频上下文和增益节点
const narratorAudioContext = ref(null)
const narratorGainNode = ref(null)
// 标记旁白音频是否已连接
const narratorAudioConnected = ref(false)
// 记录当前连接的旁白音频URL
const currentNarratorAudioUrl = ref('')

// 检测iOS设备
const checkIOS = () => {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera;
  const isIOS = /iPad|iPhone|iPod/.test(userAgent) && !window.MSStream;
  const isSafari = /Version\/[\d.]+.*Safari/.test(userAgent);
  
  console.log('设备检测:', { 
    userAgent, 
    isIOS, 
    isSafari,
    isIOSSafari: isIOS && isSafari
  });
  
  // 只对iOS Safari应用特殊处理
  return isIOS;
}

// 初始化Web Audio API - 使用单个AudioContext以简化处理
const initAudioContext = () => {
  try {
    // 创建单个共享的音频上下文
    const AudioContext = window.AudioContext || window.webkitAudioContext;
    if (!audioContext.value) {
      audioContext.value = new AudioContext();
      console.log('创建音频上下文成功');
    }
    
    // 创建单个增益节点用于控制音量
    if (!gainNode.value && audioContext.value) {
      gainNode.value = audioContext.value.createGain();
      gainNode.value.connect(audioContext.value.destination);
      console.log('创建增益节点成功');
    }
    
    // 不再创建独立的旁白音频上下文，使用更简单的方式处理
    // 将narratorAudioContext和narratorGainNode标记为已初始化但不实际使用
    narratorAudioContext.value = true; // 只用作标记
    narratorGainNode.value = true; // 只用作标记
    
    console.log('音频上下文初始化完成');
    return true;
  } catch (error) {
    console.error('初始化音频上下文失败:', error);
    return false;
  }
}

// 使用Web Audio API连接音频并控制音量 - 仅用于背景音乐
const connectAudioSource = (audioElement, volume) => {
  // 参数检查
  if (!audioElement) {
    console.error('无法连接音频：音频元素为空');
    return false;
  }
  
  // 非iOS设备直接设置音量并返回
  if (!isIOS.value) {
    audioElement.volume = volume;
    console.log('非iOS设备：直接设置音频音量:', volume);
    return true;
  }
  
  // 以下是iOS设备的处理逻辑
  if (!audioContext.value || !gainNode.value) {
    console.error('无法连接音频：音频上下文或增益节点未初始化');
    return false;
  }
  
  try {
    // 只有iOS设备才使用Web Audio API
    // 检查元素是否已经有sourceNode
    if (audioElement._isConnected) {
      console.log('音频元素已连接，直接设置音量:', volume);
      setGainVolume(volume);
      return true;
    }
    
    // 创建媒体元素源
    const source = audioContext.value.createMediaElementSource(audioElement);
    
    // 连接：源 -> 增益节点 -> 输出设备
    source.connect(gainNode.value);
    
    // 标记音频元素为已连接
    audioElement._isConnected = true;
    
    // 设置音量 (0-1范围)
    setGainVolume(volume);
    
    console.log('成功连接音频源并设置音量:', volume);
    return true;
  } catch (error) {
    console.error('连接音频源失败:', error);
    // 失败时尝试使用原生方法
    try {
      audioElement.volume = volume;
      console.log('回退：使用原生方法设置音量:', volume);
      return true;
    } catch (fallbackError) {
      console.error('回退方案也失败:', fallbackError);
      return false;
    }
  }
}

// 移除connectNarratorAudioSource方法，改为直接使用原生音量控制
// 使用旁白音频的原生音量控制
const setNarratorVolume = (audioElement, volume) => {
  if (!audioElement) return false;
  
  try {
    audioElement.volume = volume;
    console.log('设置旁白音量(原生方法):', volume);
    return true;
  } catch (error) {
    console.error('设置旁白音量失败:', error);
    return false;
  }
}

// 设置增益节点的音量
const setGainVolume = (volume) => {
  if (gainNode.value) {
    // 确保音量在0-1范围内
    const safeVolume = Math.max(0, Math.min(1, volume));
    gainNode.value.gain.value = safeVolume;
    console.log('设置增益节点音量:', safeVolume);
  }
}

// 计算属性
const sceneNames = computed(() => {
  return props.storyboards?.shots.map(scene => scene.name)
})

const currentSubtitle = computed(() => {
  return props.storyboards?.shots[activeSceneIndex.value]?.subtitle || ''
})

const currentAudio = computed(() => {
  return props.storyboards?.shots[activeSceneIndex.value]?.audio_clip || ''
})

// 添加显示的字幕文本
const displayedSubtitle = ref('')
// 添加微信浏览器检测相关变量
const isWechatBrowser = ref(false)

// 检测是否是微信浏览器
const checkWechatBrowser = () => {
  isWechatBrowser.value = checkIsWechatBrowser()
}

// 智能分段处理字幕文本
const formatSubtitle = (text) => {
  if (!text) return [];

  // 如果是微信浏览器 且 是iOS设备，则不进行分段
  if (isWechatBrowser.value && isIOS.value) {
    return [text]
  }

  // 中文主要标点符号（句末标点）- 添加逗号和分号作为主要分段标点
  const majorPunctuations = ['。', '！', '？', '…', '.', '!', '?', '，', '；', ',', ';'];
  // 中文次要标点符号（句中标点）- 移除逗号和分号
  const minorPunctuations = ['：', ':', '、'];
  // 每段最大字符数
  const maxCharsPerLine = 30; // 由于字体较大，减少每行最大字符数

  // 先按主要标点符号分段
  let segments = [];
  let currentSegment = '';

  for (let i = 0; i < text.length; i++) {
    currentSegment += text[i];

    // 如果遇到主要标点符号，则分段
    if (majorPunctuations.includes(text[i])) {
      // 去除末尾的标点符号
      segments.push(currentSegment.slice(0, -1));
      currentSegment = '';
    }
  }

  // 处理剩余文本
  if (currentSegment) {
    segments.push(currentSegment);
  }

  // 对于过长的段落，在次要标点处进一步分段
  let midSegments = [];
  segments.forEach(segment => {
    if (segment.length <= maxCharsPerLine) {
      midSegments.push(segment);
    } else {
      // 尝试在次要标点处分段
      let subSegments = [];
      let subSegment = '';

      for (let i = 0; i < segment.length; i++) {
        subSegment += segment[i];

        // 如果遇到次要标点且已累积一定长度，则分段
        if (minorPunctuations.includes(segment[i]) && subSegment.length >= 5) {
          // 去除末尾的次要标点符号
          subSegments.push(subSegment.slice(0, -1));
          subSegment = '';
        }
      }

      if (subSegment) {
        subSegments.push(subSegment);
      }

      midSegments = [...midSegments, ...subSegments];
    }
  });

  // 最后处理仍然过长的段落
  let finalSegments = [];
  midSegments.forEach(segment => {
    if (segment.length <= maxCharsPerLine) {
      finalSegments.push(segment);
    } else {
      // 尝试找到合适的分段点，避免在单词或词组中间断开
      for (let i = 0; i < segment.length; i += maxCharsPerLine) {
        let endPos = i + maxCharsPerLine;

        // 如果分段点落在单词中间，尝试向前找到空格
        if (endPos < segment.length) {
          const searchLimit = Math.max(i, endPos - 5); // 向前最多查找5个字符
          for (let j = endPos; j > searchLimit; j--) {
            if (segment[j] === ' ' || minorPunctuations.includes(segment[j])) {
              // 如果是次要标点，去除标点
              endPos = minorPunctuations.includes(segment[j]) ? j : j + 1;
              break;
            }
          }
        }

        finalSegments.push(segment.slice(i, Math.min(endPos, segment.length)));
      }
    }
  });

  // 返回所有分段，而不仅仅是最后一段
  return finalSegments;
};

// 计算当前应该显示的字幕长度
const calculateSubtitleLength = () => {
  if (!currentSubtitle.value) return

  // 获取分段后的字幕数组
  const subtitleSegments = formatSubtitle(currentSubtitle.value)

  if (subtitleSegments.length === 0) return

  // 如果没有音频或者音频播放器，直接显示第一个分段
  if (!audioPlayer.value || !audioDurations.value[activeSceneIndex.value]) {
    displayedSubtitle.value = subtitleSegments[0]
    return
  }

  // 根据音频播放进度计算应显示的分段
  const audioDuration = audioDurations.value[activeSceneIndex.value]

  // 提前1秒显示字幕
  const advanceTimeInSeconds = 0.5
  // 计算当前时间并提前1秒，确保不小于0
  const adjustedCurrentTime = Math.max(0, audioPlayer.value.currentTime + advanceTimeInSeconds)
  // 计算调整后的进度比例，确保不超过1
  const currentProgress = Math.min(1, adjustedCurrentTime / audioDuration)

  // 计算当前应该显示的分段索引
  const segmentIndex = Math.min(
    Math.floor(currentProgress * subtitleSegments.length),
    subtitleSegments.length - 1
  )

  // 更新显示的字幕
  displayedSubtitle.value = subtitleSegments[segmentIndex]
}

// 显示当前播放状态
const progressStatus = computed(() => {
  if (isPlaying.value) {
    return '播放中'
  } else if (activeSceneIndex.value === 0 && currentTime.value === 0) {
    return '准备就绪'
  } else if (activeSceneIndex.value === props.storyboards?.shots.length - 1 && audioPlayer.value?.ended) {
    return '播放完成'
  } else {
    return '已暂停'
  }
})

// 新增计算属性
const coverImage = computed(() => props.storyboards?.cover?.image || '')
const coverTitle = computed(() => props.storyboards?.cover?.title || '')
const coverSubtitle = computed(() => props.storyboards?.cover?.subtitle || '')
const coverSubtitle2 = computed(() => props.storyboards?.cover?.subtitle2 || '')

// 监听showCover属性变化
watch(() => props.showCover, (newValue) => {
  isShowingCover.value = newValue
})

// 暴露isShowingCover变化
watch(isShowingCover, (newValue) => {
  emit('update:showCover', newValue)
})

// 监听storyboards变化，当数据变化时重置组件状态
watch(() => props.storyboards, (newVal) => {
  if (newVal) {
    // 重置播放状态
    isPlaying.value = false
    activeSceneIndex.value = 0
    displayedSubtitle.value = ''

    // 根据showCover属性决定是否显示封面
    isShowingCover.value = props.showCover

    // 暂停音频播放
    if (audioPlayer.value) {
      audioPlayer.value.pause()
      audioPlayer.value.currentTime = 0
    }

    // 暂停背景音乐
    if (backgroundMusicPlayer.value) {
      backgroundMusicPlayer.value.pause()
    }

    // 重新加载音频时长和图片
    loadAudioDurations()
    preloadImages()

    // 初始化背景音乐播放器
    if (props.storyboards?.background_music?.audio_url) {
      if (backgroundMusicPlayer.value) {
        backgroundMusicPlayer.value.pause()
      }
      backgroundMusicPlayer.value = new Audio(props.storyboards?.background_music?.audio_url)
      
      // 如果是iOS设备，等待用户交互后再初始化音频上下文
      if (isIOS.value) {
        // 初始化音频上下文函数已在onMounted中设置
        // 如果audioContext已存在，则重新设置音量
        if (audioContext.value && gainNode.value) {
          // 为新的音频元素连接音频上下文
          setTimeout(() => {
            try {
              connectAudioSource(
                backgroundMusicPlayer.value, 
                props.storyboards?.background_music.volume
              );
            } catch (error) {
              console.error('重新连接音频源失败:', error);
            }
          }, 100); // 短暂延迟确保Audio元素已就绪
        }
      } else {
        // 非iOS设备直接设置音量
        backgroundMusicPlayer.value.volume = props.storyboards?.background_music.volume
      }
      
      // 设置播放速度
      backgroundMusicPlayer.value.playbackRate = playbackRate.value
    }
  }
}, { deep: true })

// 播放当前场景
const playCurrentScene = () => {
  if (activeSceneIndex.value >= 0 && activeSceneIndex.value < props.storyboards?.shots.length) {
    // 先清除可能存在的无音频定时器
    if (noAudioTimer.value) {
      clearTimeout(noAudioTimer.value)
      noAudioTimer.value = null
    }

    const currentScene = props.storyboards?.shots[activeSceneIndex.value]

    // 考虑播放速度设置动画持续时间
    const adjustedDuration = (currentScene.duration_seconds || 10) / playbackRate.value
    document.documentElement.style.setProperty('--duration', `${adjustedDuration}s`)

    // 重置字幕显示
    displayedSubtitle.value = ''

    // 标记图片正在加载
    isImageLoading.value = true

    // 检查图片是否已缓存
    if (imageCache.value[currentScene.scene_image]) {
      // 已缓存，直接使用
      isImageLoading.value = false
      console.log('使用缓存的图片:', currentScene.scene_image)
    } else {
      // 未缓存，加载图片
      const img = new Image()
      img.src = currentScene.scene_image
      img.onload = () => {
        // 缓存图片
        imageCache.value[currentScene.scene_image] = img
        isImageLoading.value = false
      }
      img.onerror = () => {
        console.error('图片加载失败:', currentScene.scene_image)
        isImageLoading.value = false
      }
    }

    // 预加载下一个场景的图片
    nextImagePreloaded.value = false
    preloadNextImage()

    // 直接设置初始字幕
    if (currentScene.subtitle) {
      const subtitleSegments = formatSubtitle(currentScene.subtitle)
      if (subtitleSegments.length > 0) {
        displayedSubtitle.value = subtitleSegments[0]
      }
    }

    if (audioPlayer.value && currentScene.audio_clip) {
      // 记录之前的src用于比较
      const previousSrc = audioPlayer.value.src;
      
      // 设置新的音频源
      audioPlayer.value.src = currentScene.audio_clip;
      audioPlayer.value.currentTime = 0;
      // 设置播放速度
      audioPlayer.value.playbackRate = playbackRate.value;

      // 设置音量 - 统一使用原生方法
      if (isIOS.value) {
        console.log('iOS设备: 为旁白音频设置音量', props.volumeLevel / 100);
      }
      
      // 无论是iOS还是其他设备，都使用原生音量设置
      setNarratorVolume(audioPlayer.value, props.volumeLevel / 100);
      
      // 播放音频
      audioPlayer.value.play().then(() => {
        isPlaying.value = true;
        emit('update:isPlaying', true);
        console.log('旁白音频播放成功');
      }).catch(error => {
        console.error('播放音频失败:', error);
        isPlaying.value = false;
      });
    } else {
      // 即使没有音频，也设置为播放状态
      isPlaying.value = true
      
      // 如果没有音频，设置定时器在指定时间后自动切换到下一个分镜
      if (isPlaying.value) {
        const durationSeconds = currentScene.duration_seconds || 5 // 默认5秒
        const adjustedDuration = durationSeconds * 1000 / playbackRate.value // 毫秒，考虑播放速度
        
        console.log(`分镜 ${activeSceneIndex.value + 1} 没有音频，将在 ${adjustedDuration}ms 后自动切换`)
        
        noAudioTimer.value = setTimeout(() => {
          // 仅当仍然在播放同一分镜时才切换到下一个
          if (isPlaying.value && activeSceneIndex.value < props.storyboards?.shots.length - 1) {
            changeStoryboard(activeSceneIndex.value + 1)
          }
        }, adjustedDuration)
      }
    }
  }
}

// 创建替代的subtitleTimer变量以保持兼容性
const subtitleTimer = ref(null)

// 切换场景（点击场景导航时调用）
const changeStoryboard = (index) => {
  if (index >= 0 && index < props.storyboards?.shots.length) {
    // 重置字幕显示
    displayedSubtitle.value = ''

    // 清除可能存在的无音频定时器
    if (noAudioTimer.value) {
      clearTimeout(noAudioTimer.value)
      noAudioTimer.value = null
    }

    // 切换到新场景
    activeSceneIndex.value = index
    playCurrentScene()
  }
}

// 当前场景播放结束后的处理
const onAudioEnded = () => {
  // 确保显示最后一个分段的字幕
  if (currentSubtitle.value) {
    const subtitleSegments = formatSubtitle(currentSubtitle.value)
    if (subtitleSegments.length > 0) {
      displayedSubtitle.value = subtitleSegments[subtitleSegments.length - 1]
    }
  }

  // 自动播放下一个场景
  if (activeSceneIndex.value < props.storyboards?.shots.length - 1) {
    changeStoryboard(activeSceneIndex.value + 1)
  } else {
    // 所有场景播放完毕，重置为初始状态
    isPlaying.value = false
    activeSceneIndex.value = 0
    // 暂停背景音乐
    if (backgroundMusicPlayer.value) {
      backgroundMusicPlayer.value.pause()
    }
    // 触发章节播放完成事件
    emit('story-completed')
  }
}

// 更新当前时间和进度
const updateCurrentTime = () => {
  if (audioPlayer.value) {
    currentTime.value = audioPlayer.value.currentTime

    // 计算字幕显示
    calculateSubtitleLength()

    // 如果已经加载了所有音频时长，计算当前总进度
    if (audioDurations.value.length === props.storyboards?.shots.length) {
      // 计算之前场景的总时长
      let previousDuration = 0
      for (let i = 0; i < activeSceneIndex.value; i++) {
        previousDuration += audioDurations.value[i] || 0
      }

      // 当前进度 = (之前场景总时长 + 当前场景播放时间) / 总时长
      const progress = ((previousDuration + currentTime.value) / totalDuration.value) * 100
      emit('update:videoProgress', progress)
    }
  }
}

// 新增方法
const startStory = () => {
  isShowingCover.value = false;
  emit('update:showCover', false);
  
  // 开始播放背景音乐
  if (backgroundMusicPlayer.value && props.storyboards?.background_music?.audio_url) {
    console.log('开始播放背景音乐');
    
    try {
      // 设置音量 - iOS设备使用Web Audio API，其他设备直接设置
      if (isIOS.value) {
        // 确保音频上下文已初始化
        if (!audioContext.value) {
          initAudioContext();
        }
        
        // 尝试连接背景音乐
        try {
          if (!backgroundMusicPlayer.value._isConnected) {
            connectAudioSource(
              backgroundMusicPlayer.value, 
              props.storyboards?.background_music.volume || 1
            );
          } else {
            // 已经连接，只设置音量
            setGainVolume(props.storyboards?.background_music.volume || 1);
          }
        } catch (error) {
          console.warn('连接背景音乐失败，尝试使用原生方法:', error);
          backgroundMusicPlayer.value.volume = props.storyboards?.background_music.volume || 1;
        }
      } else {
        // 非iOS设备直接设置音量
        backgroundMusicPlayer.value.volume = props.storyboards?.background_music.volume || 1;
      }
      
      // 设置循环播放
      backgroundMusicPlayer.value.loop = props.storyboards?.background_music.loop || false;
      
      // 播放背景音乐
      const playPromise = backgroundMusicPlayer.value.play();
      if (playPromise !== undefined) {
        playPromise.then(() => {
          console.log('背景音乐播放成功');
        }).catch(error => {
          console.error('背景音乐播放失败:', error);
        });
      }
    } catch (error) {
      console.error('背景音乐初始化失败:', error);
    }
  }
  
  // 开始播放第一个场景
  changeStoryboard(0);
}

// 组件挂载时初始化
onMounted(() => {
  // 检查是否为iOS设备
  isIOS.value = checkIOS()
  console.log('是否为iOS设备:', isIOS.value)

  checkWechatBrowser()
  console.log('是否为微信浏览器:', isWechatBrowser.value)

  // 预加载所有音频并获取时长
  loadAudioDurations()

  // 预加载所有图片
  preloadImages()

  // 初始化背景音乐播放器
  if (props.storyboards?.background_music?.audio_url) {
    backgroundMusicPlayer.value = new Audio(props.storyboards?.background_music?.audio_url)
    
    // 如果是iOS设备，等待用户交互后初始化音频上下文
    if (isIOS.value) {
      // 初始化音频上下文 (在iOS上，需要用户交互后才能创建)
      const initOnUserInteraction = () => {
        if (initAudioContext()) {
          // 连接背景音乐源
          connectAudioSource(
            backgroundMusicPlayer.value, 
            props.storyboards?.background_music.volume
          );
          
          // 此时不连接旁白音频，因为旁白音频的URL会变化
          // 我们将在每次设置旁白音频的src时重新连接
          
          // 移除事件监听器
          document.removeEventListener('touchstart', initOnUserInteraction);
          document.removeEventListener('click', initOnUserInteraction);
        }
      };
      
      // 添加用户交互事件监听器
      document.addEventListener('touchstart', initOnUserInteraction);
      document.addEventListener('click', initOnUserInteraction);
    } else {
      // 非iOS设备设置初始音量
      backgroundMusicPlayer.value.volume = props.storyboards?.background_music.volume
    }
    
    // 设置初始播放速度
    backgroundMusicPlayer.value.playbackRate = playbackRate.value
  }

  if (audioPlayer.value) {
    if (!isIOS.value) {
      // 非iOS设备直接设置音量
      audioPlayer.value.volume = props.volumeLevel / 100
    }
    // 设置初始播放速度
    audioPlayer.value.playbackRate = playbackRate.value
  }

  // 初始化字幕显示
  displayedSubtitle.value = ''

  // 添加全屏变化事件监听
  document.addEventListener('fullscreenchange', handleFullscreenChange)

  // 添加键盘快捷键监听
  document.addEventListener('keydown', handleKeyDown)

  // 初始化窗口大小检测
  handleResize()

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
})

// 监视activeSceneIndex的变化，确保图片加载
watch(activeSceneIndex, (newIndex) => {
  if (newIndex >= 0 && newIndex < props.storyboards?.shots.length) {
    const currentScene = props.storyboards?.shots[newIndex]
    // 如果该场景图片未缓存，则加载
    if (currentScene && currentScene.scene_image && !imageCache.value[currentScene.scene_image]) {
      isImageLoading.value = true
      const img = new Image()
      img.src = currentScene.scene_image
      img.onload = () => {
        imageCache.value[currentScene.scene_image] = img
        isImageLoading.value = false
      }
    }
  }
})

// 监听imageSize变化，更新objectFitStyle
watch(() => props.imageSize, () => {
  handleResize()
})

// 修改preloadImages函数，优化并行加载性能
const preloadImages = () => {
  console.log('开始预加载图片...')

  // 使用批处理方式加载图片，避免一次性创建过多Image对象
  const batchSize = 3; // 每批处理的图片数量
  const shots = props.storyboards?.shots || [];

  // 优先加载第一个场景的图片
  if (shots.length > 0 && shots[0].scene_image) {
    const firstImg = new Image();
    firstImg.src = shots[0].scene_image;
    firstImg.onload = () => {
      imageCache.value[shots[0].scene_image] = firstImg;
      console.log(`首个图片预加载成功: ${shots[0].scene_image}`);

      // 第一张图片加载完成后，批量加载其余图片
      loadImagesInBatches(shots.slice(1), batchSize);
    };
    firstImg.onerror = () => {
      console.error(`首个图片预加载失败: ${shots[0].scene_image}`);
      // 即使首图加载失败，也继续加载其他图片
      loadImagesInBatches(shots.slice(1), batchSize);
    };
  } else {
    // 没有场景或者第一个场景没有图片，直接批量加载
    loadImagesInBatches(shots, batchSize);
  }
}

// 批量加载图片函数
const loadImagesInBatches = (scenes, batchSize) => {
  let currentIndex = 0;

  function loadNextBatch () {
    // 如果已经加载完所有图片，退出
    if (currentIndex >= scenes.length) return;

    // 计算当前批次的结束索引
    const endIndex = Math.min(currentIndex + batchSize, scenes.length);
    const currentBatch = scenes.slice(currentIndex, endIndex);

    // 并行加载当前批次的图片
    const loadPromises = currentBatch.map(scene => {
      if (!scene.scene_image || imageCache.value[scene.scene_image]) {
        // 跳过没有图片或已缓存的图片
        return Promise.resolve();
      }

      return new Promise((resolve) => {
        const img = new Image();
        img.src = scene.scene_image;

        img.onload = () => {
          imageCache.value[scene.scene_image] = img;
          // console.log(`图片预加载成功: ${scene.scene_image}`);
          resolve();
        };

        img.onerror = () => {
          console.error(`图片预加载失败: ${scene.scene_image}`);
          resolve(); // 即使加载失败也继续处理
        };
      });
    });

    // 当当前批次的所有图片都处理完后，加载下一批
    Promise.all(loadPromises).then(() => {
      currentIndex = endIndex;
      // 使用requestAnimationFrame避免阻塞主线程
      requestAnimationFrame(loadNextBatch);
    });
  }

  // 开始加载第一批
  loadNextBatch();
};

// 优化音频预加载
const loadAudioDurations = async () => {
  totalDuration.value = 0;
  audioDurations.value = [];

  // 使用队列处理音频加载，而不是同时加载所有音频
  const audioQueue = [];

  for (let i = 0; i < props.storyboards?.shots.length; i++) {
    const scene = props.storyboards?.shots[i];
    if (scene.audio_clip) {
      audioQueue.push({
        index: i,
        audioClip: scene.audio_clip
      });
    } else {
      audioDurations.value[i] = 0;
    }
  }

  // 优先加载前两个音频（通常是最先需要的）
  const priorityAudios = audioQueue.splice(0, 2);
  await Promise.all(priorityAudios.map(item => loadSingleAudio(item.index, item.audioClip)));

  // 然后使用队列按顺序加载其余的音频，避免同时创建太多Audio对象
  for (const item of audioQueue) {
    await loadSingleAudio(item.index, item.audioClip);
  }

  console.log('音频时长加载完成:', audioDurations.value, '总时长:', totalDuration.value);
}

// 单个音频加载函数
const loadSingleAudio = (index, audioClip) => {
  return new Promise(resolve => {
    try {
      // 创建临时音频元素获取时长
      const tempAudio = new Audio(audioClip);

      // 添加一个超时处理，避免音频加载过久
      const timeoutId = setTimeout(() => {
        console.warn(`音频加载超时: ${audioClip}`);
        audioDurations.value[index] = 0;
        resolve();
      }, 5000); // 5秒超时

      tempAudio.addEventListener('loadedmetadata', () => {
        clearTimeout(timeoutId);
        audioDurations.value[index] = tempAudio.duration;
        totalDuration.value += tempAudio.duration;
        resolve();
      });

      tempAudio.addEventListener('error', () => {
        clearTimeout(timeoutId);
        console.error(`无法加载音频: ${audioClip}`);
        audioDurations.value[index] = 0;
        resolve();
      });
    } catch (error) {
      console.error(`加载音频失败: ${audioClip}`, error);
      audioDurations.value[index] = 0;
      resolve();
    }
  });
}

// 预加载下一个场景的图片
const preloadNextImage = () => {
  const nextIndex = activeSceneIndex.value + 1
  if (nextIndex < props.storyboards?.shots.length) {
    const nextScene = props.storyboards?.shots[nextIndex]
    if (nextScene && nextScene.scene_image) {
      // 如果下一个场景的图片已经在缓存中，标记为已预加载
      if (imageCache.value[nextScene.scene_image]) {
        nextImagePreloaded.value = true
        return
      }

      const img = new Image()
      img.src = nextScene.scene_image
      img.onload = () => {
        imageCache.value[nextScene.scene_image] = img
        nextImagePreloaded.value = true
        console.log(`下一个场景图片预加载成功: ${nextScene.scene_image}`)
      }
    }
  }
}

// 音量控制
const updateVolume = (value) => {
  if (audioPlayer.value) {
    audioPlayer.value.volume = value / 100
  }
  emit('update:volumeLevel', value)
}

// 组件卸载时清理
onBeforeUnmount(() => {
  console.log('组件卸载，清理资源');
  
  // 清理音频播放器资源
  if (audioPlayer.value) {
    try {
      audioPlayer.value.pause();
      audioPlayer.value.src = '';
      audioPlayer.value = null;
      console.log('旁白音频资源已清理');
    } catch (error) {
      console.error('清理旁白音频资源出错:', error);
    }
  }
  
  // 清理背景音乐资源
  if (backgroundMusicPlayer.value) {
    try {
      backgroundMusicPlayer.value.pause();
      backgroundMusicPlayer.value = null;
      console.log('背景音乐资源已清理');
    } catch (error) {
      console.error('清理背景音乐资源出错:', error);
    }
  }

  // 清理Web Audio API资源
  if (gainNode.value) {
    try {
      gainNode.value.disconnect();
      gainNode.value = null;
      console.log('增益节点已断开连接');
    } catch (error) {
      console.error('断开增益节点连接出错:', error);
    }
  }
  
  if (audioContext.value) {
    try {
      // 在某些浏览器中，AudioContext可能需要关闭
      if (audioContext.value.state !== 'closed' && audioContext.value.close) {
        audioContext.value.close().catch(err => {
          console.error('关闭音频上下文失败:', err);
        });
      }
      audioContext.value = null;
      console.log('音频上下文已清理');
    } catch (error) {
      console.error('清理音频上下文出错:', error);
    }
  }

  // 重置其他引用
  narratorAudioContext.value = null;
  narratorGainNode.value = null;
  narratorAudioConnected.value = false;
  
  // 清理定时器
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value);
    controlsTimer.value = null;
  }

  // 清理无音频分镜的自动切换定时器
  if (noAudioTimer.value) {
    clearTimeout(noAudioTimer.value);
    noAudioTimer.value = null;
  }

  // 移除事件监听器
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
  document.removeEventListener('keydown', handleKeyDown);
  window.removeEventListener('resize', handleResize);
  document.removeEventListener('touchstart', initAudioContext);
  document.removeEventListener('click', initAudioContext);
  
  console.log('所有资源已清理完成');
})

// 在切换场景时添加图片加载指示器
const isImageLoading = ref(false)

// 播放控制栏
const playPrevScene = () => {
  if (activeSceneIndex.value > 0) {
    changeStoryboard(activeSceneIndex.value - 1)
  }
}

const restartPlayback = () => {
  changeStoryboard(0)
}

const playNextScene = () => {
  if (activeSceneIndex.value < props.storyboards?.shots.length - 1) {
    changeStoryboard(activeSceneIndex.value + 1)
  }
}

// 切换播放暂停时处理字幕定时器
const togglePlayPause = () => {
  if (isPlaying.value) {
    // 暂停旁白音频
    if (audioPlayer.value) {
      audioPlayer.value.pause();
      console.log('旁白音频已暂停');
    }
    
    // 暂停背景音乐
    if (backgroundMusicPlayer.value) {
      backgroundMusicPlayer.value.pause();
      console.log('背景音乐已暂停');
    }
    
    // 清除无音频分镜的自动切换定时器
    if (noAudioTimer.value) {
      clearTimeout(noAudioTimer.value);
      noAudioTimer.value = null;
    }
    
    isPlaying.value = false;
    
    // 显示控制栏，但不自动隐藏
    isControlsVisible.value = true;
    // 清除可能存在的定时器
    if (controlsTimer.value) {
      clearTimeout(controlsTimer.value);
      controlsTimer.value = null;
    }
  } else {
    // 恢复播放
    if (activeSceneIndex.value == 0) {
      // 如果是从头开始播放，使用changeStoryboard方法
      changeStoryboard(0);
    } else {
      // 继续播放当前场景
      // 处理旁白音频
      if (audioPlayer.value && audioPlayer.value.src) {
        console.log('恢复播放旁白音频');
        // 设置音量
        setNarratorVolume(audioPlayer.value, props.volumeLevel / 100);
        // 播放
        audioPlayer.value.play().catch(error => {
          console.error('恢复播放旁白音频失败:', error);
        });
      } else if (isPlaying.value === false) {
        // 如果当前分镜没有音频，设置定时器自动切换
        const currentScene = props.storyboards?.shots[activeSceneIndex.value];
        if (currentScene) {
          const durationSeconds = currentScene.duration_seconds || 5; // 默认5秒
          const adjustedDuration = durationSeconds * 1000 / playbackRate.value; // 毫秒，考虑播放速度
          
          console.log(`恢复播放：无旁白音频场景，将在 ${adjustedDuration}ms 后自动切换`);
          
          noAudioTimer.value = setTimeout(() => {
            if (isPlaying.value && activeSceneIndex.value < props.storyboards?.shots.length - 1) {
              changeStoryboard(activeSceneIndex.value + 1);
            }
          }, adjustedDuration);
        }
      }
      
      // 恢复背景音乐播放
      if (backgroundMusicPlayer.value) {
        console.log('恢复播放背景音乐');
        // 设置音量
        if (isIOS.value && audioContext.value && gainNode.value && backgroundMusicPlayer.value._isConnected) {
          // iOS设备且已连接Web Audio API
          setGainVolume(props.storyboards?.background_music?.volume || props.volumeLevel / 100);
        } else if (isIOS.value && audioContext.value && gainNode.value) {
          // iOS设备但尚未连接Web Audio API
          try {
            connectAudioSource(
              backgroundMusicPlayer.value, 
              props.storyboards?.background_music?.volume || props.volumeLevel / 100
            );
          } catch (error) {
            console.error('连接背景音乐失败，使用原生方法:', error);
            backgroundMusicPlayer.value.volume = props.storyboards?.background_music?.volume || props.volumeLevel / 100;
          }
        } else {
          // 非iOS设备
          backgroundMusicPlayer.value.volume = props.storyboards?.background_music?.volume || props.volumeLevel / 100;
        }
        
        // 播放
        const playPromise = backgroundMusicPlayer.value.play();
        if (playPromise !== undefined) {
          playPromise.catch(error => {
            console.error('恢复播放背景音乐失败:', error);
          });
        }
      }
    }
    
    isPlaying.value = true;
    // 显示控制栏，然后设置定时器使其自动隐藏
    showControls();
  }
  
  // 触发事件
  emit('update:isPlaying', isPlaying.value);
}

// 新增全屏相关逻辑
const storyPlayerRef = ref(null)
const isFullscreen = ref(false)

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 处理键盘快捷键
const handleKeyDown = (event) => {
  // 只有当组件处于焦点时才响应快捷键
  if (!storyPlayerRef.value || !storyPlayerRef.value.contains(document.activeElement)) {
    // 如果处于全屏模式，即使没有聚焦也能响应
    if (!isFullscreen.value) {
      return
    }
  }

  switch (event.key) {
    case ' ': // 空格键控制播放/暂停
      event.preventDefault()
      togglePlayPause()
      break
    case 'f': // f键切换全屏
    case 'F':
      event.preventDefault()
      toggleFullscreen()
      break
    case 'ArrowLeft': // 左箭头控制上一个场景
      event.preventDefault()
      playPrevScene()
      break
    case 'ArrowRight': // 右箭头控制下一个场景
      event.preventDefault()
      playNextScene()
      break
    case 'Escape': // ESC键退出全屏（大多数浏览器会自动处理）
      // 由浏览器默认处理
      break
    case '+': // 加号键增加播放速度
    case '=': // =键(不用Shift的加号)增加播放速度
      event.preventDefault()
      const currentIndex = playbackRates.indexOf(playbackRate.value)
      if (currentIndex < playbackRates.length - 1) {
        changePlaybackRate(playbackRates[currentIndex + 1])
      }
      break
    case '-': // 减号键减少播放速度
      event.preventDefault()
      const currentIdx = playbackRates.indexOf(playbackRate.value)
      if (currentIdx > 0) {
        changePlaybackRate(playbackRates[currentIdx - 1])
      }
      break
  }
}

const toggleFullscreen = () => {
  if (storyPlayerRef.value) {
    if (!isFullscreen.value) {
      storyPlayerRef.value.requestFullscreen().catch(err => {
        console.error('全屏切换失败:', err)
        ElMessage.error('全屏切换失败')
      })
    } else {
      document.exitFullscreen().catch(err => {
        console.error('退出全屏失败:', err)
        ElMessage.error('退出全屏失败')
      })
    }
  }
}

// 当activeSceneIndex变化时，重置显示的字幕
watch(activeSceneIndex, () => {
  displayedSubtitle.value = ''
})

// 当字幕内容变化时，重置显示的字幕
watch(currentSubtitle, () => {
  displayedSubtitle.value = ''
})

// 显示控制栏
const showControls = () => {
  isControlsVisible.value = true
  // 清除可能存在的定时器
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value)
    controlsTimer.value = null
  }

  // 设置定时器，当鼠标停止移动一段时间后自动隐藏控制栏
  controlsTimer.value = setTimeout(() => {
    // 如果鼠标不在控制栏上，则隐藏控制栏
    if (!isControlsHovered.value) {
      isControlsVisible.value = false
    }
  }, 1000) // 200毫秒后自动隐藏
}

// 隐藏控制栏
const hideControls = () => {
  // 如果鼠标悬停在控制栏上，不隐藏
  if (isControlsHovered.value) return

  // 直接隐藏控制栏，不使用延迟
  isControlsVisible.value = false

  // 清除可能存在的定时器
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value)
    controlsTimer.value = null
  }
}

// 当鼠标移入控制栏时，保持显示状态
const keepControlsVisible = () => {
  isControlsHovered.value = true
  showControls()
}

// 当鼠标移出控制栏时
const leaveControls = () => {
  isControlsHovered.value = false
  hideControls()
}

// 检测窗口大小变化，更新isWindowLandscape和objectFitStyle
const handleResize = () => {
  // 检测窗口宽高比
  isWindowLandscape.value = window.innerWidth > window.innerHeight

  // 根据窗口宽高比和imageSize设置objectFitStyle
  if (props.imageSize === '16:9') {
    // 如果图片是16:9（横向）
    objectFitStyle.value = isWindowLandscape.value ? 'cover' : 'contain'
  } else if (props.imageSize === '9:16') {
    // 如果图片是9:16（竖向）
    objectFitStyle.value = isWindowLandscape.value ? 'contain' : 'cover'
  } else {
    // 默认使用cover
    objectFitStyle.value = 'cover'
  }
}

// 监听volumeLevel属性变化
watch(() => props.volumeLevel, (newVolume) => {
  // 将百分比转换为0-1范围
  const normalizedVolume = newVolume / 100
  
  // 设置音频播放器音量(旁白)
  if (audioPlayer.value) {
    // 对于iOS设备，使用Web Audio API
    if (isIOS.value && narratorAudioContext.value && narratorGainNode.value) {
      setNarratorVolume(audioPlayer.value, normalizedVolume)
    } else {
      // 非iOS设备直接设置音量
      audioPlayer.value.volume = normalizedVolume
    }
  }
  
  // 同时更新背景音乐音量
  if (isIOS.value && audioContext.value && gainNode.value) {
    setGainVolume(normalizedVolume)
  } else if (backgroundMusicPlayer.value) {
    backgroundMusicPlayer.value.volume = normalizedVolume
  }
})

</script>

<style scoped>
.story-player {
  margin-top: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 不同比例容器样式 */
.story-player.ratio-16-9 {
  aspect-ratio: 16/9;
}

.story-player.ratio-9-16 {
  aspect-ratio: 9/16;
}

.story-player.ratio-1-1 {
  aspect-ratio: 1/1;
}

@media (max-width: 768px) {

  .story-player.ratio-16-9,
  .story-player.ratio-9-16,
  .story-player.ratio-1-1 {
    width: 100%;
    max-height: 100vh;
  }
}

/* 播放器容器 */
.player-container {
  background-color: #000;
  position: relative;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

/* 幻灯片显示区 */
.slide-display {
  position: relative;
  width: 100%;
  flex: 1;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
}

.image-loading-overlay {
  position: absolute;
  margin: auto;
}

.slide-image {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.slide-image img {
  width: 100%;
  height: 100%;
  /* object-fit属性现在通过动态样式绑定设置 */
  opacity: 0;
  will-change: transform, opacity;
  transition: opacity 1s ease-in-out;
}

.slide-image img.slide-active {
  opacity: 1;
}

.slide {
  width: 100%;
  height: 100%;
  /* object-fit: cover; */
  object-fit: contain;
  opacity: 0;
  will-change: transform, opacity;
  transition: opacity 1s ease-in-out;
}

.slide.active {
  opacity: 1;
}

.slide img {
  max-width: 90%;
  max-height: 80%;
  object-fit: contain;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
}

.placeholder-image {
  font-size: 60px;
  color: rgba(255, 255, 255, 0.2);
}

/* 覆盖层和字幕 */
.slide-overlay {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 20px;
  z-index: 2;
  text-align: center;
}

.story-title {
  color: white;
  text-shadow: 0 0 10px #ff3366;
  text-align: center;
  font-size: 52px;
  margin: 0px;
  text-shadow: 0 0 10px #ff3366;
}

.story-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 18px;
  max-width: 80%;
  margin: 20;
  margin-left: auto;
  margin-right: auto;
}

.subtitle-container {
  width: 100%;
  text-align: center;
  margin-bottom: 60px;
  min-height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.subtitle-text {
  display: inline-block;
  /* 移除背景色 */
  /* background-color: rgba(0, 0, 0, 0.509); */
  color: white;
  /* 调整padding，由于没有背景色，可以减小内边距 */
  padding: 8px 16px;
  /* 保留边框圆角，以便未来可能需要添加背景时样式一致 */
  border-radius: 12px;
  font-size: 38px;
  max-width: 90%;
  /* 增加最大宽度，确保多行文本有足够空间 */
  line-height: 1.6;
  /* 增加行高，改善多行文本的可读性 */
  transition: all 0.15s ease-out;
  /* 更快的过渡效果 */
  /* border-left: 3px solid rgba(255, 255, 255, 0.6); */
  text-align: center;
  /* 改为居中对齐，使多行文本更美观 */
  /* white-space: nowrap; */
  /* 修改为nowrap，防止文本自动换行 */
  /* overflow: hidden; */
  /* 隐藏溢出部分 */
  /* text-overflow: ellipsis; */
  /* 使用省略号表示溢出的文本 */
  min-height: 1.5em;
  /* 移除阴影 */
  /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); */
  letter-spacing: 0.3px;
  /* word-spacing: 1px; */
  /* 添加文字描边效果 */
  /* text-shadow: 
    -2px -2px 0 #000,  
    2px -2px 0 #000,
    -2px 2px 0 #000,
    2px 2px 0 #000,
    0 5px 8px rgba(0, 0, 0, 0.8); */
  text-shadow:
    1px 1px 2px rgb(254, 233, 0),
    0 0 6px rgb(0, 0, 0),
    0 0 4px blue;
  /* 增加字体粗细，使文字更清晰 */
  font-weight: 800;
  /* 添加多行文本间距 */
  margin: 0 auto;
}

.subtitle-text:empty {
  opacity: 0;
  transform: translateY(10px);
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.fade-enter-from {
  opacity: 0;
  transform: scale(1.05);
}

.fade-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* 大型播放按钮 */
.play-button-large {
  width: 100vw;
  height: 100vh;
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  /* z-index: 10; */
  cursor: pointer;
  transition: all 0.3s ease;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.478);
  backdrop-filter: blur(8px);
}

.play-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36px;
  color: white;
  transition: all 0.3s ease;
  margin: 10px;
}

.play-text {
  color: white;
  font-size: 18px;
  font-weight: 500;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.play-button-large:hover {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.05);
}

.play-button-large:hover .play-icon {
  transform: scale(1.1);
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

/* 播放控制栏 */
.player-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  /* background-color: rgba(0, 0, 0, 0.4); */
  /* backdrop-filter: blur(10px); */
  /* border-top: 1px solid rgba(255, 255, 255, 0.1); */
  height: 60px;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 20;
  transition: opacity 0.3s ease, transform 0.3s ease;
  opacity: 0;
  transform: translateY(100%);
  background: linear-gradient(to top, rgba(0, 0, 0, 0.558), transparent);
}

.player-controls.visible {
  opacity: 1;
  transform: translateY(0);
}

/* 当鼠标悬停在播放区域时显示控制栏 */
/* .slide-display:hover .player-controls {
  opacity: 1;
  transform: translateY(0);
} */

.progress-info {
  text-align: left;
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-info span {
  padding: 5px 10px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  font-weight: 500;
}

.progress-info span.progress-status {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  margin-left: 5px;
}

.playback-controls {
  display: flex;
  align-items: center;
  gap: 6px;
}

.control-btn {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border: none !important;
  cursor: pointer;
  height: 40px;
  width: 40px;
  border-radius: 50% !important;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.8) !important;
  transition: all 0.3s ease;
  padding: 0 !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  outline: none !important;
}

.control-btn:hover:not([disabled]) {
  background-color: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  transform: scale(1.05) translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.control-btn:active:not([disabled]) {
  transform: scale(0.95);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.control-btn[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.play-pause-btn {
  background-color: rgba(79, 70, 229, 0.8) !important;
  width: 50px !important;
  height: 50px !important;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4) !important;
}

.play-pause-btn:hover {
  background-color: rgba(79, 70, 229, 0.9) !important;
  box-shadow: 0 8px 20px rgba(79, 70, 229, 0.5) !important;
  transform: scale(1.1) translateY(-5px) !important;
}

.play-pause-btn:active {
  transform: scale(1) translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3) !important;
}

.scene_image-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  /* z-index: 5; */
  transition: opacity 0.3s ease;
}

.loading-icon {
  font-size: 48px;
  color: white;
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 幻灯片切换动画 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: scale(1.05);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

.slide-fade-enter-to,
.slide-fade-leave-from {
  opacity: 1;
  transform: scale(1);
}

.control-btn :deep(.el-icon) {
  font-size: 24px;
}

.play-pause-btn :deep(.el-icon) {
  font-size: 24px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .slide-display {
    height: 300px;
  }

  .story-title {
    font-size: 24px;
  }

  .story-subtitle {
    font-size: 14px;
  }

  .subtitle-text {
    font-size: 16px;
  }

  /* 在移动设备上隐藏全屏按钮 */
  .fullscreen-btn {
    display: none !important;
  }
}

/* 移除原有的 zooming 动画 */
.slide-image img.zooming {
  animation: none;
}

/* 相机效果动画 */
.camera-zoom-in {
  animation: zoomIn var(--duration, 10s) linear forwards;
}

.camera-zoom-out {
  animation: zoomOut var(--duration, 10s) linear forwards;
}

.camera-pan-right {
  animation: panRight var(--duration, 10s) linear forwards;
}

.camera-pan-left {
  animation: panLeft var(--duration, 10s) linear forwards;
}

.camera-tilt-up {
  animation: tiltUp var(--duration, 10s) linear forwards;
}

.camera-tilt-down {
  animation: tiltDown var(--duration, 10s) linear forwards;
}

/* 动画关键帧定义 */
@keyframes zoomIn {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(1.2);
  }
}

@keyframes zoomOut {
  0% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes panRight {
  0% {
    transform: translateX(0) scale(1);
  }

  100% {
    transform: translateX(-10%) scale(1.2);
  }
}

@keyframes panLeft {
  0% {
    transform: translateX(0) scale(1);
  }

  100% {
    transform: translateX(10%) scale(1.2);
  }
}

@keyframes tiltUp {
  0% {
    transform: translateY(0) scale(1);
  }

  100% {
    transform: translateY(10%) scale(1.2);
  }
}

@keyframes tiltDown {
  0% {
    transform: translateY(0) scale(1);
  }

  100% {
    transform: translateY(-10%) scale(1.2);
  }
}

.cost-info,
.time-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  color: #1e293b;
}

.cost-amount {
  font-size: 24px;
  font-weight: 600;
  color: #4f46e5;
}

.time-amount {
  font-weight: 500;
  color: #64748b;
}

.balance-info {
  font-size: 14px;
  color: #64748b;
  margin-top: 8px;
}

.balance-amount {
  color: #4f46e5;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .generate-button {
    width: 100%;
    justify-content: center;
  }
}

.generate-home-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 15;
  transition: all 0.3s ease;
}

.generate-video-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 15;
  transition: all 0.3s ease;
}

.generate-video-share {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 15;
  transition: all 0.3s ease;
}

.generate-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: none;
  padding: 12px 14px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  border-radius: 12px;
  color: #ffffff;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.239);
  letter-spacing: 0.5px;
  opacity: 0.95;
  min-width: 110px;
}

.video-button {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
}

.share-button {
  background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
}

.home-button {
  background: linear-gradient(135deg, #0000003e 0%, #0000002c 100%);
}

.generate-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(99, 102, 241, 0.35);
  opacity: 1;
}

.generate-button:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.2);
}

.generate-button .el-icon {
  font-size: 18px;
  margin-right: 2px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .generate-button {
    padding: 10px 16px;
    font-size: 13px;
    min-width: 90px;
  }

  .generate-video-btn,
  .generate-video-share {
    top: 15px;
  }

  .generate-video-btn {
    right: 15px;
  }

  .generate-video-share {
    left: 15px;
  }
}

@media (max-width: 480px) {
  .generate-button {
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 8px;
    min-width: auto;
  }

  .generate-button .el-icon {
    font-size: 16px;
  }
}

/* 封面样式 */
.cover-display {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  background-color: #000;
  /* z-index: 10; */
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.8;
  transition: opacity 0.3s ease;
  scale: 1.1;
  /* object-fit: cover; */
  object-fit: contain;
}

.cover-display:hover .cover-image {
  opacity: 0.9;
}

.cover-content {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #00000086;
  text-align: center;
  color: white;
  /* z-index: 31; */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: transform 0.6s ease, box-shadow 0.6s ease;
  backdrop-filter: blur(4px);
}

.cover-display:hover .cover-content {
  transform: scale(1.2);
  box-shadow: 0 22px 60px rgba(0, 0, 0, 0.726);
}

.cover-title {
  color: white;
  text-shadow: 0 0 10px #ff3366;
  text-align: center;
  font-size: 52px;
  margin: 0px;
  text-shadow: 0 0 10px #ff3366;
}

.cover-subtitle2 {
  font-size: 24px;
  margin-bottom: 15px;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.cover-subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 10px;
  font-style: italic;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .cover-title {
    font-size: 32px;
  }

  .cover-subtitle2 {
    font-size: 18px;
  }

  .cover-subtitle {
    font-size: 16px;
  }

  .cover-content {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .cover-title {
    font-size: 24px;
  }

  .cover-subtitle2 {
    font-size: 16px;
  }

  .cover-subtitle {
    font-size: 14px;
  }
}

/* 针对移动设备的全屏优化 */
@media (max-width: 768px) {

  :fullscreen .subtitle-text,
  :-webkit-full-screen .subtitle-text {
    font-size: 18px;
    max-width: 90%;
    /* 调整移动设备全屏模式下的描边效果 */
    text-shadow:
      -1px -1px 0 #000,
      1px -1px 0 #000,
      -1px 1px 0 #000,
      1px 1px 0 #000,
      0 2px 4px rgba(0, 0, 0, 0.8);
    /* 调整移动设备全屏模式下的行高 */
    line-height: 1.6;
  }

  .subtitle-text {
    font-size: 24px;
    /* 移动设备上使用较小的字体 */
    max-width: 95%;
    /* 在移动设备上增加最大宽度 */
    line-height: 1.5;
    /* 调整移动设备上的描边效果 */
    text-shadow:
      -1px -1px 0 #000,
      1px -1px 0 #000,
      -1px 1px 0 #000,
      1px 1px 0 #000,
      0 2px 4px rgba(0, 0, 0, 0.8);
    /* 确保移动设备上也是单行显示 */
    /* white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; */
  }

  .subtitle-container {
    margin-bottom: 40px;
    /* 减少底部边距 */
  }

  :fullscreen .player-controls,
  :-webkit-full-screen .player-controls {
    padding: 15px 20px;
    height: 60px;
  }

  :fullscreen .control-btn,
  :-webkit-full-screen .control-btn {
    height: 40px;
    width: 40px;
  }

  :fullscreen .play-pause-btn,
  :-webkit-full-screen .play-pause-btn {
    width: 50px !important;
    height: 50px !important;
  }
}

/* 针对超小屏幕设备的优化 */
@media (max-width: 480px) {
  .subtitle-text {
    font-size: 20px;
    line-height: 1.4;
    max-width: 98%;
  }

  .subtitle-container {
    margin-bottom: 30px;
  }
}

/* 播放倍速相关样式 */
.speed-dropdown {
  margin: 6px;
}

.speed-btn {
  font-weight: 500 !important;
  font-size: 12px !important;
  width: auto !important;
  min-width: 40px !important;
  min-height: 40px !important;
  background-color: rgba(255, 255, 255, 0.15) !important;
}

:deep(.el-dropdown-menu__item.active-rate) {
  color: #4f46e5;
  font-weight: bold;
  background-color: rgba(79, 70, 229, 0.1);
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: rgba(79, 70, 229, 0.05);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .speed-btn {
    min-width: 40px !important;
    font-size: 13px !important;
  }
}
</style>