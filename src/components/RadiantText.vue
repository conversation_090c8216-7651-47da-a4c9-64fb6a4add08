<template>
  <div class="relative inline-block overflow-hidden">
    <span
      ref="textRef"
      class="relative inline-block text-transparent bg-clip-text"
      :class="textClasses"
      :style="textStyle"
    >
      <slot></slot>
    </span>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { cn } from '../lib/utils';
import { useMouse, useElementBounding } from '@vueuse/core';

const props = defineProps({
  colors: {
    type: Array,
    default: () => ['#0EA5E9', '#5B21B6', '#0F172A', '#1E40AF']
  },
  interactive: {
    type: Boolean,
    default: false
  },
  direction: {
    type: String,
    default: 'br', // 'br', 'bl', 'tr', 'tl'
    validator: (val) => ['br', 'bl', 'tr', 'tl'].includes(val)
  },
  size: {
    type: String,
    default: 'md', // 'sm', 'md', 'lg', 'xl', '2xl'
    validator: (val) => ['sm', 'md', 'lg', 'xl', '2xl'].includes(val)
  },
  weight: {
    type: String,
    default: 'bold', // 'normal', 'semibold', 'bold', 'extrabold'
    validator: (val) => ['normal', 'semibold', 'bold', 'extrabold'].includes(val)
  }
});

const textRef = ref(null);
const { x: mouseX, y: mouseY } = useMouse();
const elementBounding = useElementBounding(textRef);
const gradientCenter = reactive({ x: 50, y: 50 });

// Compute the size class based on the size prop
const sizeClass = computed(() => {
  const sizes = {
    sm: 'text-sm',
    md: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-4xl',
    '2xl': 'text-5xl'
  };
  return sizes[props.size] || sizes.md;
});

// Compute the weight class based on the weight prop
const weightClass = computed(() => {
  const weights = {
    normal: 'font-normal',
    semibold: 'font-semibold',
    bold: 'font-bold',
    extrabold: 'font-extrabold'
  };
  return weights[props.weight] || weights.bold;
});

// Combine all the classes
const textClasses = computed(() => {
  return cn(
    sizeClass.value,
    weightClass.value,
    'select-none'
  );
});

// Update gradient center based on mouse position if interactive
onMounted(() => {
  if (props.interactive) {
    setInterval(() => {
      if (elementBounding.left.value && elementBounding.top.value) {
        const x = ((mouseX.value - elementBounding.left.value) / elementBounding.width.value) * 100;
        const y = ((mouseY.value - elementBounding.top.value) / elementBounding.height.value) * 100;
        
        gradientCenter.x = Math.max(0, Math.min(100, x));
        gradientCenter.y = Math.max(0, Math.min(100, y));
      }
    }, 50);
  }
});

// Compute the final text style with gradient
const textStyle = computed(() => {
  let gradientDirection;
  
  // Determine gradient direction based on prop or interactive mode
  if (props.interactive) {
    gradientDirection = `circle at ${gradientCenter.x}% ${gradientCenter.y}%`;
  } else {
    switch (props.direction) {
      case 'br': gradientDirection = 'to bottom right'; break;
      case 'bl': gradientDirection = 'to bottom left'; break;
      case 'tr': gradientDirection = 'to top right'; break;
      case 'tl': gradientDirection = 'to top left'; break;
      default: gradientDirection = 'to bottom right';
    }
  }
  
  return {
    backgroundImage: `radial-gradient(${gradientDirection}, ${props.colors.join(', ')})`,
  };
});
</script> 