<template>
  <div class="canvas-assets">
    <!-- 加载中状态 -->
    <div v-if="isLoading" class="asset-loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载画布资产中...</div>
    </div>
    
    <!-- 空数据状态 -->
    <!-- <div v-else-if="!hasAssets" class="empty-state final-empty-state">
      <el-icon class="empty-icon"><Edit /></el-icon>
      <div class="empty-text">暂无画布资产</div>
      <div class="empty-desc">您可以点击"新建画布"创建您的第一个视频画布</div>
    </div> -->
    
    <!-- 画布资产内容 -->
    <div v-else class="asset-content">
      <transition-group name="canvas-item" tag="div" class="canvas-grid-container">
        <!-- 新建画布卡片 -->
        <el-card key="create-new" class="canvas-card create-canvas-card" shadow="hover" @click="createNewCanvas">
          <div class="create-canvas-content">
            <div class="create-icon-container">
              <el-icon class="create-icon"><Plus /></el-icon>
            </div>
            <div class="create-text">新建视频草稿</div>
          </div>
        </el-card>
        
        <!-- 画布资产列表 -->
        <el-card v-for="(canvas, index) in canvases" :key="index" class="canvas-card" shadow="hover" @click="handleCanvasClick(canvas)">
          <div class="canvas-card-content">
            <!-- 删除按钮 -->
            <div class="delete-icon-container" @click.stop="confirmDelete(canvas)">
              <el-icon class="delete-icon"><Delete /></el-icon>
            </div>
            
            <!-- 画布预览 -->
            <div class="canvas-image-container">
              <div class="canvas-preview">
                <!-- <div class="canvas-type">{{ canvas.type }}</div> -->
                 <img v-if="canvas.thumbnail" :src="canvas.thumbnail" alt="画布封面" class="canvas-preview-image">
              </div>
            </div>
            
            <!-- 画布信息 -->
            <div class="canvas-info">
              <div class="canvas-header">
                <h3 class="canvas-name">{{ canvas.name || '未命名画布' }}</h3>
              </div>
              <div class="canvas-meta">
                <span class="canvas-date">编辑于: {{ formatDate(canvas.updateTime) }}</span>
                <span class="canvas-dimensions">{{ canvas.ratio }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </transition-group>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Edit, Plus, Picture, Delete } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';
import { getCanvasList, createCanvas, deleteCanvas } from '@/api/auth.js';
import { ElMessageBox, ElMessage } from 'element-plus';

// 路由实例
const router = useRouter();

// 组件属性
const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false
  }
});

// 状态变量
const hasAssets = ref(false);
const canvases = ref([]);

// 定义事件
const emit = defineEmits(['create-canvas', 'update:isLoading']);

// 创建新画布
const createNewCanvas = async () => {
  try {
    emit('update:isLoading', true);
    const response = await createCanvas();
    if (response.success) {
      const canvasId = response.data;
      console.log('创建画布成功，ID:', canvasId);
      // 跳转到视频编辑器页面，并传递画布ID
      router.push({
        path: '/video-editor',
        query: { canvasId }
      });
    } else {
      console.error('创建画布失败:', response.errMessage);
    }
  } catch (error) {
    console.error('创建画布异常:', error);
  } finally {
    emit('update:isLoading', false);
  }
};

const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()

  // 获取今天的开始时间（00:00:00）
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  // 获取昨天的开始时间（00:00:00）
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)

  // 检查是否是今天
  if (date >= today) {
    return `今天 ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
  }

  // 检查是否是昨天
  if (date >= yesterday && date < today) {
    return `昨天 ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
  }

  // 其他日期显示完整日期
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 模拟加载画布数据
onMounted(() => {
  // 使用API获取画布列表
  fetchCanvasList();
});

// 获取画布列表
const fetchCanvasList = async () => {
  try {
    const loading = props.isLoading !== undefined ? props.isLoading : true;
    if (!loading) {
      emit('update:isLoading', true);
    }
    
    const params = {
      pageNum: 1,
      pageSize: 40
    };
    
    const response = await getCanvasList(params);
    if (response.success) {
      canvases.value = response.data.map(item => ({
        id: item.id,
        code: item.code,
        name: item.canvasName,
        description: item.canvasDesc,
        thumbnail: item.coverImage || '',
        // type: '视频画布',
        createTime: item.createTime,
        updateTime: item.updateTime,
        ratio: item.ratio
      }));
      hasAssets.value = canvases.value.length > 0;
      console.log('获取画布列表成功:', canvases.value.length);
    } else {
      console.error('获取画布列表失败:', response.errMessage);
    }
  } catch (error) {
    console.error('获取画布列表异常:', error);
  } finally {
    if (props.isLoading !== undefined) {
      emit('update:isLoading', false);
    }
  }
};

// 点击画布项
const handleCanvasClick = (canvas) => {
  console.log('点击画布:', canvas.id);
  // 跳转到视频编辑器页面，并传递画布ID
  router.push({
    path: '/video-editor',
    query: { canvasId: canvas.id }
  });
};

// 确认删除画布
const confirmDelete = (canvas) => {
  ElMessageBox.confirm(
    `确定要删除画布"${canvas.name || '未命名画布'}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      deleteCanvasItem(canvas.id);
    })
    .catch(() => {
      // 用户取消删除操作
    });
};

// 删除画布
const deleteCanvasItem = async (canvasId) => {
  try {
    emit('update:isLoading', true);
    const response = await deleteCanvas(canvasId);
    if (response.success) {
      ElMessage.success('画布删除成功');
      // 重新获取画布列表
      fetchCanvasList();
    } else {
      ElMessage.error(`删除失败: ${response.errMessage}`);
    }
  } catch (error) {
    console.error('删除画布异常:', error);
    ElMessage.error('删除画布失败，请稍后重试');
  } finally {
    emit('update:isLoading', false);
  }
};
</script>

<style scoped>
.canvas-assets {
  position: relative;
  width: 100%;
}

.asset-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  background-color: #f9fafc;
  border-radius: 8px;
  margin-bottom: 0;
  height: 300px;
  transition: background-color 0.3s;
}

body.dark .asset-loading {
  background-color: var(--bg-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(64, 158, 255, 0.2);
  border-top-color: #409eff;
  border-radius: 50%;
  animation: spin 1s infinite linear;
  margin-bottom: 10px;
}

body.dark .loading-spinner {
  border: 3px solid rgba(64, 158, 255, 0.1);
  border-top-color: var(--primary-color);
}

.loading-text {
  color: #909399;
  font-size: 14px;
  transition: color 0.3s;
}

body.dark .loading-text {
  color: var(--text-secondary);
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 160px);
  padding: 20px;
  text-align: center;
  border-radius: 12px;
}

.empty-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
  animation: pulse 2s infinite ease-in-out;
}

body.dark .empty-icon {
  color: #606266;
}

.empty-text {
  font-size: 18px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

body.dark .empty-text {
  color: #e0e0e0;
}

.empty-desc {
  font-size: 14px;
  color: #909399;
}

body.dark .empty-desc {
  color: #a0a0a0;
}

/* 添加动画效果 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

/* 特殊空状态样式 */
.final-empty-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
}

.final-empty-state .empty-icon {
  font-size: 64px;
  color: #6365f1ca;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: floating 3s ease-in-out infinite;
}

.final-empty-state .empty-text {
  font-size: 22px;
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 10px;
}

.final-empty-state .empty-desc {
  font-size: 16px;
  color: #6b7280;
  max-width: 400px;
  line-height: 1.5;
}

body.dark .final-empty-state .empty-text {
  color: #e5e7eb;
}

body.dark .final-empty-state .empty-desc {
  color: #9ca3af;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0);
    scale: 1;
  }
  50% {
    transform: translateY(-15px);
    scale: 1.05;
  }
}

.asset-content {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 4px;
  padding: 0px 10px;
}

.canvas-grid-container {
  display: contents;
}

/* 卡片进场动画 */
.canvas-item-enter-active {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.canvas-item-leave-active {
  transition: all 0.4s cubic-bezier(0.55, 0.085, 0.68, 0.53);
  /* 移除 position: absolute 避免全屏闪烁 */
  z-index: 1;
}

.canvas-item-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
}

.canvas-item-leave-to {
  opacity: 0;
  transform: scale(0.8);
  filter: blur(2px);
}

/* 删除时的移动动画 */
.canvas-item-move {
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 为Grid布局中的卡片添加不同的动画延迟 */
.asset-content > .canvas-card:nth-child(3n+1) {
  transition-delay: 0.05s;
}
.asset-content > .canvas-card:nth-child(3n+2) {
  transition-delay: 0.1s;
}
.asset-content > .canvas-card:nth-child(3n+3) {
  transition-delay: 0.15s;
}

.canvas-card {
  cursor: pointer;
  transition: transform 0.3s ease, background-color 0.3s, box-shadow 0.3s;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  --el-card-padding: 0px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 14px #7b7dfc52;
  animation: card-appear 0.5s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes card-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.canvas-card:hover {
  transform: scale(1.03);
  z-index: 2;
  box-shadow: 0 8px 20px rgba(123, 125, 252, 0.4);
}

.canvas-card-content {
  display: flex;
  flex-direction: column;
  min-height: 30px;
  position: relative;
}

/* 删除图标容器 */
.delete-icon-container {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
}

.canvas-card:hover .delete-icon-container {
  opacity: 1;
}

.delete-icon-container:hover {
  background-color: rgba(244, 67, 54, 0.8);
}

.delete-icon {
  color: white;
  font-size: 16px;
}

.canvas-image-container {
  min-width: 100px;
  position: relative;
  overflow: hidden;
  background-color: #f1f5f9;
  transition: background-color 0.3s;
  margin: 10px;
  border-radius: 8px;
  aspect-ratio: 4/3;
  /* padding-top: 48.25%; */
}

body.dark .canvas-image-container {
  background-color: var(--bg-tertiary);
}

.canvas-preview {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-color: #f5f7fa;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
}

body.dark .canvas-preview {
  background-color: var(--bg-tertiary);
}

.canvas-preview-image{
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  transition: 0.8s all;
  object-position: center center;
}

.canvas-preview-image:hover {
  object-position: left top;
}

.canvas-type {
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.canvas-info {
  flex: 1;
  padding: 0px 10px 10px 10px;
  display: flex;
  flex-direction: column;
  min-width: 0;
  gap: 2px;
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: nowrap;
}

.canvas-name {
  font-size: 12px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  text-align: left;
  flex: 1;
  min-width: 0;
  margin-right: 10px;
  transition: color 0.3s;
}

body.dark .canvas-name {
  color: var(--text-primary);
}

.canvas-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
  margin-top: auto;
}

.canvas-date, .canvas-dimensions {
  font-size: 10px;
  color: #9eafc6;
  text-align: left;
  transition: color 0.3s;
}

body.dark .canvas-meta,
body.dark .canvas-date,
body.dark .canvas-dimensions {
  color: var(--text-secondary);
}

/* 新建画布卡片样式 */
.create-canvas-card {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(138, 92, 246, 0.05) 100%);
  border: 2px dashed #6366f1;
  box-shadow: none;
  transition: all 0.3s ease;
}

.create-canvas-card:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(138, 92, 246, 0.1) 100%);
  border-color: #818cf8;
  transform: translateY(-5px);
}

.create-canvas-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
}

.create-icon-container {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  box-shadow: 0 10px 15px -3px rgba(99, 102, 241, 0.3);
  transition: transform 0.3s ease;
}

.create-canvas-card:hover .create-icon-container {
  transform: scale(1.1) rotate(90deg);
}

.create-icon {
  font-size: 30px;
  color: white;
}

.create-text {
  font-size: 16px;
  font-weight: 600;
  color: #6366f1;
  transition: color 0.3s;
}

body.dark .create-canvas-card {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(138, 92, 246, 0.1) 100%);
  border: 2px dashed #818cf8;
}

body.dark .create-text {
  color: #818cf8;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式布局 */
@media (max-width: 768px) {
  .asset-content {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 10px;
  }
  
  .canvas-preview {
    height: 140px;
  }
  
  .create-canvas-content {
    padding: 20px 10px;
  }
  
  .create-icon-container {
    width: 50px;
    height: 50px;
  }
  
  .create-icon {
    font-size: 24px;
  }
  
  .create-text {
    font-size: 14px;
  }
}
</style> 