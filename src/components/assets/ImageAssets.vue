<template>
  <div class="image-assets">
    <!-- 筛选和分类控件 -->
    <div class="filter-container">
      <div class="filter-options">
        <div v-for="item in filterTypes" :key="item.value" class="filter-option"
          :class="{ 'active': filterType === item.value }" @click="filterType = item.value">
          {{ item.label }}
        </div>
      </div>

      <div style="flex: 1;"></div>
      
      <!-- 多选操作栏 -->
      <div class="multi-select-toolbar" v-if="isMultiSelectMode">
        <div class="selected-count">
          <!-- <div class="custom-checkbox" @click="handleSelectAll(!isAllSelected)">
            <div class="checkbox-inner" :class="{ 'checked': isAllSelected }">
              <el-icon v-if="isAllSelected" class="check-icon">
                <Check />
              </el-icon>
            </div>
            <span class="checkbox-label">全选</span>
          </div> -->
          <span v-if="selectedItems.length > 0" class="selected-info">已选择 {{ selectedItems.length }} 项</span>
        </div>
        <div class="toolbar-actions" v-if="selectedItems.length > 0">
          <div class="filter-option action-option download" @click="batchDownload">
            <div class="filter-option-content">
              <el-icon class="option-icon">
                <Download />
              </el-icon>
              <span>下载</span>
            </div>
          </div>
          <div class="filter-option action-option favorite" @click="batchFavorite">
            <div class="filter-option-content">
              <el-icon class="option-icon">
                <Star v-if="hasFavoriteToAdd" />
                <StarFilled v-else />
              </el-icon>
              <span>{{ hasFavoriteToAdd ? '收藏' : '取消收藏' }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="filter-actions">
        <div class="filter-option" :class="{ 'active': isMultiSelectMode }" @click="toggleMultiSelectMode">
          <div class="filter-option-content">
            <el-icon v-if="isMultiSelectMode" class="option-icon">
              <Close />
            </el-icon>
            <el-icon v-else class="option-icon"><Select /></el-icon>
            <span>{{ isMultiSelectMode ? '取消选择' : '批量选择' }}</span>
          </div>
        </div>
      </div>
    </div>


    <!-- 加载中状态 -->
    <div v-if="loading" class="asset-loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载{{ '图片' }}资产中...</div>
    </div>

    <!-- 空数据状态 -->
    <div v-else-if="!hasAssets" class="empty-state final-empty-state">
      <el-icon class="empty-icon">
        <Picture />
      </el-icon>
      <div class="empty-text">暂无{{ '图片' }}资产</div>
      <div class="empty-desc">您可以在创作过程中生成{{ '图片' }}资产</div>
    </div>

    <!-- 资产内容 -->
    <div v-else class="asset-content-wrapper" ref="contentWrapper">
      <div class="asset-content">
        <div v-for="(item, index) in items" :key="item.id || index" class="asset-item" :style="{ animationDelay: index * 0.01 + 's' }">
          <div class="asset-card" :class="{ 'selected': isItemSelected(item) }" @click="handleItemClick(item)" :data-id="item.id || item.imageUrl">
            <div class="asset-preview" :class="{ 'image-error': item.imageError }">
              <!-- 多选状态下的选择框 -->
              <div v-if="isMultiSelectMode" class="select-checkbox" @click.stop="toggleItemSelection(item)">
                <div class="checkbox-inner" :class="{ 'checked': item.selected }">
                  <el-icon v-if="item.selected" class="check-icon">
                    <Check />
                  </el-icon>
                </div>
              </div>

              <!-- 图片加载错误显示占位符 -->
              <div v-if="item.imageError" class="image-placeholder">
                <el-icon>
                  <Picture />
                </el-icon>
              </div>

              <!-- 图片加载错误处理 -->
              <img v-if="!item.imageError" :src="item.imageUrl + '?x-oss-process=image/resize,h_200'"
                :class="['hidden-image', item.imageLoaded ? 'loaded' : '']" 
                @error="handleImageError(item)" 
                @load="handleImageLoaded(item)" 
                alt="" @click="handleImageClick(item)" />

              <!-- 收藏图标 -->
              <div class="favorite-icon" :class="{ 'always-visible': item.isFavorite }"
                @click.stop="toggleFavorite(item)">
                <el-icon v-if="item.isFavorite">
                  <StarFilled />
                </el-icon>
                <el-icon v-else>
                  <Star />
                </el-icon>
              </div>
            </div>
            
            <!-- 底部信息栏 -->
            <div class="asset-info-bar">
              <div class="asset-prompt" :title="item.prompt">{{ item.prompt || '无提示词' }}</div>
              <!-- <div class="asset-aspect-ratio">{{ item.aspectRatio || '未知比例' }}</div> -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页控件 -->
    <div class="pagination-container" v-if="totalCount > 0">
      <el-pagination v-model:current-page="pageNum" v-model:page-size="pageSize" :page-sizes="pageSizeOptions"
        layout="total, sizes, prev, pager, next, jumper" :total="totalCount" @size-change="handleSizeChange"
        @current-change="handlePageChange" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, onUnmounted, nextTick } from 'vue';
import { Picture, User, Download, Select, Close, Check } from '@element-plus/icons-vue';
import { getUserImage, addFavorite, removeFavorite } from '@/api/auth.js';
import { ElMessage, ElPagination, ElSelect, ElOption, ElButton, ElCheckbox } from 'element-plus';
import { Star, StarFilled } from '@element-plus/icons-vue';

// 组件属性
const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false
  },
});

// 状态变量
const hasAssets = ref(false);
const items = ref([]);
const loading = ref(false);
const contentWrapper = ref(null);

// 多选相关状态
const isMultiSelectMode = ref(false);
const selectedItems = computed(() => items.value.filter(item => item.selected));
const isAllSelected = computed({
  get: () => {
    return items.value.length > 0 && items.value.every(item => item.selected);
  },
  set: (value) => {
    // 这个 setter 是为了支持 v-model，实际逻辑在 handleSelectAll 中
  }
});

// 计算是否有可收藏的项目
const hasFavoriteToAdd = computed(() => {
  return selectedItems.value.some(item => !item.isFavorite);
});

// 分页参数
const pageNum = ref(1);
const pageSize = ref(70);
const totalCount = ref(0);
const totalPages = ref(0);
const pageSizeOptions = ref([70, 88, 100]);

// 筛选参数
const filterType = ref(3); // 1-全部 2-收藏 3-角色 4-分镜
const filterTypes = computed(() => {
  return [
    { value: 1, label: '全部' },
    { value: 3, label: '角色' },
    { value: 4, label: '分镜' },
    { value: 2, label: '收藏' },
  ];
});

// 切换多选模式
const toggleMultiSelectMode = () => {
  isMultiSelectMode.value = !isMultiSelectMode.value;

  // 退出多选模式时，清除所有选中状态
  if (!isMultiSelectMode.value) {
    clearAllSelections();
  }
};

// 清除所有选中状态
const clearAllSelections = () => {
  items.value.forEach(item => {
    item.selected = false;
  });
};

// 处理全选/取消全选
const handleSelectAll = (value) => {
  items.value.forEach(item => {
    item.selected = value;
  });
};

// 检查项目是否被选中
const isItemSelected = (item) => {
  return item.selected;
};

// 切换项目选中状态
const toggleItemSelection = (item) => {
  item.selected = !item.selected;
  
  // 添加选择波纹动画效果
  if (item.selected) {
    const card = document.querySelector(`[data-id="${item.id || item.imageUrl}"]`);
    if (card) {
      // 创建波纹元素
      const ripple = document.createElement('div');
      ripple.className = 'selection-ripple';
      card.appendChild(ripple);
      
      // 动画结束后移除波纹
      setTimeout(() => {
        card.removeChild(ripple);
      }, 700);
    }
  }
};

// 处理项目点击
const handleItemClick = (item) => {
  if (isMultiSelectMode.value) {
    toggleItemSelection(item);
  } else {
    // 非多选模式下的点击处理，可以添加预览等功能
    console.log('预览图片:', item);
  }
};

// 批量下载
const batchDownload = () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请先选择要下载的图片');
    return;
  }

  // 这里实现批量下载逻辑
  ElMessage.success(`正在下载 ${selectedItems.value.length} 张图片`);

  // 处理单张图片下载
  const downloadSingleImage = (imageUrl, fileName) => {
    // 使用 fetch API 处理可能的跨域问题
    fetch(imageUrl, {
      headers: new Headers({
        'Origin': window.location.origin
      }),
      mode: 'cors',
    })
      .then(response => response.blob())
      .then(blob => {
        // 创建 blob URL
        const blobUrl = window.URL.createObjectURL(blob);

        // 创建一个隐藏的 a 标签进行下载
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = fileName || `image-${Date.now()}.jpg`;
        document.body.appendChild(link);
        link.click();

        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(blobUrl);
      })
      .catch(error => {
        console.error('下载图片失败:', error);
        ElMessage.error(`下载图片失败: ${error.message}`);
      });
  };

  // 如果图片数量较多，可能需要分批下载以避免浏览器限制
  const batchSize = 3; // 每批下载的图片数量
  const delay = 1000; // 每批之间的延迟（毫秒）

  // 分批下载
  const downloadBatch = (startIndex) => {
    const endIndex = Math.min(startIndex + batchSize, selectedItems.value.length);

    for (let i = startIndex; i < endIndex; i++) {
      const item = selectedItems.value[i];
      const fileName = item.name || `image-${i}-${Date.now()}.jpg`;
      downloadSingleImage(item.imageUrl, fileName);
    }

    // 如果还有更多图片需要下载，设置延迟后下载下一批
    if (endIndex < selectedItems.value.length) {
      setTimeout(() => {
        downloadBatch(endIndex);
      }, delay);
    }
  };

  // 开始下载第一批
  downloadBatch(0);

  toggleMultiSelectMode();
};

// 批量收藏
const batchFavorite = async () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请先选择要操作的图片');
    return;
  }

  try {
    loading.value = true;
    
    // 根据是否有未收藏的项目决定是批量收藏还是批量取消收藏
    if (hasFavoriteToAdd.value) {
      // 筛选出未收藏的项目
      const itemsToFavorite = selectedItems.value.filter(item => !item.isFavorite);
      
      if (itemsToFavorite.length === 0) {
        ElMessage.info('所选图片已全部收藏');
        return;
      }
      
      // 构建API参数
      const resourceIds = itemsToFavorite.map(item => item.id);
      
      // 调用批量添加收藏API
      const response = await addFavorite({
        resourceIds,
        resourceType: 2 // 2表示图片资源
      });
      
      if (response.success) {
        // 更新本地状态
        itemsToFavorite.forEach(item => {
          item.isFavorite = true;
        });
        
        ElMessage.success(`成功收藏 ${itemsToFavorite.length} 张图片`);
      } else {
        ElMessage.error(response.errMessage || '收藏失败');
      }
    } else {
      // 全部是已收藏的项目，执行批量取消收藏
      const itemsToUnfavorite = selectedItems.value.filter(item => item.isFavorite);
      
      if (itemsToUnfavorite.length === 0) {
        ElMessage.info('所选图片均未收藏');
        return;
      }
      
      // 构建API参数
      const resourceIds = itemsToUnfavorite.map(item => item.id);
      
      // 调用批量移除收藏API
      const response = await removeFavorite({
        resourceIds
      });
      
      if (response.success) {
        // 更新本地状态
        itemsToUnfavorite.forEach(item => {
          item.isFavorite = false;
        });
        
        ElMessage.success(`已取消收藏 ${itemsToUnfavorite.length} 张图片`);
        
        // 如果当前是在"收藏"筛选下，取消收藏后需要重新加载数据
        if (filterType.value === 2) {
          fetchAssets();
        }
      } else {
        // 恢复原状态
        itemsToUnfavorite.forEach(item => {
          item.isFavorite = !item.isFavorite;
        });
        ElMessage.error(response.errMessage || '取消收藏失败');
      }
    }
  } catch (error) {
    console.error('批量操作收藏状态失败:', error);
    ElMessage.error('操作失败，请稍后重试');
  } finally {
    loading.value = false;
    toggleMultiSelectMode();
  }
};

// 窗口大小变化处理函数
let resizeTimeout = null;
const handleResize = () => {
  // 使用防抖处理窗口大小变化
  if (resizeTimeout) {
    clearTimeout(resizeTimeout);
  }
};

// 获取资产数据
const fetchAssets = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      type: filterType.value
    };

    const response = await getUserImage(params);

    if (response.success) {
      // 添加图片错误状态属性、选中状态属性和加载状态属性
      const assetsWithErrorState = (response.data || []).map(item => ({
        ...item,
        imageError: false,
        selected: false,
        imageLoaded: false
      }));

      items.value = assetsWithErrorState;
      totalCount.value = response.totalCount || 0;
      totalPages.value = response.totalPages || 0;
      hasAssets.value = items.value.length > 0;
    } else {
      ElMessage.error(response.errMessage || `获取${'图片'}数据失败`);
      items.value = [];
      hasAssets.value = false;
    }
  } catch (error) {
    console.error(`获取${'图片'}数据出错:`, error);
    ElMessage.error(`获取${'图片'}数据时发生错误`);
    items.value = [];
    hasAssets.value = false;
  } finally {
    loading.value = false;
  }
};

// 处理图片加载错误
const handleImageError = (item) => {
  item.imageError = true;
};

// 处理图片加载完成
const handleImageLoaded = (item) => {
  item.imageLoaded = true;
};

// 切换收藏状态
const toggleFavorite = async (item) => {
  try {
    const isFavorite = !item.isFavorite;
    
    // 乐观更新UI
    item.isFavorite = isFavorite;
    
    if (isFavorite) {
      // 添加收藏
      const response = await addFavorite({
        resourceIds: [item.id],
        resourceType: 2 // 2表示图片资源
      });
      
      if (response.success) {
        ElMessage.success('已添加到收藏');
      } else {
        // 恢复原状态
        item.isFavorite = !isFavorite;
        ElMessage.error(response.errMessage || '收藏失败');
      }
    } else {
      // 取消收藏
      const response = await removeFavorite({
        resourceIds: [item.id]
      });
      
      if (response.success) {
        ElMessage.success('已取消收藏');
        
        // 如果当前是在"收藏"筛选下，并且取消了收藏，则需要重新加载数据
        if (filterType.value === 2) {
          fetchAssets();
        }
      } else {
        // 恢复原状态
        item.isFavorite = !isFavorite;
        ElMessage.error(response.errMessage || '取消收藏失败');
      }
    }
  } catch (error) {
    console.error('切换收藏状态失败:', error);
    ElMessage.error('操作失败，请稍后重试');
    // 恢复原状态
    item.isFavorite = !item.isFavorite;
  }
};

// 处理分页变化
const handlePageChange = (newPage) => {
  // 添加页面切换动画
  if (contentWrapper.value) {
    // 淡出效果
    contentWrapper.value.style.opacity = '0';
    contentWrapper.value.style.transform = 'translateY(10px)';
    
    // 短暂延迟后切换页面
    setTimeout(() => {
      pageNum.value = newPage;
      fetchAssets();
      
      // 数据加载后恢复动画
      nextTick(() => {
        if (contentWrapper.value) {
          contentWrapper.value.style.opacity = '';
          contentWrapper.value.style.transform = '';
          // 平滑滚动到顶部
          contentWrapper.value.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        }
      });
    }, 200);
  } else {
    pageNum.value = newPage;
    fetchAssets();
  }
};

// 处理每页显示数量变化
const handleSizeChange = (newSize) => {
  pageSize.value = newSize;
  pageNum.value = 1; // 重置到第一页
  fetchAssets();
};

// 处理筛选类型变化
const handleFilterChange = () => {
  // 添加动画效果
  if (contentWrapper.value) {
    // 添加淡出效果
    contentWrapper.value.style.opacity = '0';
    contentWrapper.value.style.transform = 'translateY(10px)';
    
    // 短暂延迟后获取新数据
    setTimeout(() => {
      pageNum.value = 1; // 重置到第一页
      fetchAssets();
      
      // 数据加载后恢复动画
      nextTick(() => {
        if (contentWrapper.value) {
          contentWrapper.value.style.opacity = '';
          contentWrapper.value.style.transform = '';
        }
      });
    }, 300);
  } else {
    pageNum.value = 1; // 重置到第一页
    fetchAssets();
  }
};

// 监听筛选类型变化
watch(filterType, () => {
  handleFilterChange();
});

// 监听资产类型变化
watch(() => props.assetType, () => {
  // 重置筛选和分页
  filterType.value = 1;
  pageNum.value = 1;
  fetchAssets();
});

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 加载资产数据
onMounted(() => {
  // 首先加载数据
  fetchAssets();

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (resizeTimeout) {
    clearTimeout(resizeTimeout);
  }
});


// 处理图片点击事件
const handleImageClick = (item) => {
  if(isMultiSelectMode.value) {
    return;
  }

  // 如果传入的是字符串（URL），则创建一个简单对象
  if (typeof item === 'string') {
    window.openImagePreview(item, "");
    return;
  }
  
  // 构建描述对象
  const description = {
    alt: item.name || '',
    title: item.name || '图片预览',
    description: item.prompt || ''
  };
  
  // 调用全局图片预览函数，传递图片URL和描述信息
  window.openImagePreview(item.imageUrl, description);
}
</script>

<style scoped>
.image-assets {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* 防止整体滚动 */
}

.filter-container {
  margin-bottom: 10px;
  flex-shrink: 0;
  /* 防止筛选控件被压缩 */
  display: flex;
  /* justify-content: space-between; */
  align-items: center;
  padding: 0 10px;
}

.filter-options {
  display: flex;
  gap: 10px;
}

.filter-actions {
  display: flex;
  gap: 10px;
}

.filter-option {
  padding: 4px 20px;
  background-color: #ecf5ff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  text-align: center;
  transition: all 0.3s;
}

.filter-option.active {
  background-color: #e3eefe;
  color: #409eff;
  border: 1px solid #d9ecff;
}

/* 多选操作栏 */
.multi-select-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 10px;
  /* background-color: #f5f7fa; */
  border-radius: 4px;
  /* margin-bottom: 10px; */
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); */
}

body.dark .multi-select-toolbar {
  /* background-color: var(--bg-secondary); */
}

.selected-count {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  display: flex;
  align-items: center;
  margin-right: 10px;
  gap: 12px;
}

.selected-info {
  margin-left: 4px;
}

.custom-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  gap: 6px;
}

.checkbox-inner {
  width: 16px;
  height: 16px;
  /* border: 1px solid #dcdfe6; */
  border-radius: 2px;
  /* background-color: #fff; */
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.checkbox-inner.checked {
  background-color: #409eff;
  border-color: #409eff;
}

.check-icon {
  color: #fff;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-label {
  font-size: 14px;
}

body.dark .selected-count {
  color: var(--text-primary);
}

body.dark .checkbox-inner {
  /* border-color: #4c4d4f; */
  /* background-color: #1f1f1f; */
}

body.dark .checkbox-inner.checked {
  background-color: #409eff;
  border-color: #409eff;
}

.toolbar-actions {
  display: flex;
  gap: 10px;
}

body.dark .filter-option {
  background-color: rgba(204, 221, 255, .06);
}

body.dark .filter-option.active {
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
}

.filter-option-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.option-icon {
  font-size: 14px;
}

/* 操作按钮样式 */
.action-option.download {
  background-color: #ecf5ff;
  color: #409eff;
}

.action-option.download:hover {
  background-color: #d9ecff;
  color: #409eff;
}

.action-option.favorite {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.action-option.favorite:hover {
  background-color: #faecd8;
  color: #e6a23c;
}

body.dark .action-option.download {
  background-color: rgba(64, 158, 255, 0.15);
  color: #409eff;
}

body.dark .action-option.favorite {
  background-color: rgba(230, 162, 60, 0.15);
  color: #e6a23c;
}

.asset-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  border-radius: 8px;
  margin-bottom: 0;
  height: 300px;
  transition: background-color 0.3s;
  flex: 1;
  /* 占据剩余空间 */
  overflow-y: auto;
  /* 内容过多时可滚动 */
}

body.dark .asset-loading {
  background-color: var(--bg-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(64, 158, 255, 0.2);
  border-top-color: #409eff;
  border-radius: 50%;
  animation: spin 1s infinite linear;
  margin-bottom: 10px;
}

body.dark .loading-spinner {
  border: 3px solid rgba(64, 158, 255, 0.1);
  border-top-color: var(--primary-color);
}

.loading-text {
  color: #909399;
  font-size: 14px;
  transition: color 0.3s;
}

body.dark .loading-text {
  color: var(--text-secondary);
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 160px);
  padding: 20px;
  text-align: center;
  border-radius: 12px;
  flex: 1;
  /* 占据剩余空间 */
}

.empty-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
  animation: pulse 2s infinite ease-in-out;
}

.empty-text {
  font-size: 18px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

body.dark .empty-text {
  color: #e0e0e0;
}

.empty-desc {
  font-size: 14px;
  color: #909399;
}

body.dark .empty-desc {
  color: #a0a0a0;
}

/* 添加动画效果 */
@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
    opacity: 0.8;
  }

  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

/* 特殊空状态样式 */
.final-empty-state {
  position: absolute;
  top: 40px;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
}

.final-empty-state .empty-icon {
  font-size: 64px;
  color: #6365f1ca;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: floating 3s ease-in-out infinite;
}

.final-empty-state .empty-text {
  font-size: 22px;
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 10px;
}

.final-empty-state .empty-desc {
  font-size: 16px;
  color: #6b7280;
  max-width: 400px;
  line-height: 1.5;
}

body.dark .final-empty-state .empty-text {
  color: #e5e7eb;
}

body.dark .final-empty-state .empty-desc {
  color: #9ca3af;
}

@keyframes floating {

  0%,
  100% {
    transform: translateY(0);
    scale: 1;
  }

  50% {
    transform: translateY(-15px);
    scale: 1.05;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes rippleEffect {
  0% {
    transform: scale(0);
    opacity: 0.6;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.asset-content-wrapper {
  flex: 1;
  /* 占据剩余空间 */
  overflow-y: auto;
  /* 添加垂直滚动 */
  min-height: 0;
  /* 确保 flex 子项可以正确滚动 */
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
  animation: fadeIn 0.5s ease-out;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.asset-content {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 4px;
  width: 100%;
  transition: all 0.5s ease-out;
}

.asset-item {
  /* overflow: hidden; */
  animation: itemFadeIn 0.6s ease-out backwards;
}

@keyframes itemFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.asset-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s;
  cursor: pointer;
  height: 100%;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.asset-card.selected {
  box-shadow: 0 0 0 2px #409eff;
  transform: scale(0.98);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

/* .asset-card.selected:hover {
  transform: scale(0.98) translateY(-3px);
} */

body.dark .asset-card.selected {
  box-shadow: 0 0 0 2px var(--primary-color);
}

body.dark .asset-card {
  background-color: var(--bg-secondary);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}

/* .asset-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
} */

/* body.dark .asset-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
} */

.selection-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 10px;
  height: 10px;
  background-color: rgba(64, 158, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 10;
  animation: rippleEffect 0.7s ease-out forwards;
}

body.dark .selection-ripple {
  background-color: rgba(64, 158, 255, 0.4);
}

.select-checkbox {
  position: absolute;
  top: 5px;
  left: 5px;
  z-index: 3;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
}

.select-checkbox:hover {
  transform: scale(1.05);
  background-color: rgba(255, 255, 255, 1);
}

.select-checkbox .checkbox-inner {
  width: 18px;
  height: 18px;
}

.select-checkbox .check-icon {
  font-size: 14px;
}

body.dark .select-checkbox {
  background-color: rgba(0, 0, 0, 0.6);
  box-shadow: 0 2px 4px rgba(255, 255, 255, 0.1);
}

body.dark .select-checkbox:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

.asset-preview {
  background-color: #f5f7fa;
  box-sizing: border-box;
  width: 100%;
  aspect-ratio: 4 / 3;
  flex: 1;
  min-height: 0;
}

.asset-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;
  transition: all 0.3s ease-in-out;
}

.asset-preview:hover img {
  object-position: top left;
}

.hidden-image {
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.3s ease-in;
}

.hidden-image.loaded {
  opacity: 1;
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #909399;
  font-size: 36px;
}

body.dark .image-placeholder {
  color: var(--text-secondary);
}

.favorite-icon {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.825);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  z-index: 2;
  opacity: 0;
  visibility: hidden;
}

.favorite-icon.always-visible {
  opacity: 1;
  visibility: visible;
}

.asset-card:hover .favorite-icon {
  opacity: 1;
  visibility: visible;
}

body.dark .favorite-icon {
  background-color: rgba(0, 0, 0, 0.5);
}

.favorite-icon:hover {
  transform: scale(1.1);
  background-color: rgba(255, 255, 255, 0.9);
}

body.dark .favorite-icon:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

.favorite-icon .el-icon {
  font-size: 16px;
  color: #f5a623;
}

body.dark .asset-preview {
  background-color: var(--bg-tertiary);
}

.asset-info {
  padding: 10px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.asset-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

body.dark .asset-name {
  color: var(--text-primary);
}

.asset-description {
  font-size: 12px;
  color: #606266;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

body.dark .asset-description {
  color: var(--text-secondary);
}

.asset-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
  margin-top: auto;
}

body.dark .asset-meta {
  color: var(--text-secondary);
}

.pagination-container {
  margin: 20px 0;
  display: flex;
  justify-content: center;
  flex-shrink: 0;
  /* 防止分页控件被压缩 */
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@media (max-width: 2200px) {
  .asset-content {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  }
}

@media (max-width: 2000px) {
  .asset-content {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  }
}

@media (max-width: 1600px) {
  .asset-content {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
}

/* 响应式布局 */
@media (max-width: 768px) {
  .asset-content {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
  }

  .filter-container {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-actions {
    margin-top: 8px;
    width: 100%;
  }

  .multi-select-toolbar {
    flex-direction: column;
    gap: 8px;
  }

  .toolbar-actions {
    width: 100%;
    justify-content: space-between;
  }
}

.asset-info-bar {
  padding: 6px;
  background-color: #f8f9fa;
  border-top: 1px solid #ebeef5;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

body.dark .asset-info-bar {
  background-color: #1e1e1e;
  border-top: 1px solid #333;
}

.asset-prompt {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #606266;
}

body.dark .asset-prompt {
  color: #a0a0a0;
}

.asset-aspect-ratio {
  color: #909399;
  font-size: 11px;
}

body.dark .asset-aspect-ratio {
  color: #808080;
}
</style>