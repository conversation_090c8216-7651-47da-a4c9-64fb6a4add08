<template>
  <div class="video-assets">
    <!-- 筛选和分类控件 -->
    <div class="filter-container">
      <div class="filter-options">
        <div v-for="item in filterTypes" :key="item.value" class="filter-option"
          :class="{ 'active': videoType === item.value }" @click="videoType = item.value">
          {{ item.label }}
        </div>
      </div>
    </div>

    <!-- 加载中状态 -->
    <div v-if="loading" class="asset-loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载视频资产中...</div>
    </div>

    <!-- 空数据状态 -->
    <div v-else-if="!hasAssets" class="empty-state final-empty-state">
      <el-icon class="empty-icon">
        <VideoCamera />
      </el-icon>
      <div class="empty-text">暂无视频资产</div>
      <div class="empty-desc">您可以在创作过程中生成视频资产</div>
    </div>

    <!-- 视频资产内容 -->
    <div v-else class="asset-content-wrapper" ref="contentWrapper">
      <div class="asset-content">
        <div v-for="(video, index) in videos" :key="video.id || index" class="video-item"
          :style="{ animationDelay: index * 0.01 + 's' }">
          <div class="video-card" @click="handleVideoClick(video)" @mouseenter="handleMouseEnter(video, index)"
            @mouseleave="handleMouseLeave(video, index)">
            <div class="video-preview">
              <!-- 视频元素，始终存在但可能不可见 -->
              <video
                v-if="hoveredVideoIndex === index"
                class="preview-video"
                :src="video.videoUrl"
                :ref="el => { if (el) videoRefs[index] = el }"
                muted
                loop
                preload="none"
                playsinline
                @loadeddata="handleVideoLoaded(index)"
                @playing="handleVideoPlaying(index)"
              ></video>

              <!-- 图片覆盖层，用于防止闪屏 -->
              <img
                :src="`${video.firstFrameImage || video.lastFrameImage}?x-oss-process=image/resize,h_200`"
                alt="视频封面"
                class="preview-image"
                :class="{
                  'fade-out': hoveredVideoIndex === index && videoStates[index]?.isPlaying,
                  'show': hoveredVideoIndex !== index || !videoStates[index]?.isPlaying
                }"
              >

              <div class="duration">{{ formatDuration(video.duration) }}</div>

              <!-- 已分享显示分享按钮 -->
              <div v-if="video.videoUrl && videoType == 2 && (video.shareStatus && video.shareStatus != 0)" class="share-icon" style="opacity: 1;">
                <el-icon>
                  <Link/>
                </el-icon>
              </div>

              <!-- 分享按钮 -->
              <div v-if="video.videoUrl && videoType == 2" class="share-icon" @click.stop="handleShare(video)">
                <el-icon>
                  <Share v-if="!video.shareStatus || video.shareStatus == 0" />
                  <Link v-else />
                </el-icon>
              </div>
            </div>
            <div class="video-info">
              <div class="video-name">{{ video.prompt || '视频 ' + (index + 1) }}</div>
              <div class="video-meta">
                <span>{{ formatDate(video.createTime) }}</span>
                <span>{{ video.resolution }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页控件 -->
    <div class="pagination-container" v-if="totalCount > 0">
      <el-pagination v-model:current-page="pageNum" v-model:page-size="pageSize" :page-sizes="pageSizeOptions"
        layout="total, sizes, prev, pager, next, jumper" :total="totalCount" @size-change="handleSizeChange"
        @current-change="handlePageChange" />
    </div>

    <!-- 分享弹框 -->
    <ShareDialog
      v-if="shareDialogVisible"
      :visible="shareDialogVisible"
      :video="currentShareVideo"
      @close="closeShareDialog"
      @share="handleShareAction"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch, computed } from 'vue';
import { VideoCamera, Share, Link } from '@element-plus/icons-vue';
import { getUserVideo, videoRenderShareLink } from '@/api/auth.js';
import { ElMessage, ElPagination } from 'element-plus';
import ShareDialog from '../videoEdit/ShareDialog.vue';

// 组件属性
const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false
  }
});

// 状态变量
const hasAssets = ref(false);
const videos = ref([]);
const loading = ref(false);
const contentWrapper = ref(null);
const hoveredVideoIndex = ref(-1); // 当前悬停的视频索引
const videoRefs = ref({}); // 视频元素引用
const videoStates = ref({}); // 视频状态管理

// 分享弹框状态
const shareDialogVisible = ref(false);
const currentShareVideo = ref(null);

// 分页参数
const pageNum = ref(1);
const pageSize = ref(50);
const totalCount = ref(0);
const totalPages = ref(0);
const pageSizeOptions = ref([50, 62, 72, 82]);

// 筛选参数
const videoType = ref(1); // 1-AI生成 2-渲染导出
const filterTypes = computed(() => {
  return [
    { value: 1, label: 'AI生成' },
    { value: 2, label: '渲染导出' },
  ];
});

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 格式化视频时长
const formatDuration = (milliseconds) => {
  if (!milliseconds) return '00:00';
  const seconds = milliseconds / 1000;
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
};

// 处理视频加载完成
const handleVideoLoaded = (index) => {
  if (!videoStates.value[index]) {
    videoStates.value[index] = {};
  }
  videoStates.value[index].isLoaded = true;

  // 视频加载完成后开始播放
  const videoEl = videoRefs.value[index];
  if (videoEl && hoveredVideoIndex.value === index) {
    videoEl.playbackRate = 2.0; // 设置播放速度为2倍
    videoEl.play().catch(err => console.error('视频自动播放失败:', err));
  }
};

// 处理视频开始播放
const handleVideoPlaying = (index) => {
  if (!videoStates.value[index]) {
    videoStates.value[index] = {};
  }
  videoStates.value[index].isPlaying = true;
};

// 处理鼠标进入视频卡片
const handleMouseEnter = (video, index) => {
  if (!video.videoUrl) return;

  hoveredVideoIndex.value = index;

  // 初始化视频状态
  if (!videoStates.value[index]) {
    videoStates.value[index] = {
      isLoaded: false,
      isPlaying: false
    };
  }

  // 等待下一帧再处理视频，确保DOM更新完成
  nextTick(() => {
    const videoEl = videoRefs.value[index];
    if (videoEl) {
      // 如果视频还没有加载，开始加载
      if (videoEl.readyState === 0) {
        videoEl.load();
      } else if (videoEl.readyState >= 3) {
        // 如果视频已经加载完成，直接播放
        videoEl.playbackRate = 2.0;
        videoEl.play().catch(err => console.error('视频自动播放失败:', err));
        videoStates.value[index].isPlaying = true;
      }
    }
  });
};

// 处理鼠标离开视频卡片
const handleMouseLeave = (_, index) => {
  hoveredVideoIndex.value = -1;

  // 重置视频状态
  if (videoStates.value[index]) {
    videoStates.value[index].isPlaying = false;
    videoStates.value[index].isLoaded = false;
  }

  // 暂停视频播放
  const videoEl = videoRefs.value[index];
  if (videoEl) {
    videoEl.pause();
    videoEl.currentTime = 0; // 重置播放位置
  }
};

// 获取视频资产数据
const fetchVideos = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      videoType: videoType.value
    };

    const response = await getUserVideo(params);

    if (response.success) {
      videos.value = response.data || [];
      totalCount.value = response.totalCount || 0;
      totalPages.value = response.totalPages || 0;
      hasAssets.value = videos.value.length > 0;

      // 重置视频引用和状态
      videoRefs.value = {};
      videoStates.value = {};
    } else {
      ElMessage.error(response.errMessage || '获取视频数据失败');
      videos.value = [];
      hasAssets.value = false;
    }
  } catch (error) {
    console.error('获取视频数据出错:', error);
    ElMessage.error('获取视频数据时发生错误');
    videos.value = [];
    hasAssets.value = false;
  } finally {
    loading.value = false;
  }
};

// 处理视频点击
const handleVideoClick = (video) => {
  if (video.videoUrl) {
    // 使用全局视频预览函数
    if (window.openVideoPreview && typeof window.openVideoPreview === 'function') {
      // 传递视频元数据
      window.openVideoPreview(
        video.videoUrl,
        video.prompt || '',
        {
          title: video.prompt ? `视频预览` : '',
          resolution: video.resolution || '',
          duration: video.duration || 0
        }
      );
    } else {
      // 打开新窗口预览视频
      window.open(video.videoUrl, '_blank');
    }
  }
};

// 处理分页变化
const handlePageChange = (newPage) => {
  // 添加页面切换动画
  if (contentWrapper.value) {
    // 淡出效果
    contentWrapper.value.style.opacity = '0';
    contentWrapper.value.style.transform = 'translateY(10px)';

    // 短暂延迟后切换页面
    setTimeout(() => {
      pageNum.value = newPage;
      fetchVideos();

      // 数据加载后恢复动画
      nextTick(() => {
        if (contentWrapper.value) {
          contentWrapper.value.style.opacity = '';
          contentWrapper.value.style.transform = '';
          // 平滑滚动到顶部
          contentWrapper.value.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        }
      });
    }, 200);
  } else {
    pageNum.value = newPage;
    fetchVideos();
  }
};

// 处理每页显示数量变化
const handleSizeChange = (newSize) => {
  pageSize.value = newSize;
  pageNum.value = 1; // 重置到第一页
  fetchVideos();
};

// 处理筛选类型变化
const handleFilterChange = () => {
  // 添加动画效果
  if (contentWrapper.value) {
    // 添加淡出效果
    contentWrapper.value.style.opacity = '0';
    contentWrapper.value.style.transform = 'translateY(10px)';

    // 短暂延迟后获取新数据
    setTimeout(() => {
      pageNum.value = 1; // 重置到第一页
      fetchVideos();

      // 数据加载后恢复动画
      nextTick(() => {
        if (contentWrapper.value) {
          contentWrapper.value.style.opacity = '';
          contentWrapper.value.style.transform = '';
        }
      });
    }, 300);
  } else {
    pageNum.value = 1; // 重置到第一页
    fetchVideos();
  }
};

// 监听筛选类型变化
watch(videoType, () => {
  handleFilterChange();
});

// 处理视频分享
const handleShare = async (video) => {
  if (!video || !video.id) {
    ElMessage.error('视频信息不完整');
    return;
  }

  // 设置当前分享的视频并显示分享弹框
  currentShareVideo.value = video;
  shareDialogVisible.value = true;
};

// 关闭分享弹框
const closeShareDialog = () => {
  shareDialogVisible.value = false;
  currentShareVideo.value = null;
};

// 处理分享弹框中的分享操作
const handleShareAction = async (action) => {
  if (!currentShareVideo.value) return;

  const video = currentShareVideo.value;

  try {
    if (action === 'share') {
      // 创建或获取分享链接
      if (video.shareStatus === 1 && video.shareUrl) {
        // 已有分享链接，直接复制
        copyShareLink(video.shareUrl);
      } else {
        // 创建新的分享链接
        await toggleVideoShare(video, true);
      }
    } else if (action === 'unshare') {
      // 取消分享
      await toggleVideoShare(video, false);
    }
  } catch (error) {
    console.error('分享操作失败:', error);
    ElMessage.error('分享操作失败，请重试');
  }
};

// 切换视频分享状态
const toggleVideoShare = async (video, share) => {
  try {
    const response = await videoRenderShareLink({
      taskId: video.id,
      share: share
    });

    if (response && response.success) {
      // 更新视频的分享状态
      const videoIndex = videos.value.findIndex(v => v.id === video.id);
      if (videoIndex !== -1) {
        videos.value[videoIndex].shareStatus = share ? 1 : 0;
        if (share && response.data) {
          videos.value[videoIndex].shareUrl = response.data;
          // 自动复制分享链接
          copyShareLink(response.data);
        } else {
          videos.value[videoIndex].shareUrl = '';
          ElMessage.success('已取消视频分享');
        }
      }

      // 同时更新当前分享弹框中的视频状态
      if (currentShareVideo.value && currentShareVideo.value.id === video.id) {
        currentShareVideo.value.shareStatus = share ? 1 : 0;
        if (share && response.data) {
          currentShareVideo.value.shareUrl = response.data;
        } else {
          currentShareVideo.value.shareUrl = '';
        }
      }

      // 刷新视频列表以获取最新状态
      fetchVideos();

    } else {
      ElMessage.error(response?.errMessage || '分享操作失败');
    }
  } catch (error) {
    console.error('分享操作出错:', error);
    ElMessage.error('分享操作失败，请重试');
  }
};

// 复制分享链接
const copyShareLink = (shareUrl) => {
  if (!shareUrl) {
    ElMessage.error('分享链接不存在');
    return;
  }

  navigator.clipboard.writeText(shareUrl)
    .then(() => {
      ElMessage.success('分享链接已复制到剪贴板');
    })
    .catch(err => {
      console.error('复制失败:', err);
      ElMessage.error('复制链接失败，请手动复制');
    });
};

// 窗口大小变化处理函数
let resizeTimeout = null;
const handleResize = () => {
  // 使用防抖处理窗口大小变化
  if (resizeTimeout) {
    clearTimeout(resizeTimeout);
  }

  resizeTimeout = setTimeout(() => {
    // 可以在这里添加响应式布局的逻辑
  }, 200);
};

// 加载资产数据
onMounted(() => {
  // 首先加载数据
  fetchVideos();

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (resizeTimeout) {
    clearTimeout(resizeTimeout);
  }

  // 清理所有视频元素
  Object.values(videoRefs.value).forEach(videoEl => {
    if (videoEl && typeof videoEl.pause === 'function') {
      videoEl.pause();
      videoEl.src = '';
      videoEl.load();
    }
  });
});
</script>

<style scoped>
.video-assets {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.filter-container {
  margin-bottom: 10px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  padding: 0 10px;
}

.filter-options {
  display: flex;
  gap: 10px;
}

.filter-option {
  padding: 4px 20px;
  background-color: #ecf5ff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  text-align: center;
  transition: all 0.3s;
}

.filter-option.active {
  background-color: #e3eefe;
  color: #409eff;
  border: 1px solid #d9ecff;
}

body.dark .filter-option {
  background-color: rgba(204, 221, 255, .06);
}

body.dark .filter-option.active {
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
}

.asset-content-wrapper {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  width: 100%;
  padding: 0 8px;
  box-sizing: border-box;
  animation: fadeIn 0.5s ease-out;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.asset-content {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 4px;
  padding: 0;
  width: 100%;
  transition: all 0.5s ease-out;
}

.video-item {
  overflow: hidden;
  animation: itemFadeIn 0.6s ease-out backwards;
}

@keyframes itemFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.asset-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  background-color: #f9fafc;
  border-radius: 8px;
  margin-bottom: 0;
  height: 300px;
  transition: background-color 0.3s;
  flex: 1;
  overflow-y: auto;
}

body.dark .asset-loading {
  background-color: var(--bg-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(64, 158, 255, 0.2);
  border-top-color: #409eff;
  border-radius: 50%;
  animation: spin 1s infinite linear;
  margin-bottom: 10px;
}

body.dark .loading-spinner {
  border: 3px solid rgba(64, 158, 255, 0.1);
  border-top-color: var(--primary-color);
}

.loading-text {
  color: #909399;
  font-size: 14px;
  transition: color 0.3s;
}

body.dark .loading-text {
  color: var(--text-secondary);
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 160px);
  padding: 20px;
  text-align: center;
  border-radius: 12px;
  flex: 1;
}

.empty-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
  animation: pulse 2s infinite ease-in-out;
}

body.dark .empty-icon {
  color: #606266;
}

.empty-text {
  font-size: 18px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

body.dark .empty-text {
  color: #e0e0e0;
}

.empty-desc {
  font-size: 14px;
  color: #909399;
}

body.dark .empty-desc {
  color: #a0a0a0;
}

/* 添加动画效果 */
@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
    opacity: 0.8;
  }

  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

/* 特殊空状态样式 */
.final-empty-state {
  position: absolute;
  top: 50px;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
}

.final-empty-state .empty-icon {
  font-size: 64px;
  color: #6365f1ca;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: floating 3s ease-in-out infinite;
}

.final-empty-state .empty-text {
  font-size: 22px;
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 10px;
}

.final-empty-state .empty-desc {
  font-size: 16px;
  color: #6b7280;
  max-width: 400px;
  line-height: 1.5;
}

body.dark .final-empty-state .empty-text {
  color: #e5e7eb;
}

body.dark .final-empty-state .empty-desc {
  color: #9ca3af;
}

@keyframes floating {

  0%,
  100% {
    transform: translateY(0);
    scale: 1;
  }

  50% {
    transform: translateY(-15px);
    scale: 1.05;
  }
}

.video-card {
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
}

body.dark .video-card {
  background-color: var(--bg-secondary);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}

.video-card:hover {
  /* transform: translateY(-5px); */
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

body.dark .video-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.video-preview {
  position: relative;
  aspect-ratio: 4/3;
  overflow: hidden;
  /* background-color: #000; */
}

.thumbnail {
  width: 100%;
  height: 100%;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  /* background-color: #000; */
  transition: transform 0.3s, opacity 0.3s;
}

.thumbnail.hidden {
  opacity: 0;
}

.video-card:hover .thumbnail:not(.hidden) {
  transform: scale(1.05);
}

/* 视频预览样式 */
.preview-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: #000;
  z-index: 1;
}

.preview-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;
  transition: opacity 0.3s ease;
  z-index: 2;
}

.preview-image.show {
  opacity: 1;
}

.preview-image.fade-out {
  opacity: 0;
}

.duration {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 8px;
  /* z-index: 2; */
}

/* 分享按钮样式 */
.share-icon {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 24px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 12px;
  transition: opacity 0.3s, background-color 0.3s;
  cursor: pointer;
  z-index: 3;
  opacity: 0;
}

.share-icon:hover {
  background-color: rgba(34, 197, 94, 0.9);
}

body.dark .share-icon:hover {
  background-color: rgba(22, 163, 74, 0.9);
}

.video-card:hover .share-icon {
  opacity: 1;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 48px;
  height: 48px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24px;
  opacity: 0;
  transition: opacity 0.3s, background-color 0.3s;
  z-index: 2;
}

.video-card:hover .play-icon {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.7);
}

.video-info {
  padding: 4px 8px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.video-name {
  font-size: 12px;
  font-weight: 500;
  /* margin-bottom: 5px; */
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

body.dark .video-name {
  color: var(--text-primary);
}

.video-meta {
  display: flex;
  justify-content: space-between;
  font-size: 10px;
  color: #909399;
}

body.dark .video-meta {
  color: var(--text-secondary);
}

.pagination-container {
  margin: 20px 0;
  display: flex;
  justify-content: center;
  flex-shrink: 0;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 响应式布局 */
@media (max-width: 768px) {
  .asset-content {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 4px;
  }



  .play-icon {
    width: 36px;
    height: 36px;
    font-size: 18px;
  }
}
</style>