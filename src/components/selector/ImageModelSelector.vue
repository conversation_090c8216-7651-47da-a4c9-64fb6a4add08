<template>
  <div class="image-model-selector">
    <div class="models-grid">
      <div
        v-for="model in models"
        :key="model.id"
        class="model-item"
        :class="{ 'model-selected': modelValue === model.id }"
        @click="selectModel(model.id)"
      >
        <div class="model-info">
          <h3 class="model-name">{{ model.name }}</h3>
          <p class="model-advantages">{{ model.advantages }}</p>
        </div>
        <span class="model-price">{{ model.price }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: null
  },
  models: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue']);

const selectModel = (modelId) => {
  emit('update:modelValue', modelId);
};
</script>

<style scoped>
.image-model-selector {
  margin-bottom: 16px;
}

.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.model-item {
  position: relative;
  border-radius: 6px;
  padding: 12px;
  background-color: #f8f9fc;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border: 1px solid #dce8f6;
}

.model-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  background-color: #f0f7ff;
}

.model-selected {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.model-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
  text-align: left;
}

.model-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  text-align: left;
}

.model-advantages {
  font-size: 12px;
  color: #606266;
  margin: 0;
  line-height: 1.3;
}

.model-price {
  position: absolute;
  right: 0px;
  top: 0px;
  background-color: #4ea1ff;
  padding: 4px 12px;
  border-radius: 0 4px 0px 12px;
  font-size: 11px;
  color: #ffffff;
  font-weight: 500;
  margin-left: 8px;
}

body.dark .model-item {
  background-color: var(--bg-tertiary);
  border-color: transparent;
}

body.dark .model-item:hover {
  background-color: var(--bg-quaternary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

body.dark .model-selected {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
}

body.dark .model-name {
  color: var(--text-primary);
}

body.dark .model-advantages {
  color: var(--text-secondary);
}

@media (max-width: 768px) {
  .models-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 8px;
  }
  
  .model-item {
    padding: 8px;
  }
  
  .model-name {
    font-size: 13px;
  }
  
  .model-advantages {
    font-size: 11px;
  }
  
  .model-price {
    font-size: 10px;
  }
}
</style> 