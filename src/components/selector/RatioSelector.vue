<template>
  <div class="ratio-selector">
    <div class="ratio-options">
      <div 
        class="ratio-option" 
        v-for="ratio in ratioOptions" 
        :key="ratio" 
        :class="{ 'active': modelValue === ratio }" 
        @click="handleRatioSelect(ratio)"
      >
        <div class="ratio-preview" :class="'ratio-preview-' + ratio.replace(':', '-')">
          <div class="ratio-inner"></div>
        </div>
        <div class="ratio-label">
          <span class="ratio-text">{{ ratio }}</span>
          <span class="ratio-desc">{{ getRatioDescription(ratio) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  // v-model 绑定的值
  modelValue: {
    type: String,
    default: '16:9'
  },
  // 是否禁用组件
  disabled: {
    type: Boolean,
    default: false
  },
  // 自定义比例选项，默认为 16:9, 9:16, 1:1
  ratioOptions: {
    type: Array,
    default: () => ['16:9', '9:16', '1:1', '3:4', '2:3', '3:2', '4:3', '21:9']
  }
})

const emit = defineEmits(['update:modelValue', 'ratio-change'])

// 处理比例选择事件
const handleRatioSelect = (ratio) => {
  if (props.disabled) return
  emit('update:modelValue', ratio)
  emit('ratio-change', ratio)
}

// 获取比例描述
const getRatioDescription = (ratio) => {
  switch (ratio) {
    case '16:9': return '横版 (适合电脑/电视)'
    case '9:16': return '竖版 (适合手机)'
    case '3:4': return '竖版 (适合平板/印刷)'
    case '2:3': return '竖版 (适合书籍/杂志)'
    case '1:1': return '方形 (适合社交媒体)'
    case '3:2': return '横版 (适合传统相机)'
    case '4:3': return '横版 (适合传统显示器)'
    case '21:9': return '超宽 (适合电影/游戏)'
    default: return ''
  }
}
</script>

<style scoped>
/* 比例选择器容器 */
.ratio-selector {
  width: 100%;
  box-sizing: border-box;
  /* padding: 16px; */
  max-width: 100%;
}

/* 比例选项容器 */
.ratio-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 8px;
  box-sizing: border-box;
  width: 100%;
}

/* 比例选项 */
.ratio-option {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
}

body.dark .ratio-option {
  background-color: var(--bg-secondary, #1e1e2d);
  border-color: var(--border-color, #2d2d3f);
}

.ratio-option:hover {
  border-color: #a5b4fc;
  background-color: #f9faff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

body.dark .ratio-option:hover {
  border-color: #818cf8;
  background-color: var(--bg-hover, #2a2a3c);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ratio-option.active {
  border-color: #4f46e5;
  background-color: #f1f5ff;
  box-shadow: 0 4px 10px rgba(79, 70, 229, 0.15);
}

body.dark .ratio-option.active {
  border-color: #6366f1;
  background-color: rgba(99, 102, 241, 0.1);
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.2);
}

/* 比例预览 */
.ratio-preview {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  margin-right: 12px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.ratio-inner {
  background-color: #6366f1;
  border-radius: 3px;
  transition: all 0.2s ease;
}

.ratio-preview-16-9 .ratio-inner {
  width: 32px;
  height: 18px;
}

.ratio-preview-9-16 .ratio-inner {
  width: 18px;
  height: 32px;
}

.ratio-preview-3-4 .ratio-inner {
  width: 18px;
  height: 24px;
}

.ratio-preview-2-3 .ratio-inner {
  width: 18px;
  height: 27px;
}

.ratio-preview-1-1 .ratio-inner {
  width: 24px;
  height: 24px;
}

.ratio-preview-3-2 .ratio-inner {
  width: 27px;
  height: 18px;
}

.ratio-preview-4-3 .ratio-inner {
  width: 24px;
  height: 18px;
}

.ratio-preview-21-9 .ratio-inner {
  width: 32px;
  height: 14px;
}

.ratio-option.active .ratio-preview {
  background-color: #eff6ff;
}

body.dark .ratio-option.active .ratio-preview {
  background-color: rgba(99, 101, 241, 0);
}

.ratio-option.active .ratio-inner {
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(99, 102, 241, 0.3);
}

/* 比例标签 */
.ratio-label {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  flex: 1;
}

.ratio-text {
  font-size: 16px;
  font-weight: 500;
  color: #1e293b;
}

body.dark .ratio-text {
  color: var(--text-primary, #e2e8f0);
}

.ratio-desc {
  font-size: 13px;
  color: #64748b;
}

body.dark .ratio-desc {
  color: var(--text-secondary, #94a3b8);
}

.ratio-option.active .ratio-text {
  color: #4f46e5;
}

body.dark .ratio-option.active .ratio-text {
  color: #818cf8;
}

/* 窄屏幕布局 */
@media (max-width: 640px) {
  .ratio-options {
    grid-template-columns: repeat(1, 1fr);
  }
  
  .ratio-option {
    padding: 8px 12px;
  }
  
  .ratio-preview {
    width: 40px;
    height: 40px;
  }
}
</style> 