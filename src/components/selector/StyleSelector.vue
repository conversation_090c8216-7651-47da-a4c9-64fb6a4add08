<template>
  <div class="style-selection">

    <div v-if="isLoading" class="style-loading">
      <el-skeleton :count="1" :rows="0" animated>
        <template #template>
          <div class="style-skeleton-grid">
            <div v-for="i in 4" :key="i" class="style-skeleton-item"></div>
          </div>
        </template>
      </el-skeleton>
    </div>

    <div v-else-if="styles.length === 0" class="style-empty">
      暂无画面风格数据
    </div>

    <div v-else>
      <!-- 添加分类标签页 -->
      <el-tabs v-model="activeCategory" class="style-category-tabs style-selector-tabs" @tab-click="handleTabClick">
        <!-- 推荐标签页 -->
        <!-- <el-tab-pane label="推荐" name="all"></el-tab-pane> -->
        
        <!-- 为每个分类创建标签页 -->
        <el-tab-pane 
          v-for="styleCategory in categories" 
          :key="styleCategory" 
          :label="styleCategory" 
          :name="styleCategory"
        ></el-tab-pane>
        
        <!-- 未分类标签页 -->
        <el-tab-pane v-if="hasUncategorized" label="未分类" name="uncategorized"></el-tab-pane>
      </el-tabs>
      
      <!-- 统一的风格网格显示区域 -->
      <div class="style-grid">
        <div v-for="(style, index) in filteredStyles" :key="index" class="style-item"
          :class="{ active: (props.modelValue || props.selectedStyleId) === style.id }"
          @click="selectStyle(style.id)"
          @dblclick="previewStyle(style)"
          @contextmenu.prevent="previewStyle(style)">
          <div class="style-preview" :style="{ backgroundImage: `url(${style.preview || ''})` }">
            <div class="style-overlay"></div>
            <div class="style-badge">{{ style.name }}</div>
            <!-- 添加双击预览提示 -->
            <div class="style-preview-tip">
              <span>双击看大图</span>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'

const props = defineProps({
  styles: {
    type: Array,
    default: () => []
  },
  modelValue: {
    type: Number,
    default: null
  },
  selectedStyleId: {
    type: Number,
    default: null
  },
  isLoading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'update:selectedStyleId', 'style-select'])

// 添加当前选中的分类
const activeCategory = ref('all')

// 存储推荐的随机样式
const recommendedStyles = ref([])

// 处理标签页点击事件
const handleTabClick = () => {
  // 如果需要在标签页切换时执行特定逻辑，可以在这里添加
}

// 随机选择10-15个样式
const getRandomStyles = () => {
  if (!props.styles || props.styles.length === 0) return []
  
  // 克隆原始数组以避免修改它
  const allStyles = [...props.styles]
  
  // 随机打乱数组
  for (let i = allStyles.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [allStyles[i], allStyles[j]] = [allStyles[j], allStyles[i]]
  }
  
  // 随机选择10-15个元素
  const count = Math.floor(Math.random() * 6) + 10 // 生成10到15之间的随机数
  return allStyles.slice(0, Math.min(count, allStyles.length))
}

// 在组件挂载时初始化推荐样式
onMounted(() => {
  recommendedStyles.value = getRandomStyles()

  // 只有当 styles 数组不为空时才设置默认分类
  if (props.styles && props.styles.length > 0) {
    activeCategory.value = props.styles[0].styleCategory
  }
})

// 当原始样式数组改变时更新推荐样式
watch(() => props.styles, (newStyles) => {
  recommendedStyles.value = getRandomStyles()

  // 如果之前没有设置分类，且现在有数据了，设置默认分类
  if (newStyles && newStyles.length > 0 && !activeCategory.value) {
    activeCategory.value = newStyles[0].styleCategory
  }
}, { deep: true })

// 当分类切换到推荐标签时重新随机
watch(() => activeCategory.value, (newValue) => {
  if (newValue === 'all') {
    recommendedStyles.value = getRandomStyles()
  }
})

// 计算所有可用的分类
const categories = computed(() => {
  if (!props.styles || props.styles.length === 0) return []

  // 提取所有不同的category
  const categorySet = new Set(props.styles
    .filter(style => style.styleCategory && style.styleCategory.trim() !== '')  // 过滤掉没有category或category为空的风格
    .map(style => style.styleCategory)     // 提取category字段
  )

  // 转换为数组并返回
  return Array.from(categorySet)
})

// 是否有未分类的风格
const hasUncategorized = computed(() => {
  if (!props.styles || props.styles.length === 0) return false
  return props.styles.some(style => !style.styleCategory || style.styleCategory.trim() === '')
})

// 根据当前选中的分类过滤风格
const filteredStyles = computed(() => {
  if (!props.styles || props.styles.length === 0) return []

  if (activeCategory.value === 'all') {
    return recommendedStyles.value
  } else if (activeCategory.value === 'uncategorized') {
    return props.styles.filter(style => !style.styleCategory || style.styleCategory.trim() === '')
  } else {
    return props.styles.filter(style => style.styleCategory === activeCategory.value)
  }
})

const selectStyle = (styleId) => {
  emit('update:modelValue', styleId)
  emit('update:selectedStyleId', styleId)
  emit('style-select', styleId)
}

// 添加预览风格的方法
const previewStyle = (style) => {
  if (style && style.preview) {
    window.openImagePreview(style.preview, style.name || '风格预览')
  }
}

</script>

<style scoped>
.style-selection {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.style-loading {
  width: 100%;
  padding: 10px 0;
}

.style-skeleton-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2px;
  width: 100%;
}

.style-skeleton-item {
  aspect-ratio: 1/1;
  border-radius: 6px;
  background-color: #f0f2f5;
}

body.dark .style-skeleton-item {
  background-color: var(--bg-tertiary);
}

.style-empty {
  width: 100%;
  text-align: center;
  color: #909399;
  padding: 30px 0;
  font-size: 14px;
}

body.dark .style-empty {
  color: var(--text-secondary);
}

/* 分类标签页样式 */
.style-category-tabs {
  width: 100%;
}

/* 添加更具体的选择器以覆盖父组件样式 */
.style-selector-tabs :deep(.el-tabs__header) {
  margin-bottom: 8px !important;
  position: relative !important;
}

.style-selector-tabs :deep(.el-tabs__item) {
  color: #606266 !important;
  font-size: 14px !important;
  padding: 0 16px !important;
  height: 32px !important;
  line-height: 32px !important;
  transition: all 0.3s !important;
}

body.dark .style-selector-tabs :deep(.el-tabs__item) {
  color: var(--text-secondary) !important;
}

.style-selector-tabs :deep(.el-tabs__item.is-active) {
  color: #4f46e5 !important;
  font-weight: 500 !important;
}

body.dark .style-selector-tabs :deep(.el-tabs__item.is-active) {
  color: #6366f1 !important;
}

.style-selector-tabs :deep(.el-tabs__active-bar) {
  background-color: transparent !important;
  background-image: linear-gradient(
    90deg, transparent 0, transparent 0%,
    #4f46e5 0, #4f46e5 100%,
    transparent 0, transparent
  ) !important;
  height: 2px !important;
}

body.dark .style-selector-tabs :deep(.el-tabs__active-bar) {
  background-image: linear-gradient(
    90deg, transparent 0, transparent 0%,
    #6366f1 0, #6366f1 100%,
    transparent 0, transparent
  ) !important;
}

.style-selector-tabs :deep(.el-tabs__nav-wrap::after) {
  position: static !important;
  height: 1px !important;
  background-color: #e4e7ed !important;
}

body.dark .style-selector-tabs :deep(.el-tabs__nav-wrap::after) {
  background-color: var(--border-color) !important;
}

/* 设置更具体的选择器，确保样式覆盖 */
.style-selector-tabs :deep(.el-tabs__nav) {
  height: auto !important;
}

.style-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(86px, 1fr));
  gap: 4px;
  width: 100%;
  margin-top: 8px;
  padding: 5px;
  box-sizing: border-box;
  max-height: 300px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #4143e65d #ffffff00;
}

body.dark .style-grid {
  scrollbar-color: #4143e65d #00000000;
}

.style-item {
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  background-color: #fff;
  border: 1px solid #eaecef;
  /* 保持比例 */
  aspect-ratio: 1/1;
  display: flex;
  flex-direction: column;
  opacity: 0.8;
}

.style-preview-image {
  height: 100%;
  object-fit: cover;
}

body.dark .style-item {
  background-color: var(--bg-card);
  border-color: var(--border-color);
  opacity: 0.8;
}

.style-preview {
  flex: 1;
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  transition: transform 0.2s cubic-bezier(0.25, 1, 0.5, 1);
}

.style-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0) 50%, rgba(0,0,0,0.7) 100%);
  opacity: 0.8;
  transition: opacity 0.1s ease;
}

.style-badge {
  position: absolute;
  bottom: 2px;
  left: 2px;
  background-color: rgba(0, 0, 0, 0.635);
  color: white;
  padding: 2px 8px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
  backdrop-filter: blur(4px);
  opacity: 0;
  transition: all 0.2s ease;
  max-lines: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.style-name {
  padding: 4px;
  font-weight: 500;
  color: #303133;
  font-size: 11px;
  text-align: center;
  line-height: 1.3;
  transition: color 0.3s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

body.dark .style-name {
  color: var(--text-primary);
}

/* 悬停效果 */
.style-item:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: #e2e8f0;
  opacity: 1;
  z-index: 100;
}

.style-item:hover .style-preview {
  transform: scale(1.05);
  opacity: 1;
}

.style-item:hover .style-badge {
  opacity: 1;
  /* font-size: 6px; */
  background-color: rgba(0, 0, 0, 0.56);
}

body.dark .style-item:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  border-color: rgba(99, 102, 241, 0.3);
  opacity: 1;
}

/* 选中状态 */
.style-item.active {
  transform: scale(1.1);
  border: 2px solid #4f46e5;
  box-shadow: 0 0 2px 2px rgba(61, 52, 226, 0.3);
  opacity: 1;
  z-index: 10;
}

.style-item.active .style-badge {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.476);
}

body.dark .style-item.active {
  transform: scale(1.2);
  z-index: 100;
  opacity: 1;
  border-color: #6366f1;
  box-shadow: 0 0 2px 2px rgba(99, 102, 241, 0.4);
}

/* 响应式布局 */
@media (min-width: 768px) and (max-width: 1024px) {
  .style-grid {
    grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
  }
  
  .style-selector-tabs :deep(.el-tabs__item) {
    padding: 0 12px !important;
    font-size: 13px !important;
  }
}

@media (max-width: 767px) {
  .style-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 4px;
  }

  .style-name {
    font-size: 12px;
    padding: 6px;
  }
  
  .style-badge {
    font-size: 9px;
    padding: 2px 6px;
  }
  
  .style-selector-tabs :deep(.el-tabs__item) {
    padding: 0 8px !important;
    font-size: 12px !important;
  }
  
  .style-selector-tabs :deep(.el-tabs__header) {
    margin-bottom: 6px !important;
  }
}

/* 添加双击预览提示样式 */
.style-preview-tip {
  position: absolute;
  top: 2px;
  left: 50%;
  transform: translateX(-50%) translateY(5px) scale(0.8);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  opacity: 0;
  transition: all 0.2s ease;
  pointer-events: none; /* 确保不会影响鼠标事件 */
  backdrop-filter: blur(2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  z-index: 4; /* 确保在style-badge (z-index: 5)之下 */
  white-space: nowrap;
  letter-spacing: 0.5px;
}

body.dark .style-preview-tip {
  background-color: rgba(99, 102, 241, 0.6);
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
}

/* 悬停时显示提示 */
.style-item:hover .style-preview-tip {
  opacity: 1;
  transform: translateX(-50%) translateY(0) scale(1);
}

/* 选中状态时不显示提示 */
.style-item.active:hover .style-preview-tip {
  opacity: 0;
}

/* 响应式调整 */
@media (max-width: 767px) {
  .style-preview-tip {
    font-size: 9px;
    padding: 2px 6px;
    bottom: 22px;
  }
}
</style>