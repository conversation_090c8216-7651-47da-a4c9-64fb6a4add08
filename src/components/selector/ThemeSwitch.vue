<template>
  <div class="theme-switch" :class="{ 'on-transparent': isTransparent }">
    <el-dropdown>
      <div class="theme-icon">
        <el-icon v-if="themeMode === 'dark'"><Moon /></el-icon>
        <el-icon v-else-if="themeMode === 'light'"><Sunny /></el-icon>
        <el-icon v-else><Monitor /></el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="setTheme('light')" :class="{ 'is-active': themeMode === 'light' }">
            <el-icon><Sunny /></el-icon> 浅色模式
          </el-dropdown-item>
          <el-dropdown-item @click="setTheme('dark')" :class="{ 'is-active': themeMode === 'dark' }">
            <el-icon><Moon /></el-icon> 深色模式
          </el-dropdown-item>
          <el-dropdown-item @click="setTheme('auto')" :class="{ 'is-active': themeMode === 'auto' }">
            <el-icon><Monitor /></el-icon> 跟随系统
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { Sunny, Moon, Monitor } from '@element-plus/icons-vue';

const props = defineProps({
  isTransparent: {
    type: Boolean,
    default: false
  }
});

// 主题模式: 'light', 'dark', 'auto'
const themeMode = ref('auto');
const systemDarkMode = ref(false);

// 监听系统主题变化
const setupSystemThemeListener = () => {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  
  // 获取当前系统主题
  systemDarkMode.value = mediaQuery.matches;
  
  // 监听系统主题变化
  const handleChange = (e) => {
    systemDarkMode.value = e.matches;
    if (themeMode.value === 'auto') {
      applyTheme();
    }
  };
  
  // 使用 addEventListener 兼容现代浏览器
  mediaQuery.addEventListener('change', handleChange);
};

// 设置主题
const setTheme = (mode) => {
  themeMode.value = mode;
  
  // 保存主题设置到本地存储
  localStorage.setItem('themeMode', mode);
  
  // 应用主题
  applyTheme();
};

// 应用主题
const applyTheme = () => {
  let isDark = false;
  
  if (themeMode.value === 'auto') {
    // 自动模式下跟随系统主题
    isDark = systemDarkMode.value;
  } else {
    // 手动模式下使用用户设置
    isDark = themeMode.value === 'dark';
  }
  
  // 应用主题类名
  if (isDark) {
    document.documentElement.classList.add('dark');
    document.body.classList.add('dark');
  } else {
    document.documentElement.classList.remove('dark');
    document.body.classList.remove('dark');
  }
};

// 监听主题模式变化
watch(themeMode, () => {
  applyTheme();
});

// 在组件挂载时读取保存的主题设置
onMounted(() => {
  // 设置系统主题监听
  setupSystemThemeListener();
  
  // 读取保存的主题设置
  const savedThemeMode = localStorage.getItem('themeMode');
  
  // 如果之前保存过设置则使用，否则默认使用自动模式
  if (savedThemeMode) {
    themeMode.value = savedThemeMode;
  } else {
    themeMode.value = 'auto';
  }
  
  // 应用主题
  applyTheme();
});
</script>

<style scoped>
/* 主题切换样式已在全局暗黑主题CSS中定义 */
.theme-switch {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  padding: 6px;
}

.theme-switch:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

body.dark .theme-switch:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.theme-icon {
  font-size: 18px;
  display: flex;
  align-items: center;
  color: #606266;
  transition: color 0.3s;
}

body.dark .theme-icon {
  color: #e0e0e0;
}

.theme-switch.on-transparent {
  background-color: rgba(255, 255, 255, 0.2);
}

body.dark .theme-switch.on-transparent {
  background-color: rgba(0, 0, 0, 0.3);
}

.theme-switch.on-transparent:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

body.dark .theme-switch.on-transparent:hover {
  background-color: rgba(0, 0, 0, 0.4);
}

.theme-switch.on-transparent .theme-icon {
  color: #333;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

body.dark .theme-switch.on-transparent .theme-icon {
  color: white;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.is-active {
  color: var(--el-color-primary);
  font-weight: bold;
}
</style> 