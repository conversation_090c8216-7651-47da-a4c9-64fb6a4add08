<template>
  <div class="vue-grid-waterfall" ref="waterfallContainer">
    <div 
      class="waterfall-column" 
      v-for="(column, index) in columns" 
      :key="index"
      :style="{ width: columnWidth + '%' }"
    >
      <div 
        v-for="item in column" 
        :key="item.sessionId"
        class="waterfall-item"
      >
        <slot name="slot-scope" :slotProps="{ data: item }"></slot>
      </div>
    </div>
    
    <!-- 加载更多触发区域 -->
    <div 
      v-if="!loading && hasMore" 
      class="load-more-trigger" 
      ref="loadMoreTrigger"
      :style="{ height: bottom + 'px' }"
    ></div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="waterfall-loading">
      <div class="loading-spinner"></div>
      <span>加载中...</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'

const props = defineProps({
  dataList: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Number,
    default: 3
  },
  loading: {
    type: Boolean,
    default: false
  },
  bottom: {
    type: Number,
    default: 50
  }
})

const emit = defineEmits(['getMoreData'])

const waterfallContainer = ref(null)
const loadMoreTrigger = ref(null)
const columns = ref([])
const hasMore = ref(true)

// 计算每列的宽度百分比
const columnWidth = computed(() => {
  return 100 / props.columns
})

// 初始化列
const initColumns = () => {
  columns.value = Array.from({ length: props.columns }, () => [])
}

// 分配数据到各列（使用最短列优先算法）
const distributeItems = () => {
  initColumns()
  
  props.dataList.forEach(item => {
    // 找到高度最小的列
    let minHeight = Infinity
    let minIndex = 0
    
    columns.value.forEach((column, index) => {
      const columnHeight = column.reduce((total, item) => {
        return total + parseFloat(item.height || '200')
      }, 0)
      
      if (columnHeight < minHeight) {
        minHeight = columnHeight
        minIndex = index
      }
    })
    
    // 将项目添加到最短的列
    columns.value[minIndex].push(item)
  })
}

// 设置 Intersection Observer 来监听滚动到底部
const setupIntersectionObserver = () => {
  if (!loadMoreTrigger.value) return
  
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !props.loading && hasMore.value) {
          emit('getMoreData')
        }
      })
    },
    {
      rootMargin: `${props.bottom}px`
    }
  )
  
  observer.observe(loadMoreTrigger.value)
  
  // 保存 observer 引用以便清理
  waterfallContainer.value._observer = observer
}

// 清理 observer
const cleanupObserver = () => {
  if (waterfallContainer.value?._observer) {
    waterfallContainer.value._observer.disconnect()
  }
}

// 监听数据变化，重新分配
watch(() => props.dataList, () => {
  nextTick(() => {
    distributeItems()
  })
}, { deep: true })

// 监听列数变化，重新分配
watch(() => props.columns, () => {
  nextTick(() => {
    distributeItems()
  })
})

onMounted(() => {
  distributeItems()
  nextTick(() => {
    setupIntersectionObserver()
  })
})

onUnmounted(() => {
  cleanupObserver()
})
</script>

<style scoped>
.vue-grid-waterfall {
  display: flex;
  width: 100%;
  gap: 4px;
  position: relative;
}

.waterfall-column {
  display: flex;
  flex-direction: column;
}

.waterfall-item {
  break-inside: avoid;
}

.load-more-trigger {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  pointer-events: none;
}

.waterfall-loading {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  color: #666;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e1e5e9;
  border-top: 2px solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vue-grid-waterfall {
    gap: 4px;
  }
}
</style>
