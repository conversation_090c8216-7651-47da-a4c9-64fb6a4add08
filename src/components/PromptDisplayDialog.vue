<template>
  <div v-if="visible" class="prompt-display-dialog-overlay">
    <div class="prompt-display-dialog" @click.stop>
      <div class="card-decoration top-left"></div>
      <div class="card-decoration top-right"></div>
      <div class="card-decoration bottom-left"></div>
      <div class="card-decoration bottom-right"></div>
      
      <div class="dialog-header">
        <h3 class="dialog-title">{{ title }}</h3>
        <button class="close-button" @click="handleClose">×</button>
      </div>
      
      <div class="dialog-content">
        <!-- JSON结构化显示 -->
        <div v-if="isJsonContent" class="optimization-result">
          <!-- 核心信息部分 -->
          <div class="section core-info-section" v-if="parsedContent.core_info">
            <h4 class="section-title">
              <span class="title-icon">🎯</span>
              核心信息
            </h4>
            <div class="core-info-content">
              <div class="topic-analysis" v-if="parsedContent.core_info.topic_analysis">
                <label>主题分析</label>
                <div class="readonly-content" v-html="parsedContent.core_info.topic_analysis"></div>
              </div>
              <div class="audience-insights" v-if="parsedContent.core_info.audience_insights">
                <label>受众洞察</label>
                <div class="readonly-content" v-html="parsedContent.core_info.audience_insights"></div>
              </div>
            </div>
          </div>
          
          <!-- 风格定位部分 -->
          <div class="section style-positioning-section" v-if="parsedContent.style_positioning">
            <h4 class="section-title">
              <span class="title-icon">🎨</span>
              风格定位
            </h4>
            <div class="style-positioning-content">
              <div class="style-item" v-if="parsedContent.style_positioning.content_style">
                <label>内容风格</label>
                <div class="readonly-content" v-html="parsedContent.style_positioning.content_style"></div>
              </div>
              <div class="style-item" v-if="parsedContent.style_positioning.visual_tone">
                <label>视觉色调</label>
                <div class="readonly-content" v-html="parsedContent.style_positioning.visual_tone"></div>
              </div>
              <div class="style-item" v-if="parsedContent.style_positioning.audio_style">
                <label>音频风格</label>
                <div class="readonly-content" v-html="parsedContent.style_positioning.audio_style"></div>
              </div>
            </div>
          </div>

          <!-- 视频规划部分 -->
          <div class="section video-planning-section" v-if="parsedContent.video_planning">
            <h4 class="section-title">
              <span class="title-icon">📊</span>
              视频规划
            </h4>
            <div class="video-planning-content">
              <div class="planning-item" v-if="parsedContent.video_planning.shot_count">
                <label>镜头数量</label>
                <div class="readonly-content">{{ parsedContent.video_planning.shot_count }}</div>
              </div>
            </div>
          </div>

          <!-- 视频结构部分 -->
          <div class="section video-structure-section" v-if="parsedContent.video_structure && Array.isArray(parsedContent.video_structure)">
            <h4 class="section-title">
              <span class="title-icon">🎬</span>
              视频结构
            </h4>
            <div class="video-structure-content">
              <div v-for="(segment, index) in parsedContent.video_structure"
                   :key="index"
                   class="structure-segment">
                <div class="segment-header">
                  <h5 class="segment-title">{{ segment.segment }}</h5>
                  <span class="segment-duration">{{ segment.duration }}</span>
                </div>
                <div class="segment-content">
                  <div class="detail-item" v-if="segment.visuals">
                    <label>视觉描述</label>
                    <div class="readonly-content" v-html="segment.visuals"></div>
                  </div>
                  <div class="detail-item" v-if="segment.script">
                    <label>脚本内容</label>
                    <div class="readonly-content script-content" v-html="segment.script"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 普通文本显示 -->
        <div v-else class="text-content">
          <!-- <div class="text-label">
            <span>{{ contentLabel || '内容详情' }}</span>
            <button v-if="content" class="copy-btn" @click="copyContent" title="复制内容">
              📋
            </button>
          </div> -->
          <div class="text-display">{{ content || '暂无内容' }}</div>
        </div>
      </div>
      
      <div class="dialog-footer">
        <button class="btn btn-secondary" @click="handleClose">
          关闭
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '内容详情'
  },
  content: {
    type: String,
    default: ''
  },
  contentLabel: {
    type: String,
    default: '内容详情'
  }
})

const emit = defineEmits(['update:visible', 'close'])

// 状态管理
const isJsonContent = ref(false)
const parsedContent = ref({})

// 检查内容是否为JSON结构并解析
const parseContent = () => {
  if (!props.content) {
    isJsonContent.value = false
    parsedContent.value = {}
    return
  }

  try {
    // 尝试解析JSON
    const parsed = JSON.parse(props.content)
    if (typeof parsed === 'object' && parsed !== null) {
      isJsonContent.value = true
      parsedContent.value = parsed
      return
    }
  } catch (error) {
    // 不是JSON，按普通文本处理
  }

  // 普通文本处理
  isJsonContent.value = false
  parsedContent.value = {}
}

// 监听内容变化
watch(() => props.content, () => {
  parseContent()
}, { immediate: true })

// 处理关闭
const handleClose = () => {
  emit('close')
  emit('update:visible', false)
}

// 复制内容
const copyContent = () => {
  if (!props.content) return

  navigator.clipboard.writeText(props.content)
    .then(() => {
      ElMessage.success('内容已复制到剪贴板')
    })
    .catch(err => {
      console.error('复制失败:', err)
      ElMessage.error('复制失败，请手动选择并复制')
    })
}
</script>

<style scoped>
.prompt-display-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  box-sizing: border-box;
}

.prompt-display-dialog {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  max-width: 800px;
  width: 100%;
  max-height: 92vh;
  position: relative;
  animation: dialog-appear 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  text-align: left;
}

body.dark .prompt-display-dialog {
  background: rgba(30, 30, 30, 0.95);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

@keyframes dialog-appear {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 装饰元素 */
.card-decoration {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(138, 92, 246, 0.1));
  filter: blur(20px);
  pointer-events: none;
  z-index: 0;
}

.top-left {
  top: -20px;
  left: -20px;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(99, 102, 241, 0.1));
}

.top-right {
  top: -30px;
  right: -30px;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(168, 85, 247, 0.1));
}

.bottom-left {
  bottom: -40px;
  left: -20px;
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.3), rgba(20, 184, 166, 0.1));
}

.bottom-right {
  bottom: -30px;
  right: -30px;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(20, 184, 166, 0.3), rgba(6, 182, 212, 0.1));
}

body.dark .card-decoration {
  opacity: 0.6;
}

/* 对话框头部 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px 0;
  position: relative;
  z-index: 1;
}

.dialog-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

body.dark .dialog-title {
  color: #f1f5f9;
}

.close-button {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background-color: rgba(148, 163, 184, 0.1);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 20px;
  color: #64748b;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  transform: rotate(90deg);
}

body.dark .close-button {
  background-color: rgba(148, 163, 184, 0.1);
  color: #94a3b8;
}

body.dark .close-button:hover {
  background-color: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

/* 对话框内容 */
.dialog-content {
  padding: 16px 24px;
  position: relative;
  z-index: 1;
}

/* 优化结果 */
.optimization-result {
  max-height: 65vh;
  overflow-y: auto;
  padding-right: 8px;
}

.section {
  margin-bottom: 20px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

body.dark .section {
  background: rgba(30, 41, 59, 0.5);
  border-color: rgba(71, 85, 105, 0.3);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 12px 0;
}

body.dark .section-title {
  color: #f1f5f9;
}

.title-icon {
  font-size: 16px;
}

/* 只读内容 */
.readonly-content {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 8px 12px;
  min-height: 18px;
  line-height: 1.5;
  color: #334155;
  font-size: 14px;
  white-space: pre-wrap;
  overflow-wrap: break-word;
}

body.dark .readonly-content {
  background: rgba(51, 65, 85, 0.5);
  border-color: #475569;
  color: #e2e8f0;
}

/* 核心信息内容 */
.core-info-content > div {
  margin-bottom: 12px;
}

.core-info-content > div:last-child {
  margin-bottom: 0;
}

.core-info-content label {
  display: block;
  font-weight: 500;
  color: #475569;
  margin-bottom: 6px;
  font-size: 13px;
}

body.dark .core-info-content label {
  color: #cbd5e1;
}

/* 风格定位内容 */
.style-positioning-content .style-item {
  margin-bottom: 12px;
}

.style-positioning-content .style-item:last-child {
  margin-bottom: 0;
}

.style-positioning-content label {
  display: block;
  font-weight: 500;
  color: #475569;
  margin-bottom: 6px;
  font-size: 13px;
}

body.dark .style-positioning-content label {
  color: #cbd5e1;
}

/* 视频规划内容 */
.video-planning-content .planning-item {
  margin-bottom: 12px;
}

.video-planning-content .planning-item:last-child {
  margin-bottom: 0;
}

.video-planning-content label {
  display: block;
  font-weight: 500;
  color: #475569;
  margin-bottom: 6px;
  font-size: 13px;
}

body.dark .video-planning-content label {
  color: #cbd5e1;
}

/* 视频结构内容 */
.video-structure-content .structure-segment {
  margin-bottom: 16px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  padding: 14px;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

body.dark .video-structure-content .structure-segment {
  background: rgba(51, 65, 85, 0.3);
  border-color: rgba(71, 85, 105, 0.3);
}

.segment-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
  margin-bottom: 10px;
}

.segment-title {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.segment-title::before {
  content: '';
  width: 3px;
  height: 12px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 2px;
}

body.dark .segment-title {
  color: #f1f5f9;
}

.segment-duration {
  font-weight: 600;
  color: #6366f1;
  font-size: 12px;
  background: rgba(99, 102, 241, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
}

body.dark .segment-duration {
  color: #818cf8;
  background: rgba(129, 140, 248, 0.1);
}

.segment-content .detail-item {
  margin-bottom: 8px;
}

.segment-content .detail-item:last-child {
  margin-bottom: 0;
}

/* 脚本内容特殊样式 */
.script-content {
  min-height: 40px;
  font-family: 'Courier New', monospace;
  background: rgba(248, 250, 252, 0.8) !important;
  border: 1px dashed #cbd5e1 !important;
  font-size: 13px;
}

body.dark .script-content {
  background: rgba(30, 41, 59, 0.6) !important;
  border-color: #64748b !important;
}

/* 普通文本显示 */
.text-content {
  max-height: 65vh;
  overflow-y: auto;
  padding-right: 8px;
}

.text-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  font-size: 16px;
  color: #1e293b;
  font-weight: 600;
  margin-bottom: 12px;
}

body.dark .text-label {
  color: #f1f5f9;
}

.text-display {
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 14px;
  color: #334155;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  font-family: 'Courier New', monospace;
}

body.dark .text-display {
  background: rgba(30, 41, 59, 0.5);
  border-color: rgba(71, 85, 105, 0.3);
  color: #e2e8f0;
}

.copy-btn {
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 6px;
  padding: 4px 8px;
  color: #6366f1;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.copy-btn:hover {
  background: rgba(99, 102, 241, 0.2);
  border-color: rgba(99, 102, 241, 0.5);
  transform: translateY(-1px);
}

body.dark .copy-btn {
  background: rgba(99, 102, 241, 0.2);
  border-color: rgba(99, 102, 241, 0.4);
  color: #818cf8;
}

body.dark .copy-btn:hover {
  background: rgba(99, 102, 241, 0.3);
  border-color: rgba(99, 102, 241, 0.6);
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 0 24px 20px;
  position: relative;
  z-index: 1;
}

/* 按钮样式 */
.btn {
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  min-width: 100px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-secondary {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover:not(:disabled) {
  background: #f1f5f9;
  color: #475569;
  transform: translateY(-1px);
}

body.dark .btn-secondary {
  background: #374151;
  color: #9ca3af;
  border-color: #4b5563;
}

body.dark .btn-secondary:hover:not(:disabled) {
  background: #4b5563;
  color: #d1d5db;
}

/* 增强可读性的样式 */
.core-info-content > div + div,
.style-positioning-content .style-item + .style-item,
.video-planning-content .planning-item + .planning-item {
  border-top: 1px solid rgba(226, 232, 240, 0.3);
  padding-top: 12px;
}

body.dark .core-info-content > div + div,
body.dark .style-positioning-content .style-item + .style-item,
body.dark .video-planning-content .planning-item + .planning-item {
  border-top-color: rgba(71, 85, 105, 0.3);
}

/* 标签样式优化 */
label {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: #64748b !important;
  font-size: 11px !important;
}

body.dark label {
  color: #94a3b8 !important;
}

/* 紧凑的视频片段布局 */
.structure-segment {
  border-left: 3px solid #6366f1;
  border-radius: 0 8px 8px 0 !important;
}

.structure-segment:nth-child(even) {
  border-left-color: #8b5cf6;
}

.structure-segment:nth-child(3n) {
  border-left-color: #06b6d4;
}

/* 确保所有文本左对齐 */
.prompt-display-dialog * {
  text-align: left;
}

.prompt-display-dialog .dialog-footer {
  text-align: center;
}

.prompt-display-dialog h1,
.prompt-display-dialog h2,
.prompt-display-dialog h3,
.prompt-display-dialog h4,
.prompt-display-dialog h5,
.prompt-display-dialog h6,
.prompt-display-dialog p,
.prompt-display-dialog div,
.prompt-display-dialog span,
.prompt-display-dialog label {
  text-align: left;
}

/* 滚动条样式 */
.optimization-result::-webkit-scrollbar,
.text-content::-webkit-scrollbar {
  width: 6px;
}

.optimization-result::-webkit-scrollbar-track,
.text-content::-webkit-scrollbar-track {
  background: rgba(226, 232, 240, 0.3);
  border-radius: 3px;
}

.optimization-result::-webkit-scrollbar-thumb,
.text-content::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.5);
  border-radius: 3px;
}

.optimization-result::-webkit-scrollbar-thumb:hover,
.text-content::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.7);
}

body.dark .optimization-result::-webkit-scrollbar-track,
body.dark .text-content::-webkit-scrollbar-track {
  background: rgba(71, 85, 105, 0.3);
}

body.dark .optimization-result::-webkit-scrollbar-thumb,
body.dark .text-content::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.4);
}

body.dark .optimization-result::-webkit-scrollbar-thumb:hover,
body.dark .text-content::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.6);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .prompt-display-dialog {
    margin: 10px;
    max-width: calc(100vw - 20px);
    border-radius: 16px;
  }

  .dialog-header,
  .dialog-content,
  .dialog-footer {
    padding-left: 20px;
    padding-right: 20px;
  }

  .dialog-title {
    font-size: 18px;
  }

  .section {
    padding: 12px;
  }

  .dialog-footer {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}
</style>
