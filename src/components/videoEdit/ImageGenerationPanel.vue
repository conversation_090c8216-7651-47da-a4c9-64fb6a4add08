<template>
  <div class="image-generation-panel">
    <div class="panel-section">
      <div class="form-item">
        <el-input v-model="shotData.imagePrompt" type="textarea" placeholder="我是图片生成的文字描述" @change="updateShot"
          :autosize="{ minRows: 3, maxRows: 6 }" class="image-generation-textarea" />
        <div class="character-count">{{ shotData.imagePrompt?.length || 0 }}/800</div>
      </div>
    </div>

    <div class="panel-section">
      <div class="form-label">生成比例</div>
      <DropdownPanel v-model="showImageRatioSelector" position="bottom" align="center" closeOnClickOutside
        :width="300" :zIndex="1200" :maxHeight="400">
        <!-- 触发器插槽 -->
        <template #trigger>
          <div class="current-ratio">
            <span class="current-ratio-text">{{ imageAspectRatio }}</span>
            <span class="current-ratio-desc">{{ getRatioDescription(imageAspectRatio) }}</span>
            <el-icon class="expand-icon" :class="{ 'rotate': showImageRatioSelector }">
              <ArrowDown />
            </el-icon>
          </div>
        </template>

        <!-- 下拉内容插槽 -->
        <RatioSelector v-model="imageAspectRatio" @update:modelValue="handleImageRatioChange" />
      </DropdownPanel>
    </div>

    <div class="panel-section">
      <div class="form-label">参考图</div>

      <!-- 显示已选择的参考图 -->
      <div v-if="selectedFileUrl || shotData.referenceImage" class="selected-reference-image">
        <div class="reference-image-preview" @click="handleImageClick(selectedFileUrl || shotData.referenceImage)">
          <img :src="selectedFileUrl || shotData.referenceImage" alt="参考图" />
          <div class="reference-image-actions">
            <div class="custom-delete-button" @click.stop="clearReferenceImage">
              <el-icon>
                <Close />
              </el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 使用按钮打开资产选择器弹框 -->
      <div v-else class="reference-image-options" @click="openAssetSelector">
        <div class="duration-option">
          <el-icon>
            <Plus />
          </el-icon>
          <span></span>
        </div>
      </div>
    </div>

    <!-- 添加生成强度控制 -->
    <div class="panel-section">
      <div class="form-item voice-param-item">
        <div class="voice-param-header">
          <div class="voice-param-label">强度（值越大，参考图影响越小）</div>
          <div class="voice-param-value">{{ imageStrength.toFixed(1) }}</div>
        </div>
        <el-slider v-if="shotData && shotData.id" v-model="imageStrength" :min="1.0" :max="10.0" :step="0.1"
          :format-tooltip="(val) => val.toFixed(1)" @change="handleImageStrengthChange" />
      </div>
    </div>

    <div class="panel-actions">
      <GenerateButton
        :text="shotData.shotStatus == '1' ? '生成中...' : isGeneratingImage ? '处理中...' : '生成图片'"
        :loading="isGeneratingImage || shotData.shotStatus == '1'"
        :icon="Picture"
        credits="⚡ 30"
        @click="generateImage"
      />
    </div>

    <!-- 资产选择器弹框 -->
    <AssetSelectorDialog 
      v-model:visible="showAssetSelectorDialog"
      :canvasId="canvasId"
      title="选择参考图"
      :type="1"
      @select="handleAssetSelected"
      @upload="handleFileUploaded"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { Picture, ArrowDown, Plus, Close } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

import DropdownPanel from '../parent/DropdownPanel.vue';
import RatioSelector from '../selector/RatioSelector.vue';
import AssetSelectorDialog from './AssetSelectorDialog.vue';
import GenerateButton from './GenerateButton.vue';
import { uploadToOSS } from '@/api/oss.js';
import { generateCanvasImage, addCanvasMaterial } from '@/api/auth.js';

// 组件属性
const props = defineProps({
  shot: {
    type: Object,
    required: true
  },
  canvasId: {
    type: [Number, String],
    default: ''
  }
});

// 事件
const emit = defineEmits(['update:shot', 'generate-image', 'refresh-canvas']);

// 本地数据副本
const shotData = ref({ ...props.shot });

// 文件上传相关状态
const fileInputRef = ref(null);
const selectedFile = ref(null);
const selectedFileUrl = ref('');
const isUploading = ref(false);
const uploadProgress = ref(0);

// 图片比例相关状态
const imageAspectRatio = ref('16:9');
const showImageRatioSelector = ref(false);

// 图片生成强度
const imageStrength = ref(5.0);

// 图片生成状态
const isGeneratingImage = ref(false);

// 资产选择器弹框状态
const showAssetSelectorDialog = ref(false);

// 处理图片点击事件
const handleImageClick = (image) => {
  window.openImagePreview(image, "")
}

// 监听外部shot变化
watch(() => props.shot, (newShot) => {
  if (!newShot) return;

  shotData.value = { ...newShot };
  // 同步更新比例值
  if (newShot.imageAspectRatio) {
    imageAspectRatio.value = newShot.imageAspectRatio;
  }
  // 同步更新图片生成强度
  if (newShot.imageStrength) {
    imageStrength.value = newShot.imageStrength;
  }
}, { deep: true });

// 不再需要监听showReferenceSelector

// 更新分镜数据
const updateShot = () => {
  // emit('update:shot', { ...shotData.value });
};

// 生成图片
const generateImage = () => {
  // 如果已经在生成中，则不重复操作
  if (isGeneratingImage.value || shotData.value.shotStatus == '1') {
    return;
  }

  if (!shotData.value.imagePrompt) {
    ElMessage.warning('请先填写画面描述');
    return;
  }

  if (!shotData.value.id) {
    ElMessage.warning('请先创建分镜');
    return;
  }

  // 设置生成状态为true
  isGeneratingImage.value = true;

  let referenceImageUrl = shotData.value.referenceImage || '';
  // 检查是否以 http:// 或 https:// 开头
  if (/^https?:\/\//.test(referenceImageUrl)) {
    // 去除协议和域名部分
    referenceImageUrl = referenceImageUrl.replace(/^https?:\/\/[^\/]+/, '');
  }

  // 准备API参数
  const params = {
    prompt: shotData.value.imagePrompt,
    referenceImageUrl: referenceImageUrl, // 隐藏参考图
    aspectRatio: shotData.value.imageAspectRatio || '16:9',
    strength: shotData.value.imageStrength || 5.0,
    shotId: shotData.value.id
  };

  // 调用API
  ElMessage.info('正在生成图片，请稍候...');
  generateCanvasImage(params)
    .then(response => {
      if (response.success) {
        ElMessage.success('图片生成任务已提交');
        // 通知父组件刷新
        emit('refresh-canvas');
      } else {
        ElMessage.error(response.errMessage || '图片生成失败');
      }
    })
    .catch(error => {
      console.error('生成图片出错:', error);
      ElMessage.error('生成图片时发生错误，请重试');
    })
    .finally(() => {
      // 无论成功还是失败，最终都将生成状态设置为false
      isGeneratingImage.value = false;
    });
};

// 打开资产选择器弹框
const openAssetSelector = () => {
  showAssetSelectorDialog.value = true;
};

// 处理选择的资产
const handleAssetSelected = (imageUrl) => {

  // 更新shotData中的参考图URL
  shotData.value.referenceImage = imageUrl;
  updateShot();

  ElMessage.success('已选择参考图');
};

// 处理上传的文件
const handleFileUploaded = async (file) => {
  if (!file) return;

  isUploading.value = true;
  uploadProgress.value = 0;
  selectedFileUrl.value = URL.createObjectURL(file);

  try {
    // 假设shotData.id可以作为conversationId使用
    const conversationId = shotData.value.id || 'default';
    const result = await uploadToOSS(file, conversationId);

    if (result && result.url) {
      shotData.value.referenceImage = result.url;
      
      // 如果有canvasId，则添加素材到画布
      if (props.canvasId) {
        // 确定文件类型
        const materialType = file.type.startsWith('image/') ? 1 : 2; // 1-图片,2-视频
        
        // 构造addCanvasMaterial所需的参数
        const materialParams = {
          canvasId: Number(props.canvasId),
          materialType: materialType,
          materialUrl: result.objectName,
          materialName: file.name || '未命名素材',
          materialDesc: file.name || '未命名素材'
        };
        
        // 调用API添加素材到画布
        const addResult = await addCanvasMaterial(materialParams);
        
        if (!addResult.success) {
          ElMessage.warning(`参考图上传成功，但添加到画布失败: ${addResult.errMessage || '未知错误'}`);
        }
      }
      
      ElMessage.success('参考图上传成功');
      updateShot();
      uploadProgress.value = 100;
    } else {
      throw new Error('上传失败，未获取到URL');
    }
  } catch (error) {
    console.error('上传参考图失败:', error);
    ElMessage.error('上传参考图失败，请重试');
    uploadProgress.value = 0;
  } finally {
    isUploading.value = false;
  }
};

// 清除参考图
const clearReferenceImage = () => {
  shotData.value.referenceImage = null;
  selectedFileUrl.value = '';
  updateShot();
};

// 处理图片比例变化
const handleImageRatioChange = (newRatio) => {
  imageAspectRatio.value = newRatio;
  shotData.value.imageAspectRatio = newRatio;
  showImageRatioSelector.value = false;
  updateShot();
};

// 处理图片生成强度变化
const handleImageStrengthChange = (newStrength) => {
  imageStrength.value = newStrength;
  shotData.value.imageStrength = newStrength;
  updateShot();
};

// 获取比例描述
const getRatioDescription = (ratio) => {
  switch (ratio) {
    case '16:9': return '横版 (适合电脑/电视)'
    case '9:16': return '竖版 (适合手机)'
    case '3:4': return '竖版 (适合平板/印刷)'
    case '2:3': return '竖版 (适合书籍/杂志)'
    case '1:1': return '方形 (适合社交媒体)'
    case '3:2': return '横版 (适合传统相机)'
    case '4:3': return '横版 (适合传统显示器)'
    case '21:9': return '超宽 (适合电影/游戏)'
    default: return ''
  }
};

// 组件挂载时初始化
onMounted(() => {
  // 确保shotData存在
  if (!shotData.value) {
    console.warn('ImageGenerationPanel: shotData is null during mount');
    return;
  }

  // 初始化比例值
  if (shotData.value.imageAspectRatio) {
    imageAspectRatio.value = shotData.value.imageAspectRatio;
  } else {
    shotData.value.imageAspectRatio = imageAspectRatio.value;
  }

  // 初始化图片生成强度
  if (shotData.value.imageStrength) {
    imageStrength.value = shotData.value.imageStrength;
  } else {
    shotData.value.imageStrength = imageStrength.value;
  }

  updateShot();
});
</script>

<style scoped>
.image-generation-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 12px;
  padding: 0 16px 16px 16px;
}

.panel-section {
  /* margin-bottom: 15px; */
}

.image-generation-textarea {
  background-color: #f5f7fa;
  border-radius: 8px;
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.1) !important;
  padding-bottom: 28px;
}

body.dark .image-generation-textarea {
  background-color: rgba(204, 221, 255, .06);
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.1) !important;
}

.form-item {
  margin-bottom: 0px;
  position: relative;
}

.form-label {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 5px;
  text-align: left;
}

body.dark .form-label {
  color: var(--text-primary);
}

.panel-actions {
  margin-top: auto;
  padding: 0;
  display: flex;
  justify-content: center;
}

/* 面板操作区域 */

.loading-icon {
  animation: rotating 2s linear infinite;
}

.circular-icon {
  height: 16px;
  width: 16px;
  animation: rotating 2s linear infinite;
}

.path {
  stroke: white;
  stroke-width: 3;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-linecap: round;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }

  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

/* Character count */
.character-count {
  position: absolute;
  right: 4px;
  bottom: 4px;
  text-align: right;
  font-size: 12px;
  color: #909399;
}

/* 当前尺寸显示样式 */
.current-ratio {
  width: 290px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 14px;
  border-radius: 8px;
  background-color: #f5f7fa;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 10px;
  box-sizing: border-box;
}

body.dark .current-ratio {
  background-color: rgba(204, 221, 255, .06);
}

.current-ratio:hover {
  border-color: #a5b4fc;
  background-color: #f9faff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

body.dark .current-ratio:hover {
  border-color: #818cf8;
  background-color: var(--bg-hover, #2a2a3c);
}

.current-ratio-text {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  margin-right: 10px;
}

body.dark .current-ratio-text {
  color: var(--text-primary, #e2e8f0);
}

.current-ratio-desc {
  flex: 1;
  font-size: 14px;
  color: #64748b;
  flex-grow: 1;
  text-align: left;
}

body.dark .current-ratio-desc {
  color: var(--text-secondary, #94a3b8);
}

.expand-icon {
  font-size: 16px;
  color: #64748b;
  transition: transform 0.3s ease;
}

.expand-icon.rotate {
  transform: rotate(180deg);
}

body.dark .expand-icon {
  color: var(--text-secondary, #94a3b8);
}

/* Duration options */
.duration-option {
  padding: 8px 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  text-align: center;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

.duration-option.active {
  background-color: #ecf5ff;
  color: #409eff;
  border: 1px solid #d9ecff;
}

body.dark .duration-option {
  background-color: rgba(204, 221, 255, .06);
}

body.dark .duration-option.active {
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
}

/* 参考图相关样式 */
.selected-reference-image {
  cursor: pointer;
}

.selected-reference-image:hover .reference-image-actions{
  display: flex;
}

.reference-image-preview {
  position: relative;
}

.reference-image-actions {
  position: absolute;
  top: 0;
  left: 0;
  padding: 4px;
  display: none;
}

.reference-selector-container {
  display: flex;
  flex-direction: column;
}

.reference-selector-tabs {
  display: flex;
  gap: 4px;
  padding: 4px;
  border-bottom: 1px solid #ebeef5;
}

body.dark .reference-selector-tabs {
  border-bottom-color: var(--border-color);
}

.reference-tab {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
}

.reference-tab:hover {
  background-color: #ecf5ff;
}

.reference-tab.active {
  background-color: #ecf5ff;
  color: #409eff;
  border: 1px solid #d9ecff;
}

body.dark .reference-tab {
  background-color: rgba(204, 221, 255, .06);
}

body.dark .reference-tab:hover {
  background-color: rgba(204, 221, 255, .1);
}

body.dark .reference-tab.active {
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
}

.asset-selector-panel {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

body.dark .asset-selector-panel {
  background-color: rgba(204, 221, 255, .06);
}

.asset-selector-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.asset-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2px;
}

.asset-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

.asset-card {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
}

.asset-card.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

body.dark .asset-card {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

body.dark .asset-card.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.3);
}

.asset-preview {
  aspect-ratio: 1 / 1;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

body.dark .asset-preview {
  background-color: var(--bg-tertiary);
}

.asset-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.3s ease-in;
}

.asset-image.loaded {
  opacity: 1;
}

.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #909399;
  font-size: 24px;
}

body.dark .image-placeholder {
  color: var(--text-secondary);
}

.empty-assets {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
}

.empty-icon {
  font-size: 32px;
  color: #c0c4cc;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
  color: #909399;
}

body.dark .empty-icon {
  color: #606266;
}

body.dark .empty-text {
  color: var(--text-secondary);
}

.asset-pagination {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

.reference-image-preview{
  text-align: left;
}

.reference-image-preview img {
  max-width: 140px;
  max-height: 140px;
  object-fit: contain;
  border-radius: 8px;
  background-color: #f5f7fa;
}

body.dark .reference-image-preview img {
  background-color: rgba(204, 221, 255, .06);
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(64, 158, 255, 0.2);
  border-top-color: #409eff;
  border-radius: 50%;
  animation: spin 1s infinite linear;
  margin-bottom: 10px;
}

body.dark .loading-spinner {
  border: 3px solid rgba(64, 158, 255, 0.1);
  border-top-color: var(--primary-color);
}

.loading-text {
  color: #909399;
  font-size: 14px;
  transition: color 0.3s;
}

body.dark .loading-text {
  color: var(--text-secondary);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 修复参考图样式 */
.reference-image-options {
  width: 140px;
  aspect-ratio: 16 / 9;
  flex: 1;
  display: flex;
  gap: 10px;
  margin-top: 5px;
  cursor: pointer;
}

.voice-param-item {
}

.voice-param-item .el-slider {
  padding: 0 4px;
  box-sizing: border-box;
}

.voice-param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.voice-param-label {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
}

.voice-param-value {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

body.dark .voice-param-label {
  color: var(--text-primary);
}

body.dark .voice-param-value {
  color: var(--text-primary);
}

/* 自定义滑块颜色 */
.voice-param-item :deep(.el-slider__bar) {
  background-color: #706cef8d;
}

.voice-param-item :deep(.el-slider__button) {
  border-color: #706cef9a;
}

body.dark .voice-param-item :deep(.el-slider__bar) {
  background-color: rgba(112, 108, 239, 0.542);
}

body.dark .voice-param-item :deep(.el-slider__button) {
  border-color: rgba(112, 108, 239, 0.46);
}

/* 自定义删除按钮样式 */
.custom-delete-button {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #7a7a7a71;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  padding: 0;
}

.custom-delete-button:hover {
  background-color: #fd4a4a;
  transform: scale(1.05);
}

body.dark .custom-delete-button {
  background-color: #7a7a7a71;
}

body.dark .custom-delete-button:hover {
  background-color: #fd4a4a;
}

/* 辅助类 */
.ml-5 {
  margin-left: 5px;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-textarea__inner) {
  font-size: 13px;
}

:deep(.el-input__inner) {
  font-size: 13px;
}

:deep(.el-slider__runway) {
  margin: 8px 0;
}

:deep(.el-slider__input) {
  width: 60px;
  margin-left: 10px;
}
</style> 