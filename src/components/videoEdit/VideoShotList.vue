<template>
  <div class="video-shot-list">
    <!-- <div class="shot-list-header">
      <h3>分镜列表</h3>
      <el-button type="primary" size="small" @click="addNewShot">
        <el-icon><Plus /></el-icon> 新增分镜
      </el-button>
    </div> -->

    <div class="shot-list-container" ref="shotListContainer">

      <div class="shot-list-container-shot">
        <draggable v-model="internalShots" :key="viewMode" class="shot-list" item-key="id" :animation="150"
          ghost-class="ghost-shot" drag-class="dragging-shot" handle=".shot-thumbnail">
          <!-- 默认视图 -->
          <template #item="{ element, index }" v-if="viewMode === 'default'">
            <div class="shot-item-wrapper">
              <div class="shot-item" :class="{ 'active': modelValue === index }" :style="{ width: `${getShotWidth()}px` }">
                <div class="shot-thumbnail" :style="{ aspectRatio: `${aspectRatio.split(':')[0]}/${aspectRatio.split(':')[1]}` }">
                  <!-- 角标 -->
                  <div class="shot-type-badge" v-if="element.type && element.type == 'video'">
                    <span v-if="element.type === 'video'">动态</span>
                    <!-- <span v-else-if="element.type === 'image'">静态</span> -->
                  </div>

                  <!-- <video v-if="element.type === 'video'" :src="element.videoUrl" class="video-thumbnail"
                  :ref="el => { if (el) setVideoRef(el, index) }" muted preload="metadata"
                  @click="selectShot(index, 'video')" /> -->

                  <img v-if="element.type === 'video'"
                    :src="`${element.videoUrl}?x-oss-process=video/snapshot,t_0,f_jpg`" alt="分镜缩略图"
                    @click="selectShot(index, 'video')" />

                  <img v-else-if="element.type === 'image'" :src="element.imageUrl" alt="分镜缩略图"
                    @click="selectShot(index, 'video')" />

                  <div v-else class="add-resource" @click="selectShot(index, 'image')">
                    <span>点击右侧素材库添加</span>
                  </div>

                  <!-- element.type -->
                  <div v-if="element.shotStatus == '1'" class="loading-content"
                    @click="selectShot(index, 'video')">
                    <div class="loading-overlay">
                      <el-icon class="loading-icon">
                        <svg class="circular-icon" viewBox="0 0 50 50">
                          <circle class="path" cx="25" cy="25" r="20" fill="none" />
                        </svg>
                      </el-icon>
                      <!-- <div class="loading-text">生成中...</div> -->
                    </div>
                  </div>

                  <div class="shot-wrapper">
                    <div class="shot-id">分镜{{ element.sortOrder }}</div>
                    <div class="shot-duration">{{ formatDuration(getShotDuration(element)) }}</div>
                  </div>
                  <!-- <div class="drag-handle">
                  <el-icon><Operation /></el-icon>
                </div> -->
                </div>

                <div class="shot-info" @click="selectShot(index, 'audio')">
                  <!-- <div class="shot-narration" v-if="element.text">{{ element.text }}</div> -->
                  <div class="shot-narration" v-if="element.audios && element.audios.length > 0">
                    {{element.audios.map(voice => {
                      if(voice.audioType == 2){
                        return `音效~`
                      }
                      return voice.text
                    }).join('丨')}}
                  </div>
                  <div class="shot-narration not-narration" v-else>设置声音</div>
                  <!-- <div class="shot-id">{{ element.id }}</div> -->
                  <!-- <div class="shot-type">{{ element.type }}</div> -->
                </div>

                <div class="shot-actions" v-if="!isPlaying">
                  <el-button type="danger" size="small" circle @click.stop="removeShot(index)">
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </el-button>
                </div>
                <!-- 播放进度指示器 -->
                <div class="shot-progress-indicator" v-if="currentShotIndex === index && isPlaying"
                  :style="{ left: `${getShotProgressPercentage(element, index)}%` }"></div>
              </div>

              <!-- 分镜之间的添加按钮 -->
              <div class="shot-insert-button" @click.stop="addNewShot(index)" v-if="index !== internalShots.length - 1 && !isPlaying">
                <div class="insert-button-inner">
                  <el-icon>
                    <Plus />
                  </el-icon>
                </div>
              </div>
            </div>
          </template>

          <!-- 时间线视图 -->
          <template #item="{ element, index }" v-else>
            <div class="shot-item-wrapper"
                  :style="`width:${(getShotDuration(element) / 1000) * timelineScale}px`">
              <div class="shot-thumbnail-item">

                <div class="shot-item" :class="{ 'active': modelValue === index }">

                  <div class="shot-timeline timeline-thumbnail" :class="{ 'playing': isPlaying }">

                    <!-- 角标 -->
                    <div class="shot-type-badge" v-if="element.type && element.type == 'video'">
                      <span v-if="element.type === 'video'">动态</span>
                      <!-- <span v-else-if="element.type === 'image'">静态</span> -->
                    </div>

                    <!-- <video v-if="element.type === 'video'" :src="element.videoUrl" class="video-thumbnail"
                      :ref="el => { if (el) setVideoRef(el, index) }" muted preload="metadata"
                      @click="selectShot(index, 'video')" /> -->

                    <img v-if="element.type === 'video'"
                      :src="`${element.videoUrl}?x-oss-process=video/snapshot,t_0,f_jpg`" alt="分镜缩略图"
                      @click="selectShot(index, 'video')" />

                    <img v-else-if="element.type === 'image'"
                      :src="`${element.imageUrl}?x-oss-process=image/resize,w_300`" alt="分镜缩略图"
                      @click="selectShot(index, 'video')" />

                    <div v-else class="add-resource" @click="selectShot(index, 'image')">
                      <span>点击右侧素材库添加</span>
                    </div>
                    <div class="shot-wrapper">
                      <div class="shot-id">分镜{{ element.sortOrder }}</div>
                      <div class="shot-duration">{{ formatDuration(getShotDuration(element)) }}</div>
                    </div>

                    <!-- playDuration 拖拽手柄 -->
                    <div class="play-duration-handle"
                         v-if="!isPlaying"
                         @mousedown.stop="startPlayDurationResize(element, index, $event)"
                         :title="`拖拽调整播放时长: ${formatDuration(getShotDuration(element))}`">
                      <div class="handle-line"></div>
                      <div class="handle-grip"></div>
                    </div>

                    <div v-if="element.shotStatus == '1'" class="loading-content"
                      @click="selectShot(index, 'video')">
                      <div class="loading-overlay">
                        <el-icon class="loading-icon">
                          <svg class="circular-icon" viewBox="0 0 50 50">
                            <circle class="path" cx="25" cy="25" r="20" fill="none" />
                          </svg>
                        </el-icon>
                        <!-- <div class="loading-text">生成中...</div> -->
                      </div>
                    </div>
                  </div>
                  <div class="shot-actions" v-if="!isPlaying">
                    <el-button type="danger" size="small" circle @click.stop="removeShot(index)">
                      <el-icon>
                        <Delete />
                      </el-icon>
                    </el-button>
                  </div>
                </div>
                <div class="shot-text-audio-list">
                  <!-- 显示音频列表 -->
                  <div class="audio-timeline" v-if="element.audios && element.audios.length > 0">
                    <div v-for="(audio, audioIndex) in element.audios" :key="audio.id" class="audio-item" :style="{
                      width: `${(audio.audioDuration / 1000) * timelineScale}px`,
                      // backgroundColor: audioIndex % 2 === 0 ? '#706cef27' : '#706cef27' // #706cef33
                    }" @click="selectShot(index, 'audio')">
                      <div class="audio-text" v-if="audio.audioType == 1">{{ audio.text }}</div>
                      <div class="audio-text" v-else-if="audio.audioType == 2">音效</div>
                      <!-- <div class="audio-duration">{{ formatDuration(audio.audioDuration) }}</div> -->
                    </div>
                  </div>
                  <div v-else class="no-audio-placeholder" @click="selectShot(index, 'audio')">
                    <span>添加</span>
                  </div>
                </div>
              </div>
              <!-- 播放进度指示器  && isPlaying -->
              <div class="shot-progress-indicator" v-if="currentShotIndex === index && isPlaying"
                :style="{ left: `${getShotProgressPercentage(element, index)}%` }"></div>

              <!-- 分镜之间的添加按钮 -->
              <div class="shot-insert-button" @click.stop="addNewShot(index)" v-if="index !== internalShots.length - 1 && !isPlaying">
                <div class="insert-button-inner">
                  <el-icon>
                    <Plus />
                  </el-icon>
                </div>
              </div>
            </div>

          </template>
        </draggable>

        <!-- 添加分镜按钮 -->
        <div class="shot-item add-shot" @click="addNewShot(internalShots.length - 1)" v-if="! isPlaying">
          <div class="add-shot-content">
            <el-icon>
              <Plus />
            </el-icon>
            <span>创建空白分镜</span>
          </div>
        </div>
      </div>

      <!-- 背景音乐音轨  v-if="viewMode === 'timeline'" -->
      <div class="shot-bg-audio-list" v-if="viewMode === 'timeline' && bgAudioTrack">
        <BgAudioClip
          v-model:bgAudioTrack="bgAudioTrack"
          :timelineScale="timelineScale"
          :currentPlaybackTime="currentPlaybackTime"
          :isPlaying="isPlaying"
          :totalDuration="totalDuration"
          :setBackgroundMusic="setBackgroundMusic"
          @play-audio="playBgAudio"
          @stop-audio="stopBgAudio"
          @update:totalDuration="updateTotalDuration"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, onBeforeUnmount, nextTick } from 'vue';
import { Plus, Delete, Operation, Picture, VideoPlay } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import draggable from 'vuedraggable';
import BgAudioClip from './BgAudioClip.vue';

// 组件属性
const props = defineProps({
  shots: {
    type: Array,
    required: true
  },
  modelValue: {
    type: Number,
    default: 0
  },
  viewMode: {
    type: String,
    default: 'default'
  },
  currentPlaybackTime: {
    type: Number,
    default: 0
  },
  currentShotIndex: {
    type: Number,
    default: 0
  },
  isPlaying: {
    type: Boolean,
    default: false
  },
  timelineScale: {
    type: Number,
    default: 20
  },
  aspectRatio: {
    type: String,
    default: '16:9'
  },
  bgAudioTrack: {
    type: Object,
    default: () => null
  },
  setBackgroundMusic: {
    type: Function,
    default: () => {}
  }
});

// 事件
const emit = defineEmits(['update:modelValue', 'add', 'remove', 'shot-select', 'reorder', 'add-after', 'update:timelineScale', 'update:bgAudioTrack', 'update-play-duration']);

// 内部维护一个shots的副本，用于拖拽排序
const internalShots = computed({
  get: () => props.shots,
  set: (value) => {
    // 当拖拽排序改变时，通知父组件
    emit('reorder', value);
  }
});

// 视频引用
const videoRefs = ref([]);

// shot-list-container 引用
const shotListContainer = ref(null);

// 音频相关
const bgAudioTrack = ref(null);
const totalDuration = ref(10000); // 默认60秒
const audioPlayer = ref(null);
const bgAudioPlayer = ref(null);

// playDuration 拖拽状态
const playDurationResizing = ref({
  active: false,
  shotIndex: -1,
  shot: null,
  startX: 0,
  originalDuration: 0,
  minDuration: 0
});

// 监听shots变化，加载视频首帧
watch(() => props.shots, (newShots) => {
  // 重置视频引用数组
  videoRefs.value = new Array(newShots.length);
  
  // 计算视频总时长
  let duration = 0;
  for (const shot of newShots) {
    duration += getShotDuration(shot);
  }
  
  // 确保总时长至少为10秒
  totalDuration.value = Math.max(duration, 10000);
}, { deep: true, immediate: true });

// 监听 bgAudioTrack 变化
watch(() => props.bgAudioTrack, (newBgAudioTrack) => {
  bgAudioTrack.value = newBgAudioTrack;
  console.log('props.bgAudioTrack updated:', newBgAudioTrack);
}, { deep: true });

// 监听内部 bgAudioTrack 变化，将变化发送给父组件
watch(bgAudioTrack, (newBgAudioTrack) => {
  emit('update:bgAudioTrack', newBgAudioTrack);
  console.log('bgAudioTrack updated:', newBgAudioTrack);
}, { deep: true });

// 监听分镜选中变化，滚动到选中分镜居中位置
watch(() => props.modelValue, (newIndex, oldIndex) => {
  if (newIndex !== oldIndex && newIndex >= 0 && newIndex < props.shots.length) {
    scrollToSelectedShot(newIndex);
  }
}, { immediate: false });

// 鼠标滚轮横向滚动处理函数
const handleWheelScroll = (event) => {
  // 检查是否有横向滚动的内容
  if (shotListContainer.value) {
    const container = shotListContainer.value;
    const hasHorizontalScroll = container.scrollWidth > container.clientWidth;

    if (hasHorizontalScroll) {
      // 只处理纯垂直滚动（鼠标滚轮），不影响触摸板的横向滚动
      // 如果有横向滚动分量（deltaX），说明是触摸板横向滚动，不处理
      if (Math.abs(event.deltaX) > 0) {
        return; // 让触摸板横向滚动正常工作
      }

      // 只有纯垂直滚动时才转换为横向滚动（鼠标滚轮）
      if (Math.abs(event.deltaY) > 0) {
        // 阻止默认的垂直滚动
        event.preventDefault();

        // 将垂直滚动转换为横向滚动
        const scrollAmount = event.deltaY;
        container.scrollLeft += scrollAmount;
      }
    }
  }
};

// 初始化视频引用数组
onMounted(() => {
  videoRefs.value = new Array(props.shots.length);
  bgAudioTrack.value = props.bgAudioTrack; // 初始化 bgAudioTrack 的值

  // 添加鼠标滚轮事件监听器
  if (shotListContainer.value) {
    shotListContainer.value.addEventListener('wheel', handleWheelScroll, { passive: false });
  }
});

// 加载视频缩略图方法已移至 setVideoRef 中处理

// 选择分镜
const selectShot = (index, type) => {
  console.log('选择分镜:', index, type);
  if(!props.isPlaying){
    emit('update:modelValue', index);
    emit('shot-select', index, type);
  }
};

// 在指定分镜之后添加新分镜
const addNewShot = (index) => {
  emit('add', index);
};

// 删除分镜
const removeShot = (index) => {
  // 至少保留一个分镜
  // if (props.shots.length <= 1) {
  //   ElMessage.warning('至少需要保留一个分镜');
  //   return;
  // }

  ElMessageBox.confirm('确定要删除该分镜吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    emit('remove', index);
  });

};

// 获取分镜宽度
const getShotWidth = () => {
  let ratio = props.aspectRatio.split(':')[0] / props.aspectRatio.split(':')[1];
  if(ratio < 0.65){
    ratio = 0.65
  }else if(ratio > 1.2){
    ratio = 1.2
  }
  return 146 * ratio;
};


// 获取分镜的最小时长（不考虑 playDuration）
const getMinShotDuration = (shot) => {
  // 获取当前分镜所有语音的总时长
  let countVoiceDuration = 0;
  if (shot.audios && shot.audios.length > 0) {
    countVoiceDuration = shot.audios.reduce((acc, voice) => acc + (voice.audioDuration || 0), 0);
  }

  // 如果视频时长大于0，返回视频时长
  // if (shot?.videoDuration > 0) {
  //   return shot.videoDuration;
  // }

  if (countVoiceDuration > 0) {
    // 如果有语音时长，返回语音时长
    return countVoiceDuration;
  }

  // 最后使用默认时长
  return 1000; // 默认1秒
};

// 获取分镜时长
const getShotDuration = (shot) => {
  // 如果设置了 playDuration，优先使用
  if (shot.playDuration && shot.playDuration > 0) {
    return shot.playDuration;
  }

  // 否则使用最小时长
  return getMinShotDuration(shot);
};

// 获取 playDuration 的秒数值，用于界面显示
const getPlayDurationSeconds = (shot) => {
  if (shot.playDuration && shot.playDuration > 0) {
    return shot.playDuration / 1000;
  }
  return getMinShotDuration(shot) / 1000;
};

// 开始拖拽调整 playDuration
const startPlayDurationResize = (shot, index, event) => {
  const minDuration = getMinShotDuration(shot);

  playDurationResizing.value = {
    active: true,
    shotIndex: index,
    shot: shot,
    startX: event.clientX,
    originalDuration: shot.playDuration || minDuration,
    minDuration: minDuration
  };

  // 添加拖拽类以禁用动画，提高拖拽流畅性
  const shotElement = event.target.closest('.shot-item');
  const wrapperElement = event.target.closest('.shot-item-wrapper');
  if (shotElement) {
    shotElement.classList.add('dragging-duration');
  }
  if (wrapperElement) {
    wrapperElement.classList.add('dragging-duration');
  }

  document.addEventListener('mousemove', handlePlayDurationResizeMove);
  document.addEventListener('mouseup', handlePlayDurationResizeEnd);

  event.preventDefault();
  event.stopPropagation();
};

// 处理拖拽过程中的鼠标移动
const handlePlayDurationResizeMove = (event) => {
  if (!playDurationResizing.value.active) return;

  const deltaX = event.clientX - playDurationResizing.value.startX;
  const deltaTime = (deltaX / props.timelineScale) * 1000; // 转换为毫秒

  let newDuration = Math.max(
    playDurationResizing.value.minDuration,
    playDurationResizing.value.originalDuration + deltaTime
  );

  // 限制最大时长为60秒
  newDuration = Math.min(newDuration, 60000);

  // 更新本地数据（临时显示）
  const shot = playDurationResizing.value.shot;
  shot.playDuration = newDuration;
};

// 结束拖拽调整
const handlePlayDurationResizeEnd = () => {
  if (!playDurationResizing.value.active) return;

  const { shotIndex, shot } = playDurationResizing.value;

  // 移除拖拽类，恢复动画
  const shotElements = document.querySelectorAll('.shot-item.dragging-duration');
  const wrapperElements = document.querySelectorAll('.shot-item-wrapper.dragging-duration');
  shotElements.forEach(element => {
    element.classList.remove('dragging-duration');
  });
  wrapperElements.forEach(element => {
    element.classList.remove('dragging-duration');
  });

  // 发送更新事件给父组件
  emit('update-play-duration', {
    shotIndex: shotIndex,
    shotId: shot.id,
    playDuration: shot.playDuration
  });

  playDurationResizing.value.active = false;

  document.removeEventListener('mousemove', handlePlayDurationResizeMove);
  document.removeEventListener('mouseup', handlePlayDurationResizeEnd);
};

// 计算当前分镜内的播放时间
const getCurrentShotPlaybackTime = (index) => {
  // 如果不是当前播放的分镜，返回0
  if (props.currentShotIndex !== index) {
    return 0;
  }

  // 计算之前分镜的总时长
  let previousShotsDuration = 0;
  for (let i = 0; i < index; i++) {
    const prevShot = props.shots[i];
    if (prevShot) {
      previousShotsDuration += getShotDuration(prevShot) / 1000; // 转换为秒
    }
  }

  // 当前分镜内的播放时间 = 总时间 - 之前分镜的总时长
  return Math.max(0, props.currentPlaybackTime - previousShotsDuration);
};

// 计算当前分镜的播放进度百分比
const getShotProgressPercentage = (shot, index) => {
  // 如果不是当前播放的分镜，返回0
  if (props.currentShotIndex !== index) {
    return 0;
  }

  const shotDuration = getShotDuration(shot);

  // 防止除以0
  if (shotDuration <= 0) return 0;

  // 计算当前分镜内的播放时间
  const currentShotTime = getCurrentShotPlaybackTime(index);

  // 计算进度百分比，限制在0-100之间
  const percentage = (currentShotTime / (shotDuration / 1000)) * 100;
  return Math.max(0, Math.min(100, percentage));
};

// 格式化时长，将毫秒转换为 mm:ss 格式

const formatDuration = (duration) => {
  if (!duration) return '00:00.00';

  const seconds = Math.floor(duration / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  const milliseconds = Math.floor((duration % 1000) / 10);

  // return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}.${String(milliseconds).padStart(2, '0')}`;
  return `${String(remainingSeconds)}.${String(milliseconds).padStart(1, '0')}s`;
};

// 设置视频引用
const setVideoRef = (el, index) => {
  videoRefs.value[index] = el;
  // 为新添加的视频元素设置首帧
  if (el) {
    // 设置视频静音和预加载
    el.muted = true;
    el.preload = 'metadata';

    // 监听视频可以播放事件
    el.addEventListener('loadeddata', () => {
      // 设置视频时间到0.1秒处（确保获取第一帧）
      el.currentTime = 0.1;
      // 暂停视频，保持在第一帧
      el.pause();
    }, { once: true });

    // 如果视频已经有元数据，直接设置时间
    if (el.readyState >= 2) {
      el.currentTime = 0.1;
      el.pause();
    }
  }
};

// 播放音频
const playAudio = (clip) => {
  // 如果当前有音频在播放，先停止
  if (audioPlayer.value) {
    audioPlayer.value.pause();
    audioPlayer.value = null;
  }
  
  // 创建新的音频播放器
  if (clip.audioUrl) {
    const audio = new Audio(clip.audioUrl);
    audio.volume = clip.volume / 100;
    
    // 设置事件监听
    audio.addEventListener('ended', () => {
      audioPlayer.value = null;
    });
    
    // 播放
    audio.play().catch(error => {
      console.error('播放失败:', error);
      audioPlayer.value = null;
      ElMessage.error('音频播放失败，请重试');
    });
    
    audioPlayer.value = audio;
  }
};

// 停止音频
const stopAudio = () => {
  if (audioPlayer.value) {
    audioPlayer.value.pause();
    audioPlayer.value = null;
  }
};

// 播放背景音频
const playBgAudio = (clip) => {
  // 如果当前有背景音频在播放，先停止
  if (bgAudioPlayer.value) {
    bgAudioPlayer.value.pause();
    bgAudioPlayer.value = null;
  }

  // 创建新的音频播放器
  if (clip.audioUrl) {
    const audio = new Audio(clip.audioUrl);
    audio.volume = clip.volume / 100;

    // 设置事件监听
    audio.addEventListener('ended', () => {
      bgAudioPlayer.value = null;
    });

    // 播放
    audio.play().catch(error => {
      console.error('背景音频播放失败:', error);
      bgAudioPlayer.value = null;
      ElMessage.error('背景音频播放失败，请重试');
    });

    bgAudioPlayer.value = audio;
  }
};

// 停止背景音频
const stopBgAudio = () => {
  if (bgAudioPlayer.value) {
    bgAudioPlayer.value.pause();
    bgAudioPlayer.value = null;
  }
};

// 组件卸载时清理音频播放和事件监听器
onBeforeUnmount(() => {
  stopAudio();
  stopBgAudio();

  // 移除鼠标滚轮事件监听器
  if (shotListContainer.value) {
    shotListContainer.value.removeEventListener('wheel', handleWheelScroll);
  }

  // 清理 playDuration 拖拽事件监听器
  document.removeEventListener('mousemove', handlePlayDurationResizeMove);
  document.removeEventListener('mouseup', handlePlayDurationResizeEnd);
});

// 更新总时长
const updateTotalDuration = (newDuration) => {
  totalDuration.value = newDuration;
  // 如果需要，可以在这里添加额外的处理逻辑
};

// 处理音轨选择事件
const handleAudioTrackSelect = (trackIndex) => {
  // 将音轨选择事件传递给父组件
  emit('audio-track-select', trackIndex);
};

// 滚动到选中的分镜居中位置
const scrollToSelectedShot = (shotIndex) => {
  if (!shotListContainer.value || shotIndex < 0 || shotIndex >= props.shots.length) {
    return;
  }

  // 使用 nextTick 确保 DOM 已更新
  nextTick(() => {
    const container = shotListContainer.value;
    const shotElements = container.querySelectorAll('.shot-item-wrapper');

    if (shotElements[shotIndex]) {
      const shotElement = shotElements[shotIndex];
      const containerRect = container.getBoundingClientRect();
      const shotRect = shotElement.getBoundingClientRect();

      // 计算分镜元素相对于容器的位置
      const shotLeft = shotElement.offsetLeft;
      const shotWidth = shotElement.offsetWidth;
      const containerWidth = container.clientWidth;

      // 计算滚动位置，使分镜居中
      const scrollLeft = shotLeft + (shotWidth / 2) - (containerWidth / 2);

      // 平滑滚动到目标位置
      container.scrollTo({
        left: Math.max(0, scrollLeft),
        behavior: 'smooth'
      });
    }
  });
};
</script>

<style scoped>
.video-shot-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 10px 10px 0 10px;
  transition: all 0.3s ease-in-out;
}

.shot-bg-audio-list {
  width: 100%;
  min-width: 100%;
  width: max-content;
  margin-bottom: 2px;
}

.shot-audio-list {
  width: 100%;
  min-width: 100%;
  width: max-content;
}

.shot-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.shot-list-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

body.dark .shot-list-header h3 {
  color: var(--text-primary);
}


.loading-content {
  width: 100%;
  height: 100%;
  cursor: pointer;
  position: absolute;
  top: 0;
  left: 0;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  backdrop-filter: blur(2px);
  background-color: rgba(0, 0, 0, 0.494);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
}

.loading-icon {
  font-size: 24px;
  color: #409eff;
  margin-bottom: 8px;
  animation: rotating 2s linear infinite;
}

.loading-text {
  color: white;
  font-size: 12px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}

body.dark .loading-overlay {
  /* background-color: rgba(0, 0, 0, 0.689); */
}

body.dark .loading-icon {
  color: var(--primary-color);
}

.circular-icon {
  height: 24px;
  width: 24px;
  animation: rotating 2s linear infinite;
}

.path {
  stroke: #409eff;
  stroke-width: 4;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-linecap: round;
  animation: dash 1.5s ease-in-out infinite;
}

body.dark .path {
  stroke: var(--primary-color);
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }

  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

.shot-list-container {
  flex: 1;
  overflow-x: auto;
  overflow-y: hidden;
  display: flex;
  flex-direction: column;
  /* gap: 2px; */
  transition: all 0.3s ease;
  padding-bottom: 6px;
}

.shot-list-container-shot {
  display: flex;
  flex-direction: row;
  min-width: 100%;
  width: max-content;
}

.shot-list {
  display: flex;
  /* gap: 2px; */
  /* padding: 10px 0; */
  /* min-height: 180px; */
  transition: all 0.3s ease;
}

/* 包装器样式，包含分镜项和插入按钮 */
.shot-item-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  transition: all 0.3s ease;
}

/* 拖拽时禁用包装器动画 */
.shot-item-wrapper.dragging-duration {
  transition: none;
}

.shot-item {
  transition: all 0.3s ease-in-out;
  /* max-width: 180px;
  min-width: 110px; */
  /* width: 146px; */
  /* height: 160px; */
  /* border: 2px solid #e4e7ed00; */
  margin: 0 6px 6px 0;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  flex-shrink: 0;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
}

body.dark .shot-item {
  /* border: 2px solid #e4e7ed00; */
  background-color: var(--bg-secondary);
}

.shot-item.active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid #706cef;
  border-radius: 8px;
  box-sizing: border-box;
  pointer-events: none;
}

body.dark .shot-item.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);
}

/* 分镜之间的插入按钮样式 */
.shot-insert-button {
  width: 24px;
  height: 24px;
  position: absolute;
  right: -12px;
  top: 70px;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.shot-item-wrapper:hover .shot-insert-button {
  opacity: 1;
}

.insert-button-inner {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

body.dark .insert-button-inner {
  background-color: var(--primary-color);
}

.insert-button-inner:hover {
  transform: scale(1.1);
}

.add-resource {
  width: 100%;
  height: 100%;
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #909399;
  text-align: center;
  padding: 4px;
  box-sizing: border-box;
  border-bottom: 1px solid #e4e7ed;
}

.shot-thumbnail-item {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.shot-thumbnail {
  /* height: 100px; */
  /* max-height: 180px; */
  aspect-ratio: 4/3;
  position: relative;
  overflow: hidden;
  background-color: #6e6e6e13;
  cursor: grab;
  /* margin: 2px; */
  border-radius: 6px 6px 0 0;
  transition: all 0.3s ease-in-out;
}


.shot-timeline {
  height: 80px;
  /* max-height: 180px; */
  position: relative;
  overflow: hidden;
  background-color: #6e6e6e13;
  cursor: grab;
  margin: 2px;
  border-radius: 6px 6px 0 0;
  transition: all 0.3s ease-in-out;
}

.shot-thumbnail:active {
  cursor: grabbing;
}

.shot-thumbnail video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;
  transition: 0.8s all;
}

.shot-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;
  transition: 0.8s all;
}
.shot-thumbnail:hover img {
  object-position: left top;
}

.timeline-thumbnail img {
  /* height: 100px; */
  /* width: 100%;
  height: 100%; */
  width: 100%;
  height: 100%;
  object-fit: cover;
  /* object-position: left top; */
  transition: 1s all;
  /* object-position: center center; */
}

/* 角标样式 */
.shot-type-badge {
  position: absolute;
  top: 0px;
  right: 0px;
  background-color: #706cefd7;
  color: #fff;
  border-radius: 0 0 0 4px;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  letter-spacing: 1px;
}

body.dark .shot-type-badge {
  background-color: #706cefd7;
  color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.shot-type-badge .el-icon {
  font-size: 14px;
}

.shot-wrapper {
  position: absolute;
  bottom: 2px;
  left: 2px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.shot-duration {
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 1px 6px;
  border-radius: 4px;
  font-size: 10px;
}

.shot-id {
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 1px 6px;
  border-radius: 4px;
  font-size: 10px;
  max-lines: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 播放进度指示器 */
.shot-progress-indicator {
  position: absolute;
  top: 0;
  height: 100%;
  width: 2px;
  background-color: #706cefd7;
  z-index: 5;
  pointer-events: none;
  transition: left 0.1s linear;
}

body.dark .shot-progress-indicator {
  background-color: #706cefd7;
}

.shot-info {
  /* margin: 0 2px 2px 2px; */
  background-color: #82828218;
  border-radius: 0 0 6px 6px;
  padding: 4px 8px;
  transition: all 0.3s ease-in-out;
}

.shot-narration {
  font-size: 12px;
  height: 36px;
  line-height: 18px;
  color: #303133d0;
  text-align: left;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.not-narration {
  text-align: center;
  font-size: 12px;
  color: #868686;
  line-height: 36px;
}

body.dark .shot-narration {
  color: var(--text-secondary);
}

.shot-type {
  font-size: 12px;
  color: #606266;
}

body.dark .shot-type {
  color: var(--text-secondary);
}

.shot-actions {
  position: absolute;
  top: 0px;
  left: 0px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.shot-item:hover .shot-actions {
  opacity: 1;
}

/* 添加分镜按钮样式 */
.add-shot {
  display: flex;
  align-items: center;
  justify-content: center;
  border-style: dashed;
  min-height: 80px;
  /* margin-bottom: 10px; */
  transition: all 0.3s ease-in-out;
}

.add-shot-content {
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.add-shot-content .el-icon {
  font-size: 20px;
  margin-bottom: 5px;
}

body.dark .add-shot-content {
  color: var(--text-secondary);
}

/* 拖拽相关样式 */
.ghost-shot {
  opacity: 0.5;
  background: #c8ebfb;
  border: 2px dashed #409eff;
  border-radius: 8px;
}

body.dark .ghost-shot {
  background: #2c3e50;
  border: 2px dashed var(--primary-color);
}

.dragging-shot {
  cursor: grabbing;
}

.drag-handle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.shot-thumbnail:hover .drag-handle {
  opacity: 1;
}

/* 自定义滚动条样式 */
.shot-list-container::-webkit-scrollbar {
  height: 6px;
  /* 横向滚动条的高度 */
  width: 6px;
  /* 纵向滚动条的宽度 */
}

.shot-list-container::-webkit-scrollbar-track {
  background: #706cef16;
  /* 滚动条轨道的背景色 */
  border-radius: 3px;
}

.shot-list-container::-webkit-scrollbar-thumb {
  background: #706cef3d;
  /* 滚动条滑块的颜色 */
  border-radius: 3px;
}

.shot-list-container::-webkit-scrollbar-thumb:hover {
  background: #706cefac;
  /* 鼠标悬停时滑块的颜色 */
}

.shot-list-container::-webkit-scrollbar-corner {
  background: #f1f1f1;
  /* 滚动条边角的颜色 */
}

/* 视图切换动画 */
.shot-list-container>* {
  transition: all 0.3s ease-in-out;
}

/* 添加特定的时间线视图过渡样式 */
.shot-item[style*="width"] {
  transition: width 0.3s ease-in-out, height 0.3s ease-in-out, margin 0.3s ease-in-out, transform 0.3s ease-in-out;
}

/* 拖拽时禁用宽度动画，提高拖拽流畅性 */
.shot-item[style*="width"].dragging-duration {
  transition: height 0.3s ease-in-out, margin 0.3s ease-in-out, transform 0.3s ease-in-out;
}

/* 添加时间线缩略图的特定样式 */
.timeline-thumbnail {
  margin: 0px;
  transition: margin 0.3s ease-in-out;
}

.timeline-thumbnail.playing {
  margin: 0;
}

/* 音频时间线样式 */
.shot-text-audio-list {
  width: 100%;
  min-height: 34px;
  /* margin-top: 4px; */
  border-radius: 4px;
  overflow: hidden;
}

.audio-timeline {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  min-height: 30px;
  overflow-x: hidden;
  /* padding: 0 8px 4px 0; */
  border-radius: 4px;
}

.audio-item {
  /* min-width: 60px; */
  padding: 8px 6px;
  box-sizing: border-box;
  border-radius: 4px;
  margin: 0 6px 6px 0;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.2s ease;
  background-color: #706cef27;
}

body.dark .audio-item {
  background-color: rgba(64, 158, 255, 0.2);
}

.audio-item:hover {
  filter: brightness(1.1);
}

.audio-text {
  font-size: 10px;
  line-height: 12px;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  /* padding-right: 30px; */
  /* 为时长预留空间 */
  max-width: 100%;
  text-align: left;
}

body.dark .audio-text {
  color: var(--text-primary);
}

.audio-duration {
  font-size: 8px;
  color: #606266;
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 1px 3px;
  border-radius: 2px;
}

body.dark .audio-duration {
  color: var(--text-secondary);
  background-color: rgba(0, 0, 0, 0.5);
}

.no-audio-placeholder {
  padding: 6px;
  background-color: #f0f0f0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #909399;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 8px 8px 0;
  box-sizing: border-box;
}

.no-audio-placeholder:hover {
  background-color: #e0e0e0;
}

body.dark .no-audio-placeholder {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
}

body.dark .no-audio-placeholder:hover {
  background-color: var(--bg-tertiary);
}

/* playDuration 拖拽手柄样式 */
.play-duration-handle {
  position: absolute;
  right: 0;
  top: 0;
  width: 6px;
  height: 100%;
  cursor: ew-resize;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.shot-item:hover .play-duration-handle {
  opacity: 1;
}

.handle-line {
  position: absolute;
  right: 0;
  top: 0;
  width: 2px;
  height: 100%;
  background-color: #706cef;
  border-radius: 1px;
}

.handle-grip {
  position: absolute;
  right: -2px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 20px;
  background-color: #706cef;
  border-radius: 3px;
  border: 1px solid #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.play-duration-handle:hover .handle-line,
.play-duration-handle:hover .handle-grip {
  background-color: #8a87f0;
}

.play-duration-handle:active .handle-line,
.play-duration-handle:active .handle-grip {
  background-color: #5a56e8;
}

body.dark .handle-line,
body.dark .handle-grip {
  background-color: #8a87f0;
  border-color: var(--bg-primary);
}

body.dark .play-duration-handle:hover .handle-line,
body.dark .play-duration-handle:hover .handle-grip {
  background-color: #a5a2f2;
}

body.dark .play-duration-handle:active .handle-line,
body.dark .play-duration-handle:active .handle-grip {
  background-color: #706cef;
}
</style>