<template>
  <div class="video-generation-panel">
    <div class="panel-section">
      <div class="form-item">
        <el-input v-model="shotData.videoPrompt" type="textarea" placeholder="我是视频生成的文字描述" @change="updateShot"
        :autosize="{ minRows: 3, maxRows: 6 }" class="image-generation-textarea" />
        <div class="character-count">{{ shotData.videoPrompt?.length || 0 }}/800</div>
      </div>
    </div>

    <!-- 视频模型选择 -->
    <div class="panel-section">
      <div class="form-label">视频模型</div>
      <DropdownPanel v-model="showVideoModelSelector" position="bottom" align="center" closeOnClickOutside
        :width="290" :zIndex="1200" :maxHeight="300" dropdown-id="video-model-selector">
        <!-- 触发器插槽 -->
        <template #trigger>
          <div class="current-model">
            <div class="model-card">
              <div class="model-icon"></div>
              <div class="model-info">
                <div class="model-name">{{ getVideoModelName(selectedVideoModel) }}</div>
                <!-- <div class="model-desc">{{ getVideoModelDescription(selectedVideoModel) }}</div> -->
              </div>
            </div>
            <el-icon class="expand-icon" :class="{ 'rotate': showVideoModelSelector }">
              <ArrowDown />
            </el-icon>
          </div>
        </template>

        <!-- 下拉内容插槽 -->
        <div class="model-selector-container">
          <div class="model-list">
            <div 
              v-for="model in videoModels" 
              :key="model.id" 
              class="model-option" 
              :class="{ 'active': selectedVideoModel === model.id }"
              @click="selectVideoModel(model.id)"
            >
              <div class="model-card">
                <div class="model-icon"></div>
                <div class="model-info">
                  <div class="model-name">{{ model.name }}</div>
                  <div class="model-desc">{{ model.description }}</div>
                </div>
              </div>
              <el-icon v-if="selectedVideoModel === model.id" class="model-selected-icon">
                <Check />
              </el-icon>
            </div>
          </div>
        </div>
      </DropdownPanel>
    </div>

    <div class="video-image-content">
      <div class="panel-section flex-1">
        <div class="form-label">起始帧</div>
        <!-- 显示已选择的参考图 -->
        <div v-if="selectedStartFrameUrl || shotData.startFrameImage" class="selected-reference-image">
          <div class="reference-image-preview" @click="handleImageClick(selectedStartFrameUrl || shotData.startFrameImage)">
            <img :src="selectedStartFrameUrl || shotData.startFrameImage" alt="起始帧"/>
            <div class="reference-image-actions">
              <div class="custom-delete-button" @click.stop="clearStartFrameImage">
                <el-icon>
                  <Close />
                </el-icon>
              </div>
            </div>
          </div>
        </div>

        <!-- 使用按钮打开资产选择器弹框 -->
        <div v-else class="reference-image-options" @click="openStartFrameSelector">
          <div class="duration-option">
            <el-icon>
              <Plus />
            </el-icon>
            <span></span>
          </div>
        </div>
      </div>

      <div class="panel-section flex-1" v-if="selectedVideoModel == 'doubao-seedance-1-0-lite-i2v-250428'">
        <div class="form-label">末尾帧</div>

        <!-- 显示已选择的参考图 -->
        <div v-if="selectedEndFrameUrl || shotData.endFrameImage" class="selected-reference-image">
          <div class="reference-image-preview" @click="handleImageClick(selectedEndFrameUrl || shotData.endFrameImage)">
            <img :src="selectedEndFrameUrl || shotData.endFrameImage" alt="末尾帧"/>
            <div class="reference-image-actions">
              <div class="custom-delete-button" @click.stop="clearEndFrameImage">
                <el-icon>
                  <Close />
                </el-icon>
              </div>
            </div>
          </div>
        </div>

        <!-- 使用按钮打开资产选择器弹框 -->
        <div v-else class="reference-image-options" @click="openEndFrameSelector">
          <div class="duration-option">
            <el-icon>
              <Plus />
            </el-icon>
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <div class="panel-section">
      <div class="form-label">生成比例</div>
      <DropdownPanel v-if="!isRatioDisabled" v-model="showVideoRatioSelector" position="bottom" align="center" closeOnClickOutside
        :width="290" :zIndex="1200" :maxHeight="400">
        <!-- 触发器插槽 -->
        <template #trigger>
          <div class="current-ratio">
            <span class="current-ratio-text">{{ videoAspectRatio }}</span>
            <span class="current-ratio-desc">{{ getRatioDescription(videoAspectRatio) }}</span>
            <el-icon class="expand-icon" :class="{ 'rotate': showVideoRatioSelector }">
              <ArrowDown />
            </el-icon>
          </div>
        </template>

        <!-- 下拉内容插槽 -->
        <RatioSelector v-model="videoAspectRatio" @update:modelValue="handleVideoRatioChange" />
      </DropdownPanel>
      
      <!-- 当有起始帧图片时显示自动比例提示 -->
      <div v-else class="current-ratio disabled">
        <span class="current-ratio-text">自动</span>
        <span class="current-ratio-desc">根据起始帧图片自动确定比例</span>
      </div>
    </div>

    <!-- 视频分辨率选择 -->
    <!-- <div class="panel-section">
      <div class="form-label">视频分辨率</div>
      <div class="resolution-options">
        <div class="resolution-option" :class="{ 'active': videoResolution == '480p' }" @click="setResolution('480p')">480p</div>
        <div class="resolution-option" :class="{ 'active': videoResolution == '720p' }" @click="setResolution('720p')">720p</div>
        <div class="resolution-option" :class="{ 'active': videoResolution == '1080p' }" @click="setResolution('1080p')">1080p</div>
      </div>
    </div> -->

    <div class="panel-section">
      <div class="form-label">生成时长</div>
      <div class="duration-options">
        <div class="duration-option" :class="{ 'active': videoDuration === 5000 }" @click="setDuration(5000)">5s</div>
        <div class="duration-option" :class="{ 'active': videoDuration === 10000 }" @click="setDuration(10000)">10s</div>
      </div>
    </div>

    <div class="panel-actions">
      <GenerateButton
        :text="shotData.shotStatus == '1' ? '生成中...' : isGeneratingVideo ? '处理中...' : '生成视频'"
        :loading="isGeneratingVideo || shotData.shotStatus == '1'"
        :icon="VideoCamera"
        :credits="getCreditsText()"
        @click="generateVideo"
      />
    </div>

    <!-- 资产选择器弹框 -->
    <AssetSelectorDialog 
      v-model:visible="showStartFrameSelectorDialog"
      :canvasId="canvasId"
      title="选择起始帧"
      :type="1"
      @select="handleStartFrameAssetSelected"
      @upload="handleStartFrameFileUploaded"
    />

    <!-- 资产选择器弹框 -->
    <AssetSelectorDialog 
      v-model:visible="showEndFrameSelectorDialog"
      :canvasId="canvasId"
      title="选择末尾帧"
      :type="1"
      @select="handleEndFrameAssetSelected"
      @upload="handleEndFrameFileUploaded"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { Picture, VideoCamera, ArrowDown, Plus, Close, Check } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

import DropdownPanel from '../parent/DropdownPanel.vue';
import RatioSelector from '../selector/RatioSelector.vue';
import AssetSelectorDialog from './AssetSelectorDialog.vue';
import GenerateButton from './GenerateButton.vue';
import { uploadToOSS } from '@/api/oss.js';
import { generateCanvasVideo, addCanvasMaterial } from '@/api/auth.js';

// 组件属性
const props = defineProps({
  shot: {
    type: Object,
    required: true
  },
  canvasId: {
    type: [Number, String],
    default: ''
  }
});

// 事件
const emit = defineEmits(['update:shot', 'generate-video', 'refresh-canvas']);

// 本地数据副本
const shotData = ref({ ...props.shot });

// 视频模型相关状态
const showVideoModelSelector = ref(false);
const selectedVideoModel = ref('doubao-seedance-1-0-pro-250528');
const videoModels = ref([
  {
    id: 'doubao-seedance-1-0-pro-250528',
    name: 'SeedancePro',
    description: '支持文生视频或文+首帧生视频',
    // 是否启用
    enabled: true
  },
  {
    id: 'doubao-seedance-1-0-lite-i2v-250428',
    name: 'SeedanceLite',
    description: '必传文+首帧或尾帧生视频',
    // 是否启用
    enabled: true
  },
  {
    id: 'MiniMax-Hailuo-02',
    name: 'Hailuo',
    description: '支持文生视频或文+首帧生视频',
    // 是否启用
    enabled: true
  },
]);

// 视频分辨率相关状态
const videoResolution = ref("720p");

// 文件上传相关状态
const fileInputRef = ref(null);
const selectedFile = ref(null);
const isUploading = ref(false);
const uploadProgress = ref(0);

// 视频帧参考图相关状态
const showStartFrameSelector = ref(false);
const showEndFrameSelector = ref(false);
const selectedStartFrameUrl = ref('');
const selectedEndFrameUrl = ref('');
const videoDuration = ref(5000);

// 视频比例相关状态
const videoAspectRatio = ref('16:9');
const showVideoRatioSelector = ref(false);

// 视频生成状态
const isGeneratingVideo = ref(false);

// 资产选择器弹框状态
const showStartFrameSelectorDialog = ref(false);
const showEndFrameSelectorDialog = ref(false);

// 当前上传类型
const currentUploadType = ref('');

// 计算属性：是否禁用比例选择
const isRatioDisabled = computed(() => {
  return !!selectedStartFrameUrl.value || !!shotData.value.startFrameImage;
});

// 获取积分文本
const getCreditsText = () => {
  if (isGeneratingVideo.value || shotData.value.shotStatus == '1') {
    return '';
  }

  if (selectedVideoModel.value === 'doubao-seedance-1-0-lite-i2v-250428') {
    return `⚡ ${videoDuration.value == 5000 ? '125' : '250'}`;
  } else {
    return `⚡ ${videoDuration.value == 5000 ? '400' : '800'}`;
  }
};

// 处理图片点击事件
const handleImageClick = (image) => {
  window.openImagePreview(image, "")
}

// 监听外部shot变化
watch(() => props.shot, (newShot) => {
  shotData.value = { ...newShot };
  // 同步更新比例值
  if (newShot.videoAspectRatio) {
    videoAspectRatio.value = newShot.videoAspectRatio;
  }
}, { deep: true });

// 不再需要监听选择器状态

// 更新分镜数据
const updateShot = () => {
  // emit('update:shot', { ...shotData.value });
};

// 设置生成时长
const setDuration = (duration) => {
  videoDuration.value = duration;
  updateShot();
};

// 生成视频
const generateVideo = () => {
  // 如果已经在生成中，则不重复操作
  if (isGeneratingVideo.value || shotData.value.shotStatus == '1') {
    return;
  }

  if (!shotData.value.videoPrompt) {
    ElMessage.warning('请先填写视频描述');
    return;
  }

  if (!shotData.value.id) {
    ElMessage.warning('请先创建分镜');
    return;
  }

  // 检查模型特定要求
  if (selectedVideoModel.value === 'doubao-seedance-1-0-lite-i2v-250428') {
    if (!shotData.value.startFrameImage && !shotData.value.endFrameImage) {
      ElMessage.warning('当前选择的模型需要至少提供起始帧或末尾帧');
      return;
    }
  }

  // 设置生成状态为true
  isGeneratingVideo.value = true;

  // 更新shotData中的模型和分辨率信息
  shotData.value.videoModel = selectedVideoModel.value;
  shotData.value.videoResolution = videoResolution.value;

  let lastFrameImage = shotData.value.endFrameImage || '';
  let firstFrameImage = shotData.value.startFrameImage || '';
  // 如果模型选择 pro 尾帧传空
  if (selectedVideoModel.value === 'doubao-seedance-1-0-pro-250528' && !lastFrameImage) {
    lastFrameImage = null;
  }
  // 检查是否以 http:// 或 https:// 开头
  if (/^https?:\/\//.test(lastFrameImage)) {
    // 去除协议和域名部分
    lastFrameImage = lastFrameImage.replace(/^https?:\/\/[^\/]+/, '');
  }
  if (/^https?:\/\//.test(firstFrameImage)) {
    // 去除协议和域名部分
    firstFrameImage = firstFrameImage.replace(/^https?:\/\/[^\/]+/, '');
  }

  // 准备API参数
  const params = {
    shotId: shotData.value.id,
    model: shotData.value.videoModel || 'doubao-seedance-1-0-pro-250528',
    prompt: shotData.value.videoPrompt,
    firstFrameImage: firstFrameImage,
    lastFrameImage: lastFrameImage,
    resolution: shotData.value.videoResolution || '512x512',
    // 如果有起始帧且比例为auto，则传递'auto'值
    ratio: (shotData.value.startFrameImage && shotData.value.videoAspectRatio === 'auto') 
           ? 'auto' 
           : (shotData.value.videoAspectRatio || '1:1'),
    duration: videoDuration.value || 5000,
    // fps: shotData.value.videoFps || 24
  };

  // 调用API
  // ElMessage.info('正在生成视频，请稍候...');
  generateCanvasVideo(params)
    .then(response => {
      if (response.success) {
        // ElMessage.success('视频生成任务已提交');
        // 通知父组件刷新
        emit('refresh-canvas');
      } else {
        ElMessage.error(response.errMessage || '视频生成失败');
      }
    })
    .catch(error => {
      console.error('生成视频出错:', error);
      ElMessage.error('生成视频时发生错误，请重试');
    })
    .finally(() => {
      // 无论成功还是失败，最终都将生成状态设置为false
      isGeneratingVideo.value = false;
    });
};

// 打开起始帧选择器
const openStartFrameSelector = () => {
  showStartFrameSelectorDialog.value = true;
};

// 打开末尾帧选择器
const openEndFrameSelector = () => {
  showEndFrameSelectorDialog.value = true;
};

// 处理选择的起始帧资产
const handleStartFrameAssetSelected = (imageUrl) => {

  // 更新shotData中的起始帧参考图URL
  shotData.value.startFrameImage = imageUrl;
  
  // 当选择了起始帧图片后，设置比例为自动
  shotData.value.videoAspectRatio = 'auto';
  
  updateShot();

  ElMessage.success('已选择起始帧参考图');
};

// 处理选择的末尾帧资产
const handleEndFrameAssetSelected = (imageUrl) => {

  // 更新shotData中的末尾帧参考图URL
  shotData.value.endFrameImage = imageUrl;
  updateShot();

  ElMessage.success('已选择末尾帧参考图');
};

// 处理上传的起始帧文件
const handleStartFrameFileUploaded = async (file) => {
  if (!file) return;

  isUploading.value = true;
  uploadProgress.value = 0;
  selectedStartFrameUrl.value = URL.createObjectURL(file);

  try {
    await uploadFileAndProcessResult(file, 'startFrame');
  } finally {
    isUploading.value = false;
  }
};

// 处理上传的末尾帧文件
const handleEndFrameFileUploaded = async (file) => {
  if (!file) return;

  isUploading.value = true;
  uploadProgress.value = 0;
  selectedEndFrameUrl.value = URL.createObjectURL(file);

  try {
    await uploadFileAndProcessResult(file, 'endFrame');
  } finally {
    isUploading.value = false;
  }
};

// 上传文件并处理结果
const uploadFileAndProcessResult = async (file, frameType) => {
  if (!file) return;

  uploadProgress.value = 0;

  try {
    // 假设shotData.id可以作为conversationId使用
    const conversationId = shotData.value.id || 'default';
    const result = await uploadToOSS(file, conversationId);

    if (result && result.url) {
      // 根据上传类型更新不同的参考图URL
      if (frameType === 'startFrame') {
        shotData.value.startFrameImage = result.url;
        // 当选择了起始帧图片后，设置比例为自动
        shotData.value.videoAspectRatio = 'auto';
        ElMessage.success('起始帧参考图上传成功');
      } else if (frameType === 'endFrame') {
        shotData.value.endFrameImage = result.url;
        ElMessage.success('末尾帧参考图上传成功');
      }
      
      // 如果有canvasId，则添加素材到画布
      if (props.canvasId) {
        // 确定文件类型
        const materialType = file.type.startsWith('image/') ? 1 : 2; // 1-图片,2-视频
        
        // 构造addCanvasMaterial所需的参数
        const materialParams = {
          canvasId: Number(props.canvasId),
          materialType: materialType,
          materialUrl: result.objectName,
          materialName: file.name || '未命名素材',
          materialDesc: file.name || '未命名素材'
        };
        
        // 调用API添加素材到画布
        const addResult = await addCanvasMaterial(materialParams);
        
        if (!addResult.success) {
          ElMessage.warning(`参考图上传成功，但添加到画布失败: ${addResult.errMessage || '未知错误'}`);
        }
      }

      updateShot();
      uploadProgress.value = 100;
    } else {
      throw new Error('上传失败，未获取到URL');
    }
  } catch (error) {
    console.error('上传参考图失败:', error);
    ElMessage.error('上传参考图失败，请重试');
    uploadProgress.value = 0;
  }
};

// 清除起始帧参考图
const clearStartFrameImage = () => {
  shotData.value.startFrameImage = null;
  selectedStartFrameUrl.value = '';
  
  // 恢复默认比例
  if (shotData.value.videoAspectRatio === 'auto') {
    shotData.value.videoAspectRatio = '16:9';
    videoAspectRatio.value = '16:9';
  }
  
  updateShot();
};

// 清除末尾帧参考图
const clearEndFrameImage = () => {
  shotData.value.endFrameImage = null;
  selectedEndFrameUrl.value = '';
  updateShot();
};



// 获取视频模型名称
const getVideoModelName = (modelId) => {
  const model = videoModels.value.find(m => m.id === modelId);
  return model ? model.name : '未知模型';
};

// 获取视频模型描述
const getVideoModelDescription = (modelId) => {
  const model = videoModels.value.find(m => m.id === modelId);
  return model ? model.description : '';
};

// 选择视频模型
const selectVideoModel = (modelId) => {
  const model = videoModels.value.find(m => m.id === modelId);
  if (!model.enabled) {
    ElMessage.warning('该模型暂未开放，请选择其他模型');
    return;
  }

  selectedVideoModel.value = modelId;
  shotData.value.videoModel = modelId;
  showVideoModelSelector.value = false;
  updateShot();
};

// 设置视频分辨率
const setResolution = (resolution) => {
  videoResolution.value = resolution;
  shotData.value.videoResolution = resolution;
  updateShot();
};

// 处理视频比例变化
const handleVideoRatioChange = (newRatio) => {
  videoAspectRatio.value = newRatio;
  shotData.value.videoAspectRatio = newRatio;
  showVideoRatioSelector.value = false;
  updateShot();
};

// 获取比例描述
const getRatioDescription = (ratio) => {
  switch (ratio) {
    case 'auto': return '根据起始帧图片自动确定'
    case '16:9': return '横版 (适合电脑/电视)'
    case '9:16': return '竖版 (适合手机)'
    case '3:4': return '竖版 (适合平板/印刷)'
    case '2:3': return '竖版 (适合书籍/杂志)'
    case '1:1': return '方形 (适合社交媒体)'
    case '3:2': return '横版 (适合传统相机)'
    case '4:3': return '横版 (适合传统显示器)'
    case '21:9': return '超宽 (适合电影/游戏)'
    default: return ''
  }
};

// 组件挂载时初始化
onMounted(() => {
  // 监听起始帧变化，设置自动比例
  watch(() => shotData.value.startFrameImage, (newVal) => {
    if (newVal) {
      shotData.value.videoAspectRatio = 'auto';
    } else if (shotData.value.videoAspectRatio === 'auto') {
      shotData.value.videoAspectRatio = '16:9';
      videoAspectRatio.value = '16:9';
    }
  });

  // 初始化比例值
  if (shotData.value.videoAspectRatio) {
    videoAspectRatio.value = shotData.value.videoAspectRatio;
  } else {
    shotData.value.videoAspectRatio = videoAspectRatio.value;
  }

  // 初始化视频模型
  if (shotData.value.videoModel) {
    selectedVideoModel.value = shotData.value.videoModel;
  } else {
    shotData.value.videoModel = selectedVideoModel.value;
  }
  
  // 初始化视频分辨率
  if (shotData.value.videoResolution) {
    videoResolution.value = shotData.value.videoResolution;
  } else {
    shotData.value.videoResolution = videoResolution.value;
  }

  updateShot();
});
</script>

<style scoped>
/* 样式与ImageGenerationPanel保持一致，这里省略具体样式代码以避免超长 */
/* 完整样式请参考原VideoGenerationPanel.vue中的视频生成相关样式 */
.video-generation-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 12px;
  padding: 0 16px 16px 16px;
}

.video-image-content {
  display: flex;
  flex-direction: row;
  gap: 10px;
}

.video-image-content .flex-1 {
  flex: 1;
}

.panel-section {
  /* margin-bottom: 15px; */
}

.image-generation-textarea {
  background-color: #f5f7fa;
  border-radius: 8px;
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.1) !important;
  padding-bottom: 28px;
}

body.dark .image-generation-textarea {
  background-color: rgba(204, 221, 255, .06);
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.1) !important;
}

.form-item {
  margin-bottom: 0px;
  position: relative;
}

.form-label {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 5px;
  text-align: left;
}

body.dark .form-label {
  color: var(--text-primary);
}

.panel-actions {
  margin-top: auto;
  padding: 0;
  display: flex;
  justify-content: center;
}

/* 面板操作区域 */

.loading-icon {
  animation: rotating 2s linear infinite;
}

.circular-icon {
  height: 16px;
  width: 16px;
  animation: rotating 2s linear infinite;
}

.path {
  stroke: white;
  stroke-width: 3;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-linecap: round;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }

  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

/* Character count */
.character-count {
  position: absolute;
  right: 4px;
  bottom: 4px;
  text-align: right;
  font-size: 12px;
  color: #909399;
}

/* 当前尺寸显示样式 */
.current-ratio,
.current-model {
  width: 290px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 14px;
  border-radius: 8px;
  background-color: #f5f7fa;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 10px;
  box-sizing: border-box;
}

/* 禁用状态的比例选择器样式 */
.current-ratio.disabled {
  cursor: not-allowed;
  opacity: 0.8;
  background-color: #f0f0f0;
}

body.dark .current-ratio.disabled {
  background-color: rgba(204, 221, 255, .04);
  opacity: 0.7;
}

body.dark .current-ratio,
body.dark .current-model {
  background-color: rgba(204, 221, 255, .06);
}

.current-ratio:hover,
.current-model:hover {
  border-color: #a5b4fc;
  background-color: #f9faff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

body.dark .current-ratio:hover,
body.dark .current-model:hover {
  border-color: #818cf8;
  background-color: var(--bg-hover, #2a2a3c);
}

.current-ratio-text {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  margin-right: 10px;
}

body.dark .current-ratio-text {
  color: var(--text-primary, #e2e8f0);
}

.current-ratio-desc {
  flex: 1;
  font-size: 14px;
  color: #64748b;
  flex-grow: 1;
  text-align: left;
}

body.dark .current-ratio-desc {
  color: var(--text-secondary, #94a3b8);
}

.expand-icon {
  font-size: 16px;
  color: #64748b;
  transition: transform 0.3s ease;
}

.expand-icon.rotate {
  transform: rotate(180deg);
}

body.dark .expand-icon {
  color: var(--text-secondary, #94a3b8);
}

.model-card {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.model-icon {
  width: 18px;
  height: 18px;
  background: linear-gradient(135deg, #a78bfa, #818cf8);
  border-radius: 6px;
  flex-shrink: 0;
}

.model-info {
  flex: 1;
  min-width: 0;
}

.model-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
  text-align: left;
}

.model-desc {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
}

body.dark .model-name {
  color: var(--text-primary);
}

body.dark .model-desc {
  color: var(--text-secondary);
}

/* Duration options */
.duration-options,
.resolution-options {
  display: flex;
  gap: 10px;
  margin-top: 5px;
}

.duration-option,
.resolution-option {
  padding: 8px 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  text-align: center;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

.duration-option.active,
.resolution-option.active {
  background-color: #ecf5ff;
  color: #409eff;
  border: 1px solid #d9ecff;
}

body.dark .duration-option,
body.dark .resolution-option {
  background-color: rgba(204, 221, 255, .06);
}

body.dark .duration-option.active,
body.dark .resolution-option.active {
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
}

/* 模型选择器样式 */
.model-selector-container {
  padding: 8px;
}

.model-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.model-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.model-option:hover {
  background-color: #f5f7fa;
}

.model-option.active {
  background-color: #ecf5ff;
}

body.dark .model-option:hover {
  background-color: rgba(204, 221, 255, .06);
}

body.dark .model-option.active {
  background-color: rgba(64, 158, 255, 0.1);
}

.model-selected-icon {
  color: #409eff;
  font-size: 16px;
}

body.dark .model-selected-icon {
  color: var(--primary-color);
}

/* 参考图相关样式 */
.selected-reference-image {
  max-width: 140px;
  cursor: pointer;
}

.selected-reference-image:hover .reference-image-actions{
  display: flex;
}

.reference-image-preview {
  position: relative;
}

.reference-image-actions {
  position: absolute;
  top: 0;
  right: 0;
  padding: 4px;
  display: none;
}

.reference-selector-container {
  display: flex;
  flex-direction: column;
}

.reference-selector-tabs {
  display: flex;
  gap: 4px;
  padding: 4px;
  border-bottom: 1px solid #ebeef5;
}

body.dark .reference-selector-tabs {
  border-bottom-color: var(--border-color);
}

.reference-tab {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
}

.reference-tab:hover {
  background-color: #ecf5ff;
}

.reference-tab.active {
  background-color: #ecf5ff;
  color: #409eff;
  border: 1px solid #d9ecff;
}

body.dark .reference-tab {
  background-color: rgba(204, 221, 255, .06);
}

body.dark .reference-tab:hover {
  background-color: rgba(204, 221, 255, .1);
}

body.dark .reference-tab.active {
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
}

.asset-selector-panel {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

body.dark .asset-selector-panel {
  background-color: rgba(204, 221, 255, .06);
}

.asset-selector-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.asset-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2px;
}

.asset-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

.asset-card {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
}

.asset-card.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

body.dark .asset-card {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

body.dark .asset-card.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.3);
}

.asset-preview {
  aspect-ratio: 1 / 1;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

body.dark .asset-preview {
  background-color: var(--bg-tertiary);
}

.asset-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.3s ease-in;
}

.asset-image.loaded {
  opacity: 1;
}

.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #909399;
  font-size: 24px;
}

body.dark .image-placeholder {
  color: var(--text-secondary);
}

.empty-assets {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
}

.empty-icon {
  font-size: 32px;
  color: #c0c4cc;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
  color: #909399;
}

body.dark .empty-icon {
  color: #606266;
}

body.dark .empty-text {
  color: var(--text-secondary);
}

.asset-pagination {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

.reference-image-preview img {
  width: 100%;
  aspect-ratio: 16 / 9;
  object-fit: contain;
  border-radius: 8px;
  background-color: #f5f7fa;
}

body.dark .reference-image-preview img {
  background-color: rgba(204, 221, 255, .06);
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(64, 158, 255, 0.2);
  border-top-color: #409eff;
  border-radius: 50%;
  animation: spin 1s infinite linear;
  margin-bottom: 10px;
}

body.dark .loading-spinner {
  border: 3px solid rgba(64, 158, 255, 0.1);
  border-top-color: var(--primary-color);
}

.loading-text {
  color: #909399;
  font-size: 14px;
  transition: color 0.3s;
}

body.dark .loading-text {
  color: var(--text-secondary);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 修复参考图样式 */
.reference-image-options {
  width: 140px;
  aspect-ratio: 16 / 9;
  flex: 1;
  display: flex;
  gap: 10px;
  cursor: pointer;
}

/* 自定义删除按钮样式 */
.custom-delete-button {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #7a7a7a71;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  padding: 0;
}

.custom-delete-button:hover {
  background-color: #fd4a4a;
  transform: scale(1.05);
}

body.dark .custom-delete-button {
  background-color: #7a7a7a71;
}

body.dark .custom-delete-button:hover {
  background-color: #fd4a4a;
}

/* 辅助类 */
.ml-5 {
  margin-left: 5px;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-textarea__inner) {
  font-size: 13px;
}

:deep(.el-input__inner) {
  font-size: 13px;
}

:deep(.el-slider__runway) {
  margin: 8px 0;
}

:deep(.el-slider__input) {
  width: 60px;
  margin-left: 10px;
}
</style> 