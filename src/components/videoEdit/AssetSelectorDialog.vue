<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="800px"
    :destroy-on-close="true"
    @closed="handleDialogClosed"
  >
    <!-- 视频帧选择器对话框 -->
    <el-dialog
      v-model="isFrameSelectorVisible"
      title="选择视频帧"
      width="900px"
      append-to-body
      :destroy-on-close="true"
    >
      <div v-if="selectedVideoAsset" class="video-frame-selector">
        <div class="video-container">
          <video 
            ref="videoRef"
            :src="selectedVideoAsset.materialUrl"
            class="video-player"
            controls
            @timeupdate="handleTimeUpdate"
          ></video>
        </div>
        <div class="frame-info">
          <span>当前毫秒: {{ currentFrameMs }}</span>
          <span class="time-info">时间: {{ Math.floor(currentFrameTime) }}秒</span>
        </div>
        <div class="frame-actions">
          <el-button @click="isFrameSelectorVisible = false">取消</el-button>
          <el-button type="primary" @click="selectCurrentFrame">选择当前帧</el-button>
        </div>
      </div>
    </el-dialog>
    <div class="asset-selector-container">
      <!-- 选择器标签页 -->
      <div class="reference-selector-tabs">
        <div
          class="reference-tab"
          :class="{ 'active': activeTab === 'project' }"
          @click="activeTab = 'project'"
        >
          <el-icon><FolderOpened /></el-icon>
          <span>项目素材</span>
        </div>
        <div
          class="reference-tab"
          :class="{ 'active': activeTab === 'upload' }"
          @click="activeTab = 'upload'"
        >
          <el-icon><Upload /></el-icon>
          <span>本地上传</span>
        </div>
      </div>

      <!-- 项目素材选择面板 -->
      <div v-if="activeTab === 'project'" class="asset-selector-panel">
        <!-- 添加素材类型选择标签页 -->
        <el-tabs v-model="activeMaterialType" class="asset-category-tabs asset-selector-tabs material-type-tabs" @tab-click="handleMaterialTypeChange">
          <el-tab-pane label="全部" name="all"></el-tab-pane>
          <el-tab-pane label="图片" name="image"></el-tab-pane>
          <el-tab-pane label="视频" name="video"></el-tab-pane>
        </el-tabs>
        
        <!-- 加载中状态 -->
        <div v-if="isProjectLoading" class="asset-selector-loading">
          <div class="loading-spinner"></div>
          <div class="loading-text">正在加载项目素材...</div>
        </div>

        <!-- 空状态 -->
        <div v-else-if="!hasProjectAssets" class="empty-assets">
          <el-icon class="empty-icon"><Picture /></el-icon>
          <div class="empty-text">暂无项目素材，请上传或从我的素材选择</div>
        </div>

        <!-- 素材网格 -->
        <div v-else class="asset-grid">
          <div
            v-for="asset in projectAssets"
            :key="asset.id"
            class="asset-item"
            @click="selectAsset(asset)"
          >
            <div class="asset-card" :class="{ 'selected': selectedAssetId === asset.id }">
              <div class="asset-preview">
                <img v-if="asset.materialType == 2" :src="`${asset.materialUrl}?x-oss-process=video/snapshot,t_0,f_jpg`" class="asset-image" :class="{ 'loaded': true }" alt="素材预览" />
                <img v-else
                  :src="asset.materialUrl || asset.imageUrl"
                  class="asset-image"
                  :class="{ 'loaded': true }"
                  @error="handleImageError(asset)"
                  alt="素材预览"
                />
                <!-- <div v-if="asset.imageError" class="image-placeholder">
                  <el-icon><Picture /></el-icon>
                </div> -->
                <!-- 视频时长显示 -->
                <div class="video-duration" v-if="asset.materialType == 2 && asset.videoMaterialParams?.duration">
                  {{ formatDuration(asset.videoMaterialParams.duration) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页器 -->
        <div v-if="hasProjectAssets && totalProjectAssets > projectPageSize" class="asset-pagination">
          <el-pagination
            v-model:current-page="projectPageNum"
            :page-size="projectPageSize"
            layout="prev, pager, next"
            :total="totalProjectAssets"
            @current-change="handleProjectPageChange"
          />
        </div>
      </div>

      <!-- 本地上传面板 -->
      <div v-if="activeTab === 'upload'" class="upload-panel">
        <el-upload
          class="asset-uploader"
          action="#"
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleFileChange"
          accept="image/*"
        >
          <div class="upload-area">
            <el-icon class="upload-icon"><Plus /></el-icon>
            <div class="upload-text">点击或拖拽文件到此处上传</div>
            <div class="upload-hint">支持 JPG、PNG 格式图片</div>
          </div>
        </el-upload>

        <!-- 预览区域 -->
        <div v-if="selectedFile" class="upload-preview">
          <div class="preview-header">
            <div class="preview-title">预览</div>
            <div class="preview-action" @click="clearSelectedFile">
              <el-icon><Delete /></el-icon>
              <span>清除</span>
            </div>
          </div>
          <div class="preview-image-container">
            <img :src="previewUrl" alt="预览图" class="preview-image" />
          </div>
          <div class="upload-actions">
            <el-button type="primary" @click="uploadSelectedFile" :loading="isUploading">
              {{ isUploading ? '上传中...' : '确认上传' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { FolderOpened, Upload, Picture, Plus, Delete } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getCanvasMaterialList } from '@/api/auth.js';

// 组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  canvasId: {
    type: [Number, String],
    default: ''
  },
  title: {
    type: String,
    default: '选择素材'
  },
  type: {
    type: Number,
    default: 1 // 1-图片, 2-视频
  },
  selectedAssetId: {
    type: [Number, String],
    default: null
  }
});

// 事件
const emit = defineEmits(['update:visible', 'select', 'upload']);

// 内部状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 标签页状态
const activeTab = ref('project');

// 添加素材类型选择状态
const activeMaterialType = ref('all');

// 项目素材相关状态
const isProjectLoading = ref(false);
const projectAssets = ref([]);
const projectPageNum = ref(1);
const projectPageSize = ref(24);
const totalProjectAssets = ref(0);
const hasProjectAssets = computed(() => projectAssets.value.length > 0);

// 本地上传相关状态
const selectedFile = ref(null);
const previewUrl = ref('');
const isUploading = ref(false);

// 监听弹框可见性变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 重置状态
    activeTab.value = 'project';
    projectPageNum.value = 1;
    
    // 加载项目素材
    if (props.canvasId) {
      fetchProjectMaterials();
    }
  } else {
    // 清理状态
    clearSelectedFile();
  }
});

// 监听画布ID变化
watch(() => props.canvasId, (newVal) => {
  if (newVal && props.visible) {
    fetchProjectMaterials();
  }
});

// 格式化时长，将毫秒转换为 mm:ss 格式
const formatDuration = (ms) => {
  if (!ms) return '00:00';

  const totalSeconds = Math.floor(ms / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;

  return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
};

// 加载项目素材
const fetchProjectMaterials = async () => {
  if (!props.canvasId) {
    projectAssets.value = [];
    return;
  }
  
  isProjectLoading.value = true;
  
  try {
    const params = {
      canvasId: Number(props.canvasId),
      pageNum: projectPageNum.value,
      pageSize: projectPageSize.value,
    };
    
    // 添加素材类型筛选条件
    if (activeMaterialType.value !== 'all') {
      params.materialType = activeMaterialType.value === 'image' ? 1 : 2; // 1-图片, 2-视频
    }

    const response = await getCanvasMaterialList(params);

    if (response.success) {
      // 去除 materialType 为 3 的素材
      projectAssets.value = response.data.filter(item => item.materialType !== 3) || [];
      totalProjectAssets.value = response.totalCount || 0;
    } else {
      ElMessage.error(response.errMessage || '获取项目素材失败');
      projectAssets.value = [];
    }
  } catch (error) {
    console.error('获取项目素材失败:', error);
    ElMessage.error('获取项目素材失败，请重试');
    projectAssets.value = [];
  } finally {
    isProjectLoading.value = false;
  }
};

// 处理素材类型切换
const handleMaterialTypeChange = (tab) => {
  console.log('Material type changed to:', tab.props.name);
  // 重新加载素材列表
  fetchProjectMaterials();
};

// 处理项目素材分页变化
const handleProjectPageChange = (page) => {
  projectPageNum.value = page;
  fetchProjectMaterials();
};

// 处理图片加载错误
const handleImageError = (asset) => {
  asset.imageError = true;
};

// 选择素材
const selectAsset = (asset) => {
  if (asset.materialType == 2) { // 视频类型
    // 显示视频帧选择器
    showVideoFrameSelector(asset);
  } else {
    // 图片类型，直接返回素材URL
    emit('select', asset.materialUrl || asset.imageUrl);
    dialogVisible.value = false;
  }
};

// 视频帧选择状态
const selectedVideoAsset = ref(null);
const isFrameSelectorVisible = ref(false);
const currentFrameTime = ref(0); // 当前帧时间，单位秒
const currentFrameMs = ref(0); // 当前播放的毫秒数
const videoRef = ref(null);

// 显示视频帧选择器
const showVideoFrameSelector = (asset) => {
  selectedVideoAsset.value = asset;
  isFrameSelectorVisible.value = true;
  currentFrameTime.value = 0;
  
  // 延迟执行以确保DOM已更新
  setTimeout(() => {
    if (videoRef.value) {
      videoRef.value.currentTime = 0;
    }
  }, 100);
};

// 选择当前帧
const selectCurrentFrame = () => {
  if (!selectedVideoAsset.value) return;
  
  // 构建带有毫秒数的URL
  const frameMs = currentFrameMs.value;
  const frameUrl = `${selectedVideoAsset.value.materialUrl}?x-oss-process=video/snapshot,t_${frameMs},f_jpg`;
  
  emit('select', frameUrl);
  isFrameSelectorVisible.value = false;
  dialogVisible.value = false;
};

// 视频时间更新处理
const handleTimeUpdate = (event) => {
  const video = event.target;
  currentFrameTime.value = video.currentTime;
  
  // 计算当前毫秒数
  currentFrameMs.value = Math.floor(video.currentTime * 1000) - 41;
};

// 处理文件选择
const handleFileChange = (file) => {
  if (!file) return;
  
  // 检查文件类型
  const isImage = file.raw.type.startsWith('image/');
  const isVideo = file.raw.type.startsWith('video/');
  
  if (props.type === 1 && !isImage) {
    ElMessage.warning('请选择图片文件');
    return;
  }
  
  if (props.type === 2 && !isVideo) {
    ElMessage.warning('请选择视频文件');
    return;
  }
  
  selectedFile.value = file.raw;
  previewUrl.value = URL.createObjectURL(file.raw);
};

// 清除选中的文件
const clearSelectedFile = () => {
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value);
  }
  selectedFile.value = null;
  previewUrl.value = '';
};

// 上传选中的文件
const uploadSelectedFile = () => {
  if (!selectedFile.value) return;
  
  emit('upload', selectedFile.value);
  dialogVisible.value = false;
};

// 处理弹框关闭
const handleDialogClosed = () => {
  clearSelectedFile();
};

// 组件挂载时初始化
onMounted(() => {
  if (props.visible && props.canvasId) {
    fetchProjectMaterials();
  }
});
</script>

<style scoped>
.asset-selector-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.reference-selector-tabs {
  display: flex;
  gap: 8px;
  padding: 4px;
  border-bottom: 1px solid #ebeef5;
}

body.dark .reference-selector-tabs {
  border-bottom-color: var(--border-color);
}

.reference-tab {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
}

.reference-tab:hover {
  background-color: #ecf5ff;
}

.reference-tab.active {
  background-color: #ecf5ff;
  color: #409eff;
  border: 1px solid #d9ecff;
}

body.dark .reference-tab {
  background-color: rgba(204, 221, 255, .06);
}

body.dark .reference-tab:hover {
  background-color: rgba(204, 221, 255, .1);
}

body.dark .reference-tab.active {
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
}

.asset-selector-panel {
  padding: 0 10px 10px 10px;
  /* background-color: #f5f7fa; */
  border-radius: 8px;
  min-height: 330px;
}

body.dark .asset-selector-panel {
  /* background-color: rgba(204, 221, 255, .06); */
}

/* 素材类型选择标签页样式 */
.material-type-tabs {
  margin-top: 0;
  padding: 0 0 10px 0;
}

.material-type-tabs :deep(.el-tabs__header) {
  margin-bottom: 10px !important;
}

.material-type-tabs :deep(.el-tabs__item) {
  padding: 0 12px !important;
  height: 32px !important;
  line-height: 32px !important;
  font-size: 13px !important;
}

/* 资产分类标签页样式 */
.asset-category-tabs {
  /* margin: 0 0 10px 0; */
}

/* 添加更具体的选择器以覆盖父组件样式 */
.asset-selector-tabs :deep(.el-tabs__header) {
  position: relative !important;
  margin: 0 !important;
}

.asset-selector-tabs :deep(.el-tabs__item) {
  color: #606266 !important;
  font-size: 14px !important;
  padding: 0 16px !important;
  height: 38px !important;
  line-height: 38px !important;
  transition: all 0.3s !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__item) {
  color: var(--text-secondary) !important;
}

.asset-selector-tabs :deep(.el-tabs__item.is-active) {
  color: #4f46e5 !important;
  font-weight: 500 !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__item.is-active) {
  color: #6366f1 !important;
}

.asset-selector-tabs :deep(.el-tabs__active-bar) {
  background-color: transparent !important;
  background-image: linear-gradient(
    90deg, transparent 0, transparent 0%,
    #4f46e5 0, #4f46e5 100%,
    transparent 0, transparent
  ) !important;
  height: 2px !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__active-bar) {
  background-image: linear-gradient(
    90deg, transparent 0, transparent 0%,
    #6366f1 0, #6366f1 100%,
    transparent 0, transparent
  ) !important;
}

.asset-selector-tabs :deep(.el-tabs__nav-wrap::after) {
  position: static !important;
  height: 1px !important;
  background-color: #e4e7ed !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__nav-wrap::after) {
  background-color: var(--border-color) !important;
}

.asset-selector-tabs :deep(.el-tabs__nav) {
  height: auto !important;
}

.asset-selector-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(64, 158, 255, 0.2);
  border-top-color: #409eff;
  border-radius: 50%;
  animation: spin 1s infinite linear;
  margin-bottom: 10px;
}

body.dark .loading-spinner {
  border: 3px solid rgba(64, 158, 255, 0.1);
  border-top-color: var(--primary-color);
}

.loading-text {
  color: #909399;
  font-size: 14px;
  transition: color 0.3s;
}

body.dark .loading-text {
  color: var(--text-secondary);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.empty-assets {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  height: 300px;
}

.empty-icon {
  font-size: 32px;
  color: #c0c4cc;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
  color: #909399;
}

body.dark .empty-icon {
  color: #606266;
}

body.dark .empty-text {
  color: var(--text-secondary);
}

.asset-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 4px;
}

.asset-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

.asset-card {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
}

.asset-card.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

body.dark .asset-card {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

body.dark .asset-card.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.3);
}

.asset-preview {
  aspect-ratio: 4/3;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

body.dark .asset-preview {
  background-color: var(--bg-tertiary);
}

.asset-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;
  opacity: 0;
  transition: 0.8s all;
}

.asset-image:hover {
  object-position: left top;
}

.asset-image.loaded {
  opacity: 1;
}

.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #909399;
  font-size: 24px;
}

body.dark .image-placeholder {
  color: var(--text-secondary);
}

.asset-pagination {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

/* 视频时长显示样式 */
.video-duration {
  position: absolute;
  bottom: 5px;
  right: 5px;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 2px 5px;
  border-radius: 4px;
  font-size: 12px;
}

.upload-panel {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 8px;
  min-height: 300px;
}

body.dark .upload-panel {
  background-color: rgba(204, 221, 255, .06);
}

.asset-uploader {
  width: 100%;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area {
  height: 100%;
  width: 100%;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  padding: 40px;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

body.dark .upload-area {
  border-color: #606266;
}

body.dark .upload-area:hover {
  border-color: var(--primary-color);
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.upload-hint {
  font-size: 12px;
  color: #909399;
}

body.dark .upload-icon {
  color: #a6a6a6;
}

body.dark .upload-text {
  color: var(--text-primary);
}

body.dark .upload-hint {
  color: var(--text-secondary);
}

.upload-preview {
  margin-top: 16px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.preview-title {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.preview-action {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #f56c6c;
  cursor: pointer;
}

.preview-action:hover {
  opacity: 0.8;
}

body.dark .preview-title {
  color: var(--text-primary);
}

body.dark .preview-action {
  color: #f56c6c;
}

.preview-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 16px;
}

body.dark .preview-image-container {
  background-color: rgba(204, 221, 255, .04);
}

.preview-image {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
}

.upload-actions {
  display: flex;
  justify-content: center;
}

/* 视频帧选择器样式 */
.video-frame-selector {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.video-container {
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
  background-color: #000;
}

.video-player {
  width: 100%;
  aspect-ratio: 16/9;
  display: block;
}

.frame-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.time-info {
  color: #909399;
  font-size: 13px;
}

body.dark .frame-info {
  color: var(--text-primary);
}

.frame-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}
</style>
