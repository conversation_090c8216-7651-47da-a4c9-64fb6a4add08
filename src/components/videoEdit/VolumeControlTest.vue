<template>
  <div class="volume-control-test">
    <h2>音量控制测试</h2>
    
    <div class="test-section">
      <h3>测试分镜数据</h3>
      <div class="shot-controls">
        <div class="shot-item" v-for="(shot, index) in testShots" :key="index">
          <h4>分镜 {{ index + 1 }} ({{ shot.type }})</h4>
          
          <!-- 视频音量控制 -->
          <div v-if="shot.type === 'video'" class="volume-control">
            <label>视频音量: {{ shot.videoVolume }}</label>
            <input 
              type="range" 
              min="0" 
              max="1" 
              step="0.1" 
              v-model.number="shot.videoVolume"
              @input="updateShotVolume(index, 'video', $event.target.value)"
            />
          </div>
          
          <!-- 音频音量控制 -->
          <div v-if="shot.audios && shot.audios.length > 0" class="audio-controls">
            <div v-for="(audio, audioIndex) in shot.audios" :key="audioIndex" class="volume-control">
              <label>音频 {{ audioIndex + 1 }} 音量: {{ audio.volume }}</label>
              <input 
                type="range" 
                min="0" 
                max="1" 
                step="0.1" 
                v-model.number="audio.volume"
                @input="updateAudioVolume(index, audioIndex, $event.target.value)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="preview-section">
      <h3>预览播放器</h3>
      <VideoEditorPreview 
        :shots="testShots"
        :autoPlay="false"
        :showSubtitles="true"
        ref="previewRef"
      />
      
      <div class="controls">
        <button @click="playPreview">播放</button>
        <button @click="pausePreview">暂停</button>
        <button @click="resetVolumes">重置音量</button>
      </div>
    </div>
    
    <div class="info-section">
      <h3>当前分镜数据</h3>
      <pre>{{ JSON.stringify(testShots, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import VideoEditorPreview from './VideoEditorPreview.vue';

const previewRef = ref(null);

// 测试分镜数据
const testShots = reactive([
  {
    type: 'video',
    videoUrl: 'https://example.com/video1.mp4',
    videoVolume: 0.8, // 视频音量 80%
    videoDuration: 5000,
    audios: [
      {
        audioUrl: 'https://example.com/audio1.mp3',
        volume: 0.6, // 音频音量 60%
        audioDuration: 3000,
        text: '这是第一段音频'
      },
      {
        audioUrl: 'https://example.com/audio2.mp3',
        volume: 0.9, // 音频音量 90%
        audioDuration: 2000,
        text: '这是第二段音频'
      }
    ]
  },
  {
    type: 'image',
    imageUrl: 'https://example.com/image1.jpg',
    audios: [
      {
        audioUrl: 'https://example.com/audio3.mp3',
        volume: 0.5, // 音频音量 50%
        audioDuration: 4000,
        text: '图片配音'
      }
    ]
  },
  {
    type: 'video',
    videoUrl: 'https://example.com/video2.mp4',
    videoVolume: 0.3, // 视频音量 30%
    videoDuration: 6000,
    audios: [
      {
        audioUrl: 'https://example.com/audio4.mp3',
        volume: 1.0, // 音频音量 100%
        audioDuration: 5000,
        text: '最后一段音频'
      }
    ]
  }
]);

// 更新分镜视频音量
const updateShotVolume = (shotIndex, type, volume) => {
  console.log(`更新分镜 ${shotIndex} ${type} 音量为: ${volume}`);
  testShots[shotIndex].videoVolume = parseFloat(volume);
};

// 更新音频音量
const updateAudioVolume = (shotIndex, audioIndex, volume) => {
  console.log(`更新分镜 ${shotIndex} 音频 ${audioIndex} 音量为: ${volume}`);
  testShots[shotIndex].audios[audioIndex].volume = parseFloat(volume);
};

// 播放预览
const playPreview = () => {
  if (previewRef.value) {
    previewRef.value.play();
  }
};

// 暂停预览
const pausePreview = () => {
  if (previewRef.value) {
    previewRef.value.pause();
  }
};

// 重置所有音量为默认值
const resetVolumes = () => {
  testShots.forEach(shot => {
    if (shot.type === 'video') {
      shot.videoVolume = 1.0;
    }
    if (shot.audios) {
      shot.audios.forEach(audio => {
        audio.volume = 1.0;
      });
    }
  });
  console.log('所有音量已重置为默认值');
};
</script>

<style scoped>
.volume-control-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
}

.shot-controls {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.shot-item {
  border: 1px solid #ddd;
  padding: 15px;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.volume-control {
  margin: 10px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.volume-control label {
  min-width: 150px;
  font-weight: bold;
}

.volume-control input[type="range"] {
  flex: 1;
  max-width: 200px;
}

.audio-controls {
  margin-left: 20px;
  border-left: 3px solid #409eff;
  padding-left: 15px;
}

.preview-section {
  margin-bottom: 30px;
}

.controls {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.controls button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #409eff;
  color: white;
  cursor: pointer;
}

.controls button:hover {
  background-color: #337ecc;
}

.info-section {
  margin-top: 30px;
}

.info-section pre {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

/* 暗色模式 */
body.dark .shot-item {
  background-color: #2a2a3a;
  border-color: #444;
  color: #fff;
}

body.dark .info-section pre {
  background-color: #1e1e2d;
  color: #fff;
}
</style>
