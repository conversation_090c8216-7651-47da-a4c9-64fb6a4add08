<template>
  <div class="export-video-dialog" v-if="modelValue" @click="handleBackdropClick">
    <!-- 遮罩层 -->
    <div class="dialog-backdrop"></div>

    <!-- 弹框内容 -->
    <div class="dialog-container" @click.stop>
      <!-- 标题栏 -->
      <!-- <div class="dialog-header">
        <h3 class="dialog-title">视频导出</h3>
        <button class="close-button" @click="handleClose">×</button>
      </div> -->

      <!-- 内容区域 -->
      <div class="dialog-content">
        <!-- 历史导出视频 -->
        <div class="export-history">
          <h3 class="export-section-title">生成视频</h3>

          <!-- 加载中状态 -->
          <div v-if="isLoadingVideos" class="export-loading">
            <div class="loading-spinner"></div>
            <!-- <div class="loading-text">加载视频中...</div> -->
          </div>

          <!-- 空数据状态 -->
          <div v-else-if="videos.length === 0" class="export-empty">
            <el-icon class="empty-icon">
              <VideoCamera />
            </el-icon>
            <div class="empty-text">暂无导出视频</div>
          </div>

          <!-- 视频列表 -->
          <div v-else class="export-videos">
            <div class="video-grid">
              <div v-for="(video, index) in videos" :key="video.id || index" class="video-item">
                <div class="video-card" @click="handleVideoClick(video)">
                  <div class="video-preview">
                    <img class="thumbnail" v-if="video.status == 2"
                      :src="`${video.videoUrl}?x-oss-process=video/snapshot,t_0,f_jpg`" alt="视频封面">
                    <div class="duration" v-if="video.status == 2">{{ formatDuration(video.videoDuration) }}</div>
                    <div class="play-icon">
                      <el-icon>
                        <VideoPlay />
                      </el-icon>
                    </div>
                    <!-- 添加下载按钮 -->
                    <div v-if="video.status == 2" class="download-icon" @click.stop="handleDownload(video)">
                      <el-icon>
                        <Download />
                      </el-icon>
                    </div>
                    <!-- 添加视频状态标签 -->
                    <div v-if="video.status !== 2" class="status-badge">
                      <el-icon>
                        <Loading class="loading-icon" />
                      </el-icon>
                      {{ video.statusDesc }}...
                    </div>
                  </div>
                  <div class="video-info">
                    <!-- <div class="video-name">{{ video.prompt || '视频 ' + (index + 1) }}</div> -->
                    <div class="video-meta">
                      <span>{{ formatDate(video.createTime) }}</span>
                      <span>{{ video.resolution }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 分页 -->
            <!-- <div class="export-pagination" v-if="totalVideos > pageSize">
              <el-pagination
                v-model:current-page="currentPage"
                :page-size="pageSize"
                layout="prev, pager, next"
                :total="totalVideos"
                @current-change="handlePageChange"
              />
            </div> -->
          </div>
        </div>

        <!-- 导出设置 -->
        <div class="export-settings" v-if="false">
          <h3 class="export-section-title">导出分辨率</h3>

          <!-- 分辨率选择 -->
          <div class="export-resolution">
            <!-- <div class="setting-label">分辨率选择</div> -->
            <div class="resolution-options">
              <div class="resolution-option" :class="{ 'active': resolution === '480p' }"
                @click="setResolution('480p')">
                480p
              </div>
              <div class="resolution-option" :class="{ 'active': resolution === '720p' }"
                @click="setResolution('720p')">
                720p
              </div>
              <div class="resolution-option" :class="{ 'active': resolution === '1080p' }"
                @click="setResolution('1080p')">
                1080p
              </div>
            </div>
          </div>

          <!-- FPS 选择 -->
          <div class="export-fps">
            <h3 class="export-section-title">帧率选择</h3>
            <div class="fps-options">
              <div class="fps-option" :class="{ 'active': fps === 24 }" @click="setFps(24)">
                24fps
              </div>
              <div class="fps-option" :class="{ 'active': fps === 30 }" @click="setFps(30)">
                30fps
              </div>
              <!-- <div 
                class="fps-option" 
                :class="{ 'active': fps === 60 }" 
                @click="setFps(60)"
              >
                60fps
              </div> -->
            </div>
          </div>


        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="dialog-footer">

        <!-- 字幕开关 -->
        <div class="export-subtitles">
          <!-- <h3 class="export-section-title">字幕设置</h3> -->
          <div class="subtitle-switch">
            <el-switch v-model="showSubtitles" inline-prompt active-text="字幕" inactive-text="字幕" size="large"
              class="subtitle-switch-component" style="--el-switch-on-color: #706cefd7" @change="setShowSubtitles" />
          </div>
        </div>

        <button class="cancel-btn" @click="handleClose" :disabled="isExporting">取消</button>
        <button class="export-btn" @click="handleExport" :disabled="isExporting || hasUnfinishedVideos">
          <el-icon v-if="!isExporting && !hasUnfinishedVideos">
            <Download />
          </el-icon>
          <span>{{ isExporting || hasUnfinishedVideos ? '导出中...' : '导出' }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount, computed } from 'vue';
import { VideoCamera, VideoPlay, Download, Loading } from '@element-plus/icons-vue';
import { ElMessage, ElPagination, ElSwitch } from 'element-plus';
import { getUserVideo, getExportVideoList, exportVideo } from '@/api/auth.js';

// 轮询相关常量
const POLLING_INTERVAL = 5000; // 轮询间隔，5秒

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  defaultResolution: {
    type: String,
    default: '1080p'
  },
  defaultShowSubtitles: {
    type: Boolean,
    default: true
  },
  defaultFps: {
    type: Number,
    default: 24
  },
  isExporting: {
    type: Boolean,
    default: false
  },
  canvasId: {
    type: [Number, String],
    default: ''
  }
});

const emit = defineEmits([
  'update:modelValue',
  'video-click',
  'page-change',
  'export',
  'update:resolution',
  'update:showSubtitles',
  'update:fps',
  'update:isExporting'
]);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(3);

// 视频数据状态
const videos = ref([]);
const isLoadingVideos = ref(false);
const totalVideos = ref(0);

// 轮询相关变量
const pollingTimer = ref(null); // 轮询定时器

// 分辨率选择
const resolution = ref(props.defaultResolution);

// FPS 选择
const fps = ref(props.defaultFps);

// 字幕开关
const showSubtitles = ref(props.defaultShowSubtitles);

// 计算属性：检查是否有未完成的视频（状态不为2）
const hasUnfinishedVideos = computed(() => {
  return videos.value.some(video => video.status !== 2 && video.status !== 3);
});

// 监听props中的分辨率变化
watch(() => props.defaultResolution, (newVal) => {
  resolution.value = newVal;
});

// 监听props中的FPS变化
watch(() => props.defaultFps, (newVal) => {
  fps.value = newVal;
});

// 监听props中的字幕开关变化
watch(() => props.defaultShowSubtitles, (newVal) => {
  showSubtitles.value = newVal;
});

// 监听弹窗显示状态变化
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    fetchExportVideos();
  }
});

// 获取历史导出视频
const fetchExportVideos = async () => {
  // isLoadingVideos.value = true;
  try {
    // 检查是否有画布ID
    if (!props.canvasId) {
      console.warn('缺少画布ID，无法获取导出视频列表');
      videos.value = [];
      return;
    }

    const response = await getExportVideoList(props.canvasId);

    if (response && response.success) {
      videos.value = response.data || [];
      totalVideos.value = videos.value.length;
      console.log('获取导出视频列表成功:', videos.value);

      // 检查视频状态，决定是否需要轮询
      checkVideosStatus();
    } else {
      console.error('获取导出视频列表失败:', response?.errMessage);
      ElMessage.error(response?.errMessage || '获取导出视频列表失败');
      videos.value = [];

      // 出错时停止轮询
      stopPolling();
    }
  } catch (error) {
    console.error('获取视频数据出错:', error);
    ElMessage.error('获取视频数据时发生错误');
    videos.value = [];
  } finally {
    isLoadingVideos.value = false;
  }
};

// 监听ESC键关闭弹框
const handleKeyDown = (e) => {
  if (e.key === 'Escape' && props.modelValue && !props.isExporting) {
    handleClose();
  }
};

// 关闭弹框
const handleClose = () => {
  if (props.isExporting) return;

  // 停止轮询
  stopPolling();

  emit('update:modelValue', false);
};

// 点击遮罩层关闭弹框
const handleBackdropClick = () => {
  if (props.isExporting) return;
  handleClose();
};

// 处理视频点击
const handleVideoClick = (video) => {
  emit('video-click', video);
};

// 处理分页变化
const handlePageChange = (page) => {
  currentPage.value = page;
  fetchExportVideos();
  emit('page-change', page);
};

// 设置分辨率
const setResolution = (res) => {
  resolution.value = res;
  emit('update:resolution', res);
};

// 设置帧率
const setFps = (value) => {
  fps.value = value;
  emit('update:fps', value);
};

// 处理导出
const handleExport = async () => {
  // 检查是否有画布ID
  if (!props.canvasId) {
    ElMessage.error('缺少画布ID，无法导出视频');
    return;
  }

  try {
    // 构建参数
    const params = {
      canvasId: props.canvasId,
      resolution: resolution.value,
      showSubtitle: showSubtitles.value ? 1 : 0,
      fps: fps.value
    };

    // 显示导出中状态
    emit('update:isExporting', true);
    // ElMessage.info(`正在导出${resolution.value}视频，请稍候...`);

    // 调用导出API
    const response = await exportVideo(params);

    if (response && response.success) {
      // ElMessage.success('视频导出任务已提交，请稍后查看');

      // 刷新导出视频列表
      setTimeout(() => {
        fetchExportVideos();
      }, 1000);

      // 通知父组件导出成功
      emit('export', {
        resolution: resolution.value,
        showSubtitles: showSubtitles.value,
        fps: fps.value,
        success: true,
        data: response.data
      });

      // 关闭对话框
      // handleClose();
    } else {
      ElMessage.error(response?.errMessage || '视频导出失败');

      // 通知父组件导出失败
      emit('export', {
        resolution: resolution.value,
        showSubtitles: showSubtitles.value,
        fps: fps.value,
        success: false,
        error: response?.errMessage
      });
    }
  } catch (error) {
    console.error('导出视频出错:', error);
    ElMessage.error('导出视频时发生错误，请重试');

    // 通知父组件导出失败
    emit('export', {
      resolution: resolution.value,
      showSubtitles: showSubtitles.value,
      fps: fps.value,
      success: false,
      error: error.message
    });
  } finally {
    // 恢复导出状态
    emit('update:isExporting', false);
  }
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 格式化视频时长
const formatDuration = (milliseconds) => {
  if (!milliseconds) return '00:00';
  // 将毫秒转换为秒
  const seconds = milliseconds / 1000;
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
};

// 设置字幕开关
const setShowSubtitles = (value) => {
  showSubtitles.value = value;
  emit('update:showSubtitles', value);
};

// 处理视频下载
const handleDownload = async (video) => {
  if (!video || !video.videoUrl) {
    ElMessage.error('视频链接不存在');
    return;
  }

  try {
    ElMessage.info('准备下载视频，请稍候...');

    // 使用fetch API获取视频文件
    const response = await fetch(video.videoUrl);

    // 检查响应状态
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`);
    }

    // 将响应转换为blob
    const blob = await response.blob();

    // 创建一个blob URL
    const blobUrl = URL.createObjectURL(blob);

    // 创建一个临时的a标签用于下载
    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = `视频_${formatDate(video.createTime)}_${video.resolution}.mp4`;
    link.style.display = 'none';
    document.body.appendChild(link);

    // 触发点击下载
    link.click();

    // 清理
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl); // 释放blob URL
    }, 100);

    ElMessage.success('视频下载已开始');
  } catch (error) {
    console.error('下载视频出错:', error);
    ElMessage.error(`下载视频失败: ${error.message}`);

    // 如果fetch失败，尝试直接在新标签页中打开视频
    window.open(video.videoUrl, '_blank');
  }
};

// 检查视频状态，判断是否需要轮询
const checkVideosStatus = () => {
  // 检查是否有状态不为2的视频
  const hasUnfinishedVideos = videos.value.some(video => video.status !== 2);

  if (hasUnfinishedVideos) {
    // 如果有未完成的视频，开始轮询
    startPolling();
  } else {
    // 如果所有视频都已完成，停止轮询
    stopPolling();
  }
};

// 开始轮询
const startPolling = () => {
  // 如果已经存在定时器，先清除
  stopPolling();

  // 设置新的定时器
  pollingTimer.value = setInterval(() => {
    console.log('轮询获取视频列表...');
    fetchExportVideos();
  }, POLLING_INTERVAL);

  console.log('开始轮询视频状态');
};

// 停止轮询
const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
    console.log('停止轮询视频状态');
  }
};

// 组件挂载时添加事件监听
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown);
});

// 组件卸载时移除事件监听和清除定时器
onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleKeyDown);

  // 确保清除轮询定时器
  stopPolling();
});
</script>

<style scoped>
/* 弹框容器 */
.export-video-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 11;
  animation: fadeIn 0.3s ease;
}

.status-badge el-icon {
  margin-right: 4px;
  font-size: 26px;
}

.status-badge .el-icon {
  animation: spin 1s infinite linear;
}

/* 遮罩层 */
.dialog-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
}

/* 弹框容器 */
.dialog-container {
  position: relative;
  width: 800px;
  max-width: 90%;
  max-height: 90vh;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease;
  overflow: hidden;
}

body.dark .dialog-container {
  background-color: var(--bg-card, #1e1e2d);
  color: var(--text-primary, #e2e8f0);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.73);
}

/* 标题栏 */
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #6366f1cc, #4f46e5cc);
  border-bottom: none;
}

body.dark .dialog-header {
  background: linear-gradient(135deg, #4f46e5cc, #3730a3cc);
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  font-size: 24px;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-button:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

/* 内容区域 */
.dialog-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 导出历史区域 */
.export-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  text-align: left;
  margin: 2px 0 10px 0;
}

body.dark .export-section-title {
  color: var(--text-primary);
}

/* 加载中状态 */
.export-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  /* background-color: #f9fafc; */
  border-radius: 8px;
  height: 180px;
}

body.dark .export-loading {
  /* background-color: var(--bg-secondary); */
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(99, 102, 241, 0.2);
  border-top-color: #6366f1;
  border-radius: 50%;
  animation: spin 1s infinite linear;
  margin-bottom: 10px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #64748b;
  font-size: 14px;
}

body.dark .loading-text {
  color: var(--text-secondary);
}

/* 空状态 */
.export-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  /* background-color: #f9fafc; */
  border-radius: 8px;
  text-align: center;
}

body.dark .export-empty {
  /* background-color: var(--bg-secondary); */
}

.empty-icon {
  font-size: 48px;
  color: #6366f1;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #64748b;
}

body.dark .empty-text {
  color: var(--text-secondary);
}

/* 视频网格 */
.video-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.video-item {
  overflow: hidden;
}

.video-card {
  /* background-color: #f5f5f5; */
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s;
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}

body.dark .video-card {
  background-color: var(--bg-secondary);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}

.video-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

body.dark .video-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.video-preview {
  position: relative;
  aspect-ratio: 16/9;
  overflow: hidden;
}

.thumbnail {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  /* background-color: #000; */
  transition: transform 0.3s;
  object-fit: contain;
}

.video-card:hover .thumbnail {
  transform: scale(1.05);
}

.duration {
  position: absolute;
  top: 8px;
  left: 8px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 20px;
  opacity: 0;
  transition: opacity 0.3s, background-color 0.3s;
}

/* 下载按钮样式 */
.download-icon {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 16px;
  transition: opacity 0.3s, background-color 0.3s;
  cursor: pointer;
  z-index: 3;
}

.download-icon:hover {
  background-color: rgba(99, 102, 241, 0.9);
}

body.dark .download-icon:hover {
  background-color: rgba(79, 70, 229, 0.9);
}

.video-card:hover .download-icon {
  opacity: 1;
}

.video-card:hover .play-icon {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.7);
}

/* 视频状态标签 */
.status-badge {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000000;
  color: rgba(255, 255, 255, 0.82);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  z-index: 2;
  text-align: center;
}

.video-info {
  padding: 8px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.video-name {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 6px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

body.dark .video-name {
  color: var(--text-primary);
}

.video-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #64748b;
}

body.dark .video-meta {
  color: var(--text-secondary);
}

/* 分页 */
.export-pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 导出设置 */
.export-settings {
  /* border-top: 1px solid #e5e7eb; */
  /* padding-top: 20px; */
}

body.dark .export-settings {
  border-top-color: var(--border-color);
}

.setting-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 10px;
  text-align: left;
}

body.dark .setting-label {
  color: var(--text-secondary);
}

/* 分辨率选择器 */
.resolution-options,
.fps-options {
  display: flex;
  gap: 12px;
}

.resolution-option,
.fps-option {
  padding: 8px 16px;
  border-radius: 8px;
  background-color: #f1f5f9;
  color: #64748b;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  min-width: 60px;
}

body.dark .resolution-option,
body.dark .fps-option {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
}

.resolution-option:hover,
.fps-option:hover {
  background-color: #e2e8f0;
  color: #334155;
}

body.dark .resolution-option:hover,
body.dark .fps-option:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.resolution-option.active,
.fps-option.active {
  background-color: #6366f1;
  color: white;
}

body.dark .resolution-option.active,
body.dark .fps-option.active {
  background-color: #4f46e5;
}

.resolution-option.active:hover,
.fps-option.active:hover {
  background-color: #4f46e5;
  color: white;
}

body.dark .resolution-option.active:hover,
body.dark .fps-option.active:hover {
  background-color: #4338ca;
}

/* FPS 选择 */
.export-fps {
  margin-top: 20px;
}

/* 字幕开关 */
.export-subtitles {
}

.subtitle-switch {
  display: flex;
  align-items: center;
  padding: 0 10px;
}

.subtitle-switch-component {
  margin-right: 5px;
}

/* 自定义字幕开关颜色 */
:deep(.subtitle-switch-component.el-switch.is-checked .el-switch__core) {
  background-color: #706cefd7 !important;
  border-color: #706cefd7 !important;
}

:deep(.subtitle-switch-component.el-switch .el-switch__core:hover) {
  border-color: #706cefd7 !important;
}

body.dark .subtitle-switch {
  color: var(--text-secondary);
}

/* 底部按钮区域 */
.dialog-footer {
  padding: 16px 20px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid #e5e7eb;
}

body.dark .dialog-footer {
  border-top-color: var(--border-color);
}

.cancel-btn,
.export-btn {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.cancel-btn {
  background-color: #f1f5f9;
  border: 1px solid #e2e8f0;
  color: #64748b;
}

.cancel-btn:hover:not(:disabled) {
  background-color: #e2e8f0;
}

.cancel-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

body.dark .cancel-btn {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

body.dark .cancel-btn:hover:not(:disabled) {
  background-color: var(--bg-hover);
}

.export-btn {
  background-color: #6366f1;
  border: 1px solid #6366f1;
  color: white;
}

.export-btn:hover:not(:disabled) {
  background-color: #4f46e5;
}

.export-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

body.dark .export-btn {
  background-color: #4f46e5;
  border-color: #4f46e5;
}

body.dark .export-btn:hover:not(:disabled) {
  background-color: #4338ca;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式布局 */
@media (max-width: 768px) {
  .video-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .video-grid {
    grid-template-columns: 1fr;
  }

  .dialog-container {
    width: 95%;
    max-width: 95%;
  }
}
</style>