<template>
  <div class="floating-particles">
    <transition-group name="fade">
      <div 
        v-for="(particle, index) in particles" 
        :key="index"
        class="particle"
        :style="getParticleStyle(particle)"
      ></div>
    </transition-group>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  count: {
    type: Number,
    default: 15
  },
  colors: {
    type: Array,
    default: () => ['#3b82f6', '#6366f1', '#8b5cf6', '#14b8a6', '#06b6d4']
  },
  minSize: {
    type: Number,
    default: 20
  },
  maxSize: {
    type: Number,
    default: 80
  },
  minOpacity: {
    type: Number,
    default: 0.1
  },
  maxOpacity: {
    type: Number,
    default: 0.3
  },
  interactive: {
    type: Boolean,
    default: true
  },
  interactiveForce: {
    type: Number,
    default: 8
  }
});

const containerRef = ref(null);
const mousePosition = ref({ x: 0, y: 0 });
let animationFrameId = null;

const particles = ref([]);

// 初始化粒子
onMounted(() => {
  // 使用 document 而不是特定引用，因为组件可能还未渲染完成
  containerRef.value = document.querySelector('.floating-particles');
  if (!containerRef.value) return;
  
  const containerWidth = window.innerWidth;
  const containerHeight = window.innerHeight;
  
  // 创建粒子
  particles.value = Array.from({ length: props.count }, () => ({
    x: Math.random() * containerWidth,
    y: Math.random() * containerHeight,
    size: props.minSize + Math.random() * (props.maxSize - props.minSize),
    color: props.colors[Math.floor(Math.random() * props.colors.length)],
    opacity: props.minOpacity + Math.random() * (props.maxOpacity - props.minOpacity),
    speed: 0.5 + Math.random() * 1,
    direction: Math.random() * Math.PI * 2,
    scale: 1
  }));
  
  if (props.interactive) {
    window.addEventListener('mousemove', handleMouseMove);
    animationFrameId = requestAnimationFrame(animateParticles);
  }
  
  // 添加缩放动画
  animateParticleScales();
});

onUnmounted(() => {
  if (props.interactive) {
    window.removeEventListener('mousemove', handleMouseMove);
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
    }
  }
});

function handleMouseMove(e) {
  mousePosition.value = { x: e.clientX, y: e.clientY };
}

function animateParticles() {
  if (!containerRef.value) return;
  
  particles.value.forEach(particle => {
    if (props.interactive) {
      // 计算粒子到鼠标的距离
      const dx = mousePosition.value.x - particle.x;
      const dy = mousePosition.value.y - particle.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      
      // 在一定范围内对粒子施加力
      if (distance < 200) {
        const force = (200 - distance) / 200 * props.interactiveForce;
        particle.x -= (dx / distance) * force;
        particle.y -= (dy / distance) * force;
      }
    }
    
    // 边界检查，保持粒子在视口内
    if (particle.x < -particle.size) particle.x = window.innerWidth + particle.size;
    if (particle.x > window.innerWidth + particle.size) particle.x = -particle.size;
    if (particle.y < -particle.size) particle.y = window.innerHeight + particle.size;
    if (particle.y > window.innerHeight + particle.size) particle.y = -particle.size;
  });
  
  animationFrameId = requestAnimationFrame(animateParticles);
}

// 添加缩放动画功能
function animateParticleScales() {
  particles.value.forEach((particle, index) => {
    setInterval(() => {
      // 在0.8和1.2之间随机缩放
      particle.scale = 0.8 + Math.random() * 0.4;
    }, 2000 + index * 100); // 错开每个粒子的动画时间
  });
}

function getParticleStyle(particle) {
  return {
    width: `${particle.size}px`,
    height: `${particle.size}px`,
    backgroundColor: particle.color,
    left: `${particle.x}px`,
    top: `${particle.y}px`,
    opacity: particle.opacity,
    transform: `scale(${particle.scale})`,
    transition: 'transform 2s ease-in-out'
  };
}
</script>

<style scoped>
.floating-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 0;
  overflow: hidden;
}

.particle {
  position: absolute;
  border-radius: 50%;
  filter: blur(8px);
  transform-origin: center;
  will-change: transform, opacity;
}

/* 添加过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0 !important;
}
</style> 