<template>
  <div class="radiant-text-container">
    <span
      ref="textRef"
      class="radiant-text"
      :style="textStyle"
    >
      <slot></slot>
    </span>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue';
import { useMouse, useElementBounding } from '@vueuse/core';

const props = defineProps({
  colors: {
    type: Array,
    default: () => ['#0EA5E9', '#5B21B6', '#0F172A', '#1E40AF']
  },
  interactive: {
    type: Boolean,
    default: false
  },
  direction: {
    type: String,
    default: 'br', // 'br', 'bl', 'tr', 'tl'
    validator: (val) => ['br', 'bl', 'tr', 'tl'].includes(val)
  },
  size: {
    type: String,
    default: 'md', // 'sm', 'md', 'lg', 'xl', '2xl'
    validator: (val) => ['sm', 'md', 'lg', 'xl', '2xl'].includes(val)
  },
  weight: {
    type: String,
    default: 'bold', // 'normal', 'semibold', 'bold', 'extrabold'
    validator: (val) => ['normal', 'semibold', 'bold', 'extrabold'].includes(val)
  }
});

const textRef = ref(null);
const { x: mouseX, y: mouseY } = useMouse();
const elementBounding = useElementBounding(textRef);
const gradientCenter = reactive({ x: 50, y: 50 });

// 计算最终文本样式
const textStyle = computed(() => {
  let gradientDirection;
  
  // 基于prop或交互模式确定渐变方向
  if (props.interactive) {
    gradientDirection = `circle at ${gradientCenter.x}% ${gradientCenter.y}%`;
  } else {
    switch (props.direction) {
      case 'br': gradientDirection = 'to bottom right'; break;
      case 'bl': gradientDirection = 'to bottom left'; break;
      case 'tr': gradientDirection = 'to top right'; break;
      case 'tl': gradientDirection = 'to top left'; break;
      default: gradientDirection = 'to bottom right';
    }
  }
  
  // 基于size属性确定字体大小
  let fontSize;
  switch (props.size) {
    case 'sm': fontSize = '0.875rem'; break; // text-sm
    case 'md': fontSize = '1.25rem'; break;  // text-xl
    case 'lg': fontSize = '1.5rem'; break;   // text-2xl
    case 'xl': fontSize = '2.25rem'; break;  // text-4xl
    case '2xl': fontSize = '3rem'; break;    // text-5xl
    default: fontSize = '1.25rem';
  }
  
  // 基于weight属性确定字体粗细
  let fontWeight;
  switch (props.weight) {
    case 'normal': fontWeight = '400'; break;
    case 'semibold': fontWeight = '600'; break;
    case 'bold': fontWeight = '700'; break;
    case 'extrabold': fontWeight = '800'; break;
    default: fontWeight = '700';
  }
  
  return {
    backgroundImage: `radial-gradient(${gradientDirection}, ${props.colors.join(', ')})`,
    fontSize: fontSize,
    fontWeight: fontWeight
  };
});

// 更新基于鼠标位置的梯度中心（如果交互式）
onMounted(() => {
  if (props.interactive) {
    setInterval(() => {
      if (elementBounding.left.value && elementBounding.top.value) {
        const x = ((mouseX.value - elementBounding.left.value) / elementBounding.width.value) * 100;
        const y = ((mouseY.value - elementBounding.top.value) / elementBounding.height.value) * 100;
        
        gradientCenter.x = Math.max(0, Math.min(100, x));
        gradientCenter.y = Math.max(0, Math.min(100, y));
      }
    }, 50);
  }
});
</script>

<style scoped>
.radiant-text-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.radiant-text {
  position: relative;
  display: inline-block;
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
  user-select: none;
}
</style> 