<template>
  <button
    :class="[
      'glow-button',
      variantClass,
      sizeClass,
      className,
      { 'disabled': disabled }
    ]"
    :disabled="disabled"
    @mouseenter="isHovering = true"
    @mouseleave="isHovering = false"
    @focus="isFocused = true"
    @blur="isFocused = false"
    @click="$emit('click', $event)"
    :type="buttonType"
  >
    <!-- 按钮发光效果 -->
    <span
      class="glow-effect"
      :class="{ 'active': isHovering || isFocused, 'inactive': !isHovering && !isFocused }"
      :style="{ background: variantGlow }"
    ></span>

    <!-- 按钮内容 -->
    <span
      class="button-content"
      :class="{ 'scale-up': isHovering || isFocused }"
    >
      <slot name="icon-before"></slot>
      <slot>{{ text }}</slot>
      <slot name="icon-after"></slot>
    </span>
  </button>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  text: {
    type: String,
    default: 'Button'
  },
  variant: {
    type: String,
    default: 'primary',
    validator: (val) => ['primary', 'secondary', 'success', 'danger', 'warning', 'info'].includes(val)
  },
  size: {
    type: String,
    default: 'md',
    validator: (val) => ['sm', 'md', 'lg'].includes(val)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  className: {
    type: String,
    default: ''
  },
  primary: {
    type: Boolean,
    default: false
  },
  border: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'submit'
  }
});

const isHovering = ref(false);
const isFocused = ref(false);

const emit = defineEmits(['click']);

// 按钮类型
const buttonType = computed(() => props.type || 'submit');

// 定义变体类
const variantClass = computed(() => {
  if (props.primary) return 'variant-primary';
  if (props.border) return 'variant-border';
  
  const variants = {
    primary: 'variant-primary',
    secondary: 'variant-secondary',
    success: 'variant-success',
    danger: 'variant-danger',
    warning: 'variant-warning',
    info: 'variant-info'
  };
  
  return variants[props.variant] || variants.primary;
});

// 定义发光颜色
const variantGlow = computed(() => {
  if (props.primary) return 'linear-gradient(45deg, #3b82f6 0%, #60a5fa 100%)';
  if (props.border) return 'linear-gradient(45deg, #4b5563 0%, #6b7280 100%)';
  
  const glows = {
    primary: 'linear-gradient(45deg, #3b82f6 0%, #60a5fa 100%)',
    secondary: 'linear-gradient(45deg, #4b5563 0%, #6b7280 100%)',
    success: 'linear-gradient(45deg, #10b981 0%, #34d399 100%)',
    danger: 'linear-gradient(45deg, #ef4444 0%, #f87171 100%)',
    warning: 'linear-gradient(45deg, #f59e0b 0%, #fbbf24 100%)',
    info: 'linear-gradient(45deg, #06b6d4 0%, #22d3ee 100%)'
  };
  
  return glows[props.variant] || glows.primary;
});

// 定义尺寸类
const sizeClass = computed(() => {
  const sizes = {
    sm: 'size-sm',
    md: 'size-md',
    lg: 'size-lg'
  };
  
  return sizes[props.size] || sizes.md;
});
</script>

<style scoped>
.glow-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  overflow: hidden;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s;
  border: none;
  cursor: pointer;
}

.glow-effect {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  transition: all 0.3s;
  filter: blur(8px);
}

.glow-effect.active {
  opacity: 0.7;
}

.glow-effect.inactive {
  opacity: 0;
}

.button-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  z-index: 10;
  font-size: 0.875rem;
  font-weight: 500;
  transition: transform 0.3s;
}

.button-content.scale-up {
  transform: scale(1.05);
}

/* 变体样式 */
.variant-primary {
  background-color: #3b82f6;
  color: white;
}

.variant-primary:hover {
  background-color: #2563eb;
}

.variant-secondary {
  background-color: #4b5563;
  color: white;
}

.variant-secondary:hover {
  background-color: #374151;
}

.variant-success {
  background-color: #10b981;
  color: white;
}

.variant-success:hover {
  background-color: #059669;
}

.variant-danger {
  background-color: #ef4444;
  color: white;
}

.variant-danger:hover {
  background-color: #dc2626;
}

.variant-warning {
  background-color: #f59e0b;
  color: white;
}

.variant-warning:hover {
  background-color: #d97706;
}

.variant-info {
  background-color: #06b6d4;
  color: white;
}

.variant-info:hover {
  background-color: #0891b2;
}

.variant-border {
  background-color: transparent;
  border: 2px solid #3b82f6;
  color: #3b82f6;
}

.variant-border:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

/* 尺寸样式 */
.size-sm {
  font-size: 0.75rem;
  padding: 0.5rem 0.75rem;
}

.size-md {
  font-size: 0.875rem;
  padding: 0.625rem 1.25rem;
}

.size-lg {
  font-size: 1rem;
  padding: 0.75rem 1.5rem;
}

/* 禁用状态 */
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}
</style> 