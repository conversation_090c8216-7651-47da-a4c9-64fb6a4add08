<template>
  <Teleport to="body">
    <Transition name="context-menu">
      <div
        v-if="visible"
        ref="menuRef"
        class="context-menu"
        :style="menuStyle"
        @click.stop
        @contextmenu.prevent
      >
        <div class="context-menu-content">
          <div
            v-for="(item, index) in menuItems"
            :key="index"
            class="context-menu-item"
            :class="{
              'context-menu-item--disabled': item.disabled,
              'context-menu-item--divider': item.divider
            }"
            @click="handleItemClick(item, index)"
          >
            <template v-if="!item.divider">
              <i v-if="item.icon" :class="item.icon" class="context-menu-item__icon"></i>
              <span class="context-menu-item__label">{{ item.label }}</span>
              <span v-if="item.shortcut" class="context-menu-item__shortcut">{{ item.shortcut }}</span>
            </template>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  x: {
    type: Number,
    default: 0
  },
  y: {
    type: Number,
    default: 0
  },
  items: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'item-click'])

const menuRef = ref(null)

// 计算菜单项，过滤掉隐藏的项目
const menuItems = computed(() => {
  return props.items.filter(item => !item.hidden)
})

// 计算菜单位置，确保不超出视窗
const menuStyle = computed(() => {
  if (!props.visible) return {}
  
  const style = {
    position: 'fixed',
    left: `${props.x}px`,
    top: `${props.y}px`,
    zIndex: 9999
  }
  
  return style
})

// 调整菜单位置，防止超出视窗
const adjustMenuPosition = async () => {
  if (!menuRef.value) return
  
  await nextTick()
  
  const menu = menuRef.value
  const rect = menu.getBoundingClientRect()
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight
  
  let { x, y } = props
  
  // 如果菜单超出右边界，向左调整
  if (x + rect.width > viewportWidth) {
    x = viewportWidth - rect.width - 10
  }
  
  // 如果菜单超出下边界，向上调整
  if (y + rect.height > viewportHeight) {
    y = viewportHeight - rect.height - 10
  }
  
  // 确保不超出左边界和上边界
  x = Math.max(10, x)
  y = Math.max(10, y)
  
  menu.style.left = `${x}px`
  menu.style.top = `${y}px`
}

// 处理菜单项点击
const handleItemClick = (item, index) => {
  if (item.disabled || item.divider) return
  
  emit('item-click', { item, index })
  hideMenu()
}

// 隐藏菜单
const hideMenu = () => {
  emit('update:visible', false)
}

// 点击外部隐藏菜单
const handleClickOutside = (event) => {
  if (props.visible && menuRef.value && !menuRef.value.contains(event.target)) {
    hideMenu()
  }
}

// 按ESC键隐藏菜单
const handleKeydown = (event) => {
  if (event.key === 'Escape' && props.visible) {
    hideMenu()
  }
}

// 监听菜单显示状态变化
const handleVisibilityChange = () => {
  if (props.visible) {
    adjustMenuPosition()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  document.addEventListener('keydown', handleKeydown)
  document.addEventListener('scroll', hideMenu, true)
  window.addEventListener('resize', hideMenu)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleKeydown)
  document.removeEventListener('scroll', hideMenu, true)
  window.removeEventListener('resize', hideMenu)
})

// 监听visible变化
watch(() => props.visible, handleVisibilityChange)
</script>

<style scoped>
.context-menu {
  position: fixed;
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  min-width: 120px;
  max-width: 240px;
  user-select: none;
  z-index: 9999;
}

.context-menu-content {
  display: flex;
  flex-direction: column;
}

.context-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
  color: #606266;
  position: relative;
}

.context-menu-item:hover:not(.context-menu-item--disabled):not(.context-menu-item--divider) {
  background-color: #f5f7fa;
  color: #409eff;
}

.context-menu-item--disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

.context-menu-item--divider {
  height: 1px;
  background-color: #e4e7ed;
  margin: 4px 0;
  padding: 0;
  cursor: default;
}

.context-menu-item__icon {
  margin-right: 8px;
  width: 16px;
  text-align: center;
  flex-shrink: 0;
}

.context-menu-item__label {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.context-menu-item__shortcut {
  margin-left: 16px;
  font-size: 12px;
  color: #909399;
  flex-shrink: 0;
}

/* 动画效果 */
.context-menu-enter-active,
.context-menu-leave-active {
  transition: all 0.15s ease;
}

.context-menu-enter-from {
  opacity: 0;
  transform: scale(0.95) translateY(-5px);
}

.context-menu-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(-5px);
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .context-menu {
    background: #2d2d2d;
    border-color: #4c4d4f;
    color: #e4e7ed;
  }

  .context-menu-item {
    color: #e4e7ed;
  }

  .context-menu-item:hover:not(.context-menu-item--disabled):not(.context-menu-item--divider) {
    background-color: #3a3a3a;
    color: #409eff;
  }

  .context-menu-item--disabled {
    color: #6c6e72;
  }

  .context-menu-item--divider {
    background-color: #4c4d4f;
  }

  .context-menu-item__shortcut {
    color: #909399;
  }
}
</style>
