<template>
  <div :class="['mouse-follower', { 'hidden': hidden }]" :style="followerStyle"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, reactive } from 'vue';
// 移除对@vueuse/core的依赖
// import { useMouse, useTransition } from '@vueuse/core';

const props = defineProps({
  size: {
    type: Number,
    default: 32
  },
  color: {
    type: String,
    default: 'rgba(99, 102, 241, 0.6)'
  },
  delay: {
    type: Number,
    default: 0.1
  },
  blur: {
    type: Number,
    default: 5
  },
  zIndex: {
    type: Number,
    default: 9999
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

// 自己实现鼠标位置跟踪
const mousePos = reactive({ x: 0, y: 0 });
const smoothPos = reactive({ x: 0, y: 0 });
const hidden = ref(true);
const isHovering = ref(false);
const scale = ref(1);
let animationFrame = null;

// 监听鼠标移动
function handleMouseMove(e) {
  mousePos.x = e.clientX;
  mousePos.y = e.clientY;
  
  if (hidden.value) {
    hidden.value = false;
  }
  
  if (!animationFrame) {
    animationFrame = requestAnimationFrame(updatePosition);
  }
}

// 平滑更新位置
function updatePosition() {
  // 实现平滑过渡
  smoothPos.x += (mousePos.x - smoothPos.x) * 0.2;
  smoothPos.y += (mousePos.y - smoothPos.y) * 0.2;
  
  if (
    Math.abs(mousePos.x - smoothPos.x) < 0.1 &&
    Math.abs(mousePos.y - smoothPos.y) < 0.1
  ) {
    smoothPos.x = mousePos.x;
    smoothPos.y = mousePos.y;
    animationFrame = null;
  } else {
    animationFrame = requestAnimationFrame(updatePosition);
  }
}

// 计算跟随器的位置和样式
const followerStyle = computed(() => {
  return {
    transform: `translate(${smoothPos.x}px, ${smoothPos.y}px) scale(${scale.value})`,
    width: `${props.size}px`,
    height: `${props.size}px`,
    background: props.color,
    filter: `blur(${props.blur}px)`,
    zIndex: props.zIndex,
    opacity: isHovering.value ? 0.8 : 0.6
  };
});

// 监听交互元素
function setupInteractiveElements() {
  const interactiveElements = document.querySelectorAll('a, button, [role="button"], input, select, textarea');
  
  interactiveElements.forEach(el => {
    el.addEventListener('mouseenter', () => {
      isHovering.value = true;
      scale.value = 1.5;
    });
    
    el.addEventListener('mouseleave', () => {
      isHovering.value = false;
      scale.value = 1;
    });
  });
}

onMounted(() => {
  if (!props.disabled) {
    window.addEventListener('mousemove', handleMouseMove);
    setupInteractiveElements();
    
    // 短暂延迟后再显示，避免页面加载时突然出现
    setTimeout(() => {
      hidden.value = false;
    }, 10);
  }
});

onUnmounted(() => {
  if (!props.disabled) {
    window.removeEventListener('mousemove', handleMouseMove);
    if (animationFrame) {
      cancelAnimationFrame(animationFrame);
    }
  }
});
</script>

<style scoped>
.mouse-follower {
  position: fixed;
  pointer-events: none;
  border-radius: 50%;
  mix-blend-mode: multiply;
  transition: opacity 0.3s ease, width 0.3s ease, height 0.3s ease;
  will-change: transform;
}

.hidden {
  opacity: 0 !important;
}
</style> 