<template>
  <div class="payment-modal" v-if="visible">
    <div class="payment-backdrop" @click="cancelPayment"></div>
    <div class="payment-container">
      <div class="payment-header">
        <h3>{{ title }}</h3>
        <el-icon class="close-icon" @click="cancelPayment"><Close /></el-icon>
      </div>
      
      <div class="payment-content">
        <div class="recharge-dialog-content">
          <div v-if="!showQrCode">
            <div class="exchange-rate-info">
              <el-icon>
                <InfoFilled />
              </el-icon>
              <span>充值比例：1元 = {{ pointsExchangeRate }}积分</span>
            </div>

            <div class="amount-selection">
              <div class="amount-label">选择充值金额</div>
              <div class="amount-options">
                <div v-for="amount in predefinedAmounts" :key="amount"
                  :class="['amount-option', rechargeAmount === amount ? 'selected' : '']" @click="selectAmount(amount)">
                  <div class="amount-value">¥{{ amount }}</div>
                  <div class="points-value">{{ amount * pointsExchangeRate }}积分</div>
                </div>
              </div>
            </div>

            <!-- <div class="custom-amount">
              <div class="amount-label">自定义金额（元）</div>
              <el-input-number v-model="rechargeAmount" :min="1" :max="10000" :precision="1" size="large"
                controls-position="right" style="width: 100%" />
            </div> -->

            <div class="payment-summary">
              <div class="summary-item">
                <span class="label">充值金额：</span>
                <span class="value">¥{{ rechargeAmount }}</span>
              </div>
              <div class="summary-item">
                <span class="label">获得积分：</span>
                <span class="value highlight">{{ getPointsForAmount }}点</span>
              </div>
            </div>

            <div class="payment-method">
              <!-- <div class="method-label">支付方式</div> -->
              <div class="method-option selected">
                <i class="icon-wechat">
                  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="35" height="35"
                    viewBox="0 0 24 24">
                    <defs>
                      <path id="0f20791c-6774-4e52-920f-6b6d8404b4dc-a"
                        d="M6.724 0h10.552c2.338 0 3.186.243 4.04.7A4.766 4.766 0 0 1 23.3 2.684c.458.855.701 1.703.701 4.04v10.553c0 2.338-.243 3.186-.7 4.04a4.766 4.766 0 0 1-1.983 1.983c-.855.458-1.703.701-4.04.701H6.723c-2.338 0-3.186-.243-4.04-.7A4.766 4.766 0 0 1 .7 21.316c-.457-.854-.7-1.702-.7-4.039V6.723c0-2.338.243-3.186.7-4.04A4.766 4.766 0 0 1 2.684.7C3.538.243 4.386 0 6.723 0z" />
                      <linearGradient id="0f20791c-6774-4e52-920f-6b6d8404b4dc-b" x1="50%" x2="50%" y1="0%" y2="100%">
                        <stop offset="0%" stop-color="#02E36F" />
                        <stop offset="100%" stop-color="#05CD65" />
                        <stop offset="100%" stop-color="#07C160" />
                      </linearGradient>
                    </defs>
                    <g fill="none" fill-rule="evenodd">
                      <mask id="0f20791c-6774-4e52-920f-6b6d8404b4dc-c" fill="#fff">
                        <use xlink:href="#0f20791c-6774-4e52-920f-6b6d8404b4dc-a" />
                      </mask>
                      <path fill="url(#0f20791c-6774-4e52-920f-6b6d8404b4dc-b)" d="M0 0h24v24H0z"
                        mask="url(#0f20791c-6774-4e52-920f-6b6d8404b4dc-c)" />
                      <path fill="#FFF"
                        d="M19.095 17.63c1.141-.826 1.87-2.05 1.87-3.408 0-2.49-2.423-4.51-5.411-4.51-2.989 0-5.411 2.02-5.411 4.51 0 2.49 2.422 4.51 5.41 4.51.618 0 1.214-.089 1.767-.248a.543.543 0 0 1 .447.06l1.184.683c.033.02.065.034.104.034.1 0 .18-.08.18-.18 0-.045-.017-.09-.028-.132l-.244-.91a.36.36 0 0 1 .132-.409M13.75 13.5a.721.721 0 1 1 0-1.442.721.721 0 0 1 0 1.443M9.493 4.734c3.24 0 5.925 1.977 6.414 4.562a7.206 7.206 0 0 0-.353-.01c-3.27 0-5.922 2.21-5.922 4.936 0 .46.077.904.218 1.326a7.687 7.687 0 0 1-2.476-.288.651.651 0 0 0-.536.071l-1.421.82a.245.245 0 0 1-.125.041.216.216 0 0 1-.217-.216c0-.054.021-.107.035-.158l.292-1.092a.433.433 0 0 0-.159-.49C3.876 13.243 3 11.775 3 10.145c0-2.989 2.907-5.412 6.493-5.412zm7.865 7.323a.721.721 0 1 1 0 1.443.721.721 0 0 1 0-1.443zM7.328 7.548a.866.866 0 1 0 0 1.732.866.866 0 0 0 0-1.732zm4.33 0a.866.866 0 1 0 0 1.731.866.866 0 0 0 0-1.73z"
                        mask="url(#0f20791c-6774-4e52-920f-6b6d8404b4dc-c)" />
                    </g>
                  </svg>
                </i>
                <span>微信支付</span>
              </div>
            </div>
          </div>

          <div v-else class="qrcode-container">
            <div class="qrcode-title">请使用微信扫码支付</div>
            <div class="qrcode-wrapper">
              <QrcodeVue 
                v-if="qrCodeUrl" 
                :value="qrCodeUrl" 
                :size="200" 
                level="H" 
                render-as="svg"
                class="qrcode-image"
              />
              <div v-else class="qrcode-loading">
                <el-icon class="is-loading"><Loading /></el-icon>
                <span>正在生成支付二维码...</span>
              </div>
            </div>
            <div class="payment-info">
              <div class="payment-amount">¥{{ rechargeAmount }}</div>
              <div class="payment-status" v-if="paymentStatus === 0">等待支付...</div>
              <div class="payment-status success" v-else-if="paymentStatus === 2">支付成功</div>
              <div class="payment-status warning" v-else-if="paymentStatus === 3 || paymentStatus === 4">支付已取消</div>
            </div>
            <div class="payment-tips">
              <p>1. 请在5分钟内完成支付</p>
              <p>2. 支付成功后将自动为您的账户充值积分</p>
              <p>3. 如有问题请联系客服</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="payment-footer">
        <el-button @click="cancelPayment">{{ showQrCode ? '关闭' : '取消' }}</el-button>
        <el-button v-if="!showQrCode" type="primary" @click="confirmRecharge" :loading="paymentLoading">
          立即支付
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled, Loading, Close } from '@element-plus/icons-vue'
import QrcodeVue from 'qrcode.vue'
import { createPayOrder, getPayOrderStatus, closePayOrder } from '@/api/auth'

// 定义组件的 props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  pointsExchangeRate: {
    type: Number,
    default: 100
  },
  title: {
    type: String,
    default: '积分充值'
  }
})

// 定义组件的事件
const emit = defineEmits(['update:visible', 'payment-success', 'payment-cancel'])

// 组件内部状态
const rechargeAmount = ref(10)
const predefinedAmounts = [10, 20, 50,80, 100]
const paymentLoading = ref(false)
const orderNo = ref('')
const qrCodeUrl = ref('')
const paymentStatus = ref(0) // 0-未支付，1-支付中，2-已支付，3-已取消，4-已退款
const paymentTimer = ref(null)
const showQrCode = ref(false)

// 计算可获得的积分
const getPointsForAmount = computed(() => {
  return rechargeAmount.value * props.pointsExchangeRate
})

// 选择预设金额
const selectAmount = (amount) => {
  rechargeAmount.value = amount
}

// 确认充值
const confirmRecharge = async () => {
  try {
    paymentLoading.value = true
    
    // 创建支付订单
    const orderData = {
      amount: rechargeAmount.value,
      body: `充值${rechargeAmount.value}元获得${getPointsForAmount.value}积分`,
      payType: 1, // 微信支付
      subject: "积分充值"
    }
    
    const response = await createPayOrder(orderData)
    
    if (response && response.data) {
      orderNo.value = response.data.orderNo
      qrCodeUrl.value = response.data.codeUrl
      paymentStatus.value = response.data.status
      showQrCode.value = true
      
      // 开始轮询支付状态
      startPollingPaymentStatus()
    } else {
      ElMessage.error('创建支付订单失败')
    }
  } catch (error) {
    console.error('支付请求失败:', error)
    ElMessage.error('创建支付订单失败，请稍后重试')
  } finally {
    paymentLoading.value = false
  }
}

// 开始轮询支付状态
const startPollingPaymentStatus = () => {
  // 清除可能存在的定时器
  if (paymentTimer.value) {
    clearInterval(paymentTimer.value)
  }
  
  // 设置轮询间隔为3秒
  paymentTimer.value = setInterval(async () => {
    try {
      if (!orderNo.value) {
        clearInterval(paymentTimer.value)
        return
      }
      
      const response = await getPayOrderStatus(orderNo.value)
      
      if (response && response.data) {
        paymentStatus.value = response.data.status
        
        // 如果支付成功
        if (response.data.status === 2) {
          // 清除轮询
          clearInterval(paymentTimer.value)
          paymentTimer.value = null
          
          // 触发支付成功事件
          emit('payment-success', {
            amount: rechargeAmount.value,
            points: getPointsForAmount.value
          })
          
          // 显示成功消息
          ElMessage.success(`充值${rechargeAmount.value}元成功，获得${getPointsForAmount.value}积分`)
          
          // 关闭对话框
          closeModal()
        }
        // 如果支付已取消或已退款
        else if (response.data.status === 3 || response.data.status === 4) {
          clearInterval(paymentTimer.value)
          paymentTimer.value = null
          ElMessage.warning('支付已取消')
        }
      }
    } catch (error) {
      console.error('查询支付状态失败:', error)
    }
  }, 3000)
}

// 取消支付
const cancelPayment = async () => {
  if (orderNo.value && paymentStatus.value === 0) {
    try {
      // 清除轮询
      if (paymentTimer.value) {
        clearInterval(paymentTimer.value)
        paymentTimer.value = null
      }
      
      // 关闭订单
      await closePayOrder(orderNo.value)
      ElMessage.info('支付已取消')
      
      // 触发支付取消事件
      emit('payment-cancel')
    } catch (error) {
      console.error('取消支付失败:', error)
    }
  }
  
  closeModal()
}

// 关闭模态框
const closeModal = () => {
  // 通知父组件更新 visible 状态
  emit('update:visible', false)
  
  // 重置状态
  resetState()
}

// 重置状态
const resetState = () => {
  rechargeAmount.value = 10
  paymentStatus.value = 0
  orderNo.value = ''
  qrCodeUrl.value = ''
  showQrCode.value = false
  
  // 清除可能存在的定时器
  if (paymentTimer.value) {
    clearInterval(paymentTimer.value)
    paymentTimer.value = null
  }
}

// 在组件卸载前清除定时器
onUnmounted(() => {
  if (paymentTimer.value) {
    clearInterval(paymentTimer.value)
    paymentTimer.value = null
  }
})
</script>

<style scoped>
/* 支付对话框样式 */
.payment-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  animation: fadeIn 0.3s ease;
  color: #6769f3;
}

.payment-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.payment-container {
  position: relative;
  width: 90%;
  max-width: 460px;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease;
  transition: background-color 0.3s;
}

body.dark .payment-container {
  background-color: var(--bg-card);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.4);
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f8fafc;
  transition: background-color 0.3s, border-color 0.3s;
}

body.dark .payment-header {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

.payment-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  transition: color 0.3s;
}

body.dark .payment-header h3 {
  color: var(--text-primary);
}

.payment-content {
  padding: 16px;
  overflow-y: auto;
  max-height: 70vh;
}

.payment-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 12px 16px;
  border-top: 1px solid #e2e8f0;
  background-color: #f8fafc;
  transition: background-color 0.3s, border-color 0.3s;
}

body.dark .payment-footer {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 充值对话框样式 */
.recharge-dialog-content {
  padding: 5px;
}

.exchange-rate-info {
  background-color: rgba(99, 102, 241, 0.1);
  padding: 8px 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  color: #6366f1;
}

.exchange-rate-info .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

.amount-selection {
  margin-bottom: 16px;
}

.amount-label {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 8px;
}

.amount-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.amount-option {
  flex: 1;
  min-width: calc(33.333% - 8px);
  border: 1px solid #87878733;
  border-radius: 8px;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.amount-option:hover {
  border-color: #6366f1;
}

.amount-option.selected {
  border-color: #6366f1;
  background-color: rgba(99, 102, 241, 0.05);
}

.amount-option .amount-value {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 2px;
}

.amount-option .points-value {
  font-size: 12px;
  color: #67c23a;
}

.custom-amount {
  margin-bottom: 16px;
}

.payment-summary {
  background-color: #f9fafc;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
}

body.dark .payment-summary {
  background-color: var(--bg-tertiary);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-item .label {
  color: #606266;
}

.summary-item .value {
  font-weight: 600;
}

.summary-item .value.highlight {
  color: #6366f1;
  font-size: 16px;
}

.payment-method {
  margin-bottom: 12px;
}

.method-label {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 8px;
}

.method-option {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  gap: 8px;
  transition: all 0.3s ease;
}

.method-option.selected {
  border-color: #6366f1;
  background-color: rgba(99, 102, 241, 0.05);
}

.payment-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

/* 二维码容器样式 */
.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}

.qrcode-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

body.dark .qrcode-title {
  color: #f3f4f6;
}

.qrcode-wrapper {
  width: 180px;
  height: 180px;
  background-color: #fff;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}

.qrcode-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.qrcode-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #909399;
}

.qrcode-loading .el-icon {
  font-size: 28px;
  color: #6366f1;
}

.payment-info {
  margin-bottom: 12px;
  text-align: center;
}

.payment-amount {
  font-size: 22px;
  font-weight: 700;
  color: #6366f1;
  margin-bottom: 6px;
}

.payment-status {
  font-size: 14px;
  color: #909399;
}

.payment-status.success {
  color: #67c23a;
}

.payment-status.warning {
  color: #e6a23c;
}

.payment-tips {
  width: 100%;
  background-color: #f9fafc;
  border-radius: 8px;
  padding: 10px 12px;
  margin-top: 12px;
}

body.dark .payment-tips {
  background-color: var(--bg-tertiary);
}

.payment-tips p {
  margin: 6px 0;
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

body.dark .payment-tips p {
  color: #a0a0a0;
}

/* 响应式布局调整 */
@media (max-width: 576px) {
  .payment-container {
    width: 95%;
    max-height: 85vh;
  }
  
  .amount-option {
    min-width: calc(50% - 8px);
  }
  
  .payment-header h3 {
    font-size: 15px;
  }
  
  .payment-content {
    padding: 12px;
    max-height: 65vh;
  }
  
  .payment-footer {
    padding: 10px 12px;
  }
  
  .qrcode-wrapper {
    width: 160px;
    height: 160px;
  }
}
</style> 