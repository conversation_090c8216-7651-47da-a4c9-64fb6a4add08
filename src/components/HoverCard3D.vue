<template>
  <div
    ref="cardRef"
    class="relative w-full h-full rounded-xl overflow-hidden cursor-pointer"
    @mousemove="handleMouseMove"
    @mouseleave="handleMouseLeave"
  >
    <div
      class="absolute inset-0 z-10 transition-transform duration-300 card-content"
      :style="cardStyle"
    >
      <slot></slot>
    </div>
    <div
      class="absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-300 dark:from-blue-900 dark:to-blue-700"
      :style="bgStyle"
    ></div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { cn } from '../lib/utils';

const props = defineProps({
  rotateMultiplier: {
    type: Number,
    default: 3
  },
  scaleOnHover: {
    type: Boolean,
    default: true
  },
  glareOnHover: {
    type: Boolean,
    default: true
  }
});

const cardRef = ref(null);
const rotation = reactive({ x: 0, y: 0 });
const translation = reactive({ x: 0, y: 0 });
const glarePosition = reactive({ x: 0, y: 0 });
const isHovering = ref(false);

const cardStyle = computed(() => ({
  transform: `perspective(1000px) rotateX(${rotation.x}deg) rotateY(${rotation.y}deg) translateX(${translation.x}px) translateY(${translation.y}px) scale(${isHovering.value && props.scaleOnHover ? 1.05 : 1})`,
}));

const bgStyle = computed(() => {
  if (!props.glareOnHover) return {};
  
  return {
    backgroundImage: isHovering.value 
      ? `radial-gradient(circle at ${glarePosition.x}% ${glarePosition.y}%, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 60%)` 
      : 'none',
  };
});

function handleMouseMove(e) {
  if (!cardRef.value) return;
  
  isHovering.value = true;
  
  const rect = cardRef.value.getBoundingClientRect();
  const width = rect.width;
  const height = rect.height;
  
  // Calculate mouse position relative to card center (in percentage)
  const mouseX = e.clientX - rect.left;
  const mouseY = e.clientY - rect.top;
  
  const centerX = width / 2;
  const centerY = height / 2;
  
  // Calculate rotation based on mouse position
  rotation.x = ((mouseY - centerY) / centerY) * -props.rotateMultiplier;
  rotation.y = ((mouseX - centerX) / centerX) * props.rotateMultiplier;
  
  // Set subtle translation to enhance 3D effect
  translation.x = ((mouseX - centerX) / centerX) * 2;
  translation.y = ((mouseY - centerY) / centerY) * 2;
  
  // Update glare position
  glarePosition.x = (mouseX / width) * 100;
  glarePosition.y = (mouseY / height) * 100;
}

function handleMouseLeave() {
  isHovering.value = false;
  rotation.x = 0;
  rotation.y = 0;
  translation.x = 0;
  translation.y = 0;
}
</script> 