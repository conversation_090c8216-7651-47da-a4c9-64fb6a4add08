<template>
  <div class="story-design-container">
    <AXThoughtChain v-if="designResult && designResult.status === 'in-progress'" :loading="true" :thinking="true">
      <div class="thought-content">正在生成故事设计，请稍候...</div>
    </AXThoughtChain>

    <div v-if="storyDesign" class="design-content">

      <!-- 故事基础信息 -->
      <div class="design-section story-design-section animation-delay-1" v-if="storyDesign.storyBasics">
        <div class="section-header">
          <el-icon>
            <Document />
          </el-icon>
          <h3 class="section-title">故事基础</h3>

          <div class="section-actions">

            <!-- <el-tooltip content="生成故事" placement="top" :effect="'light'">
              <div class="scene-group-icon-btn" @click.stop="insertTextSendMessage('调用工具生成故事')">
                生成故事
              </div>
            </el-tooltip> -->
            <template v-if="!editingSections.storyBasics">
              <el-tooltip content="编辑" placement="top" :effect="'light'">
                <div class="edit-btn" @click="startEditing('storyBasics')">
                  <el-icon>
                    <Edit />
                  </el-icon>
                </div>
              </el-tooltip>
            </template>
            <template v-else>
              <el-tooltip content="取消" placement="top" :effect="'light'">
                <div class="cancel-btn" @click="cancelEditing('storyBasics')">
                  <el-icon>
                    <Close />
                  </el-icon>
                </div>
              </el-tooltip>
              <el-tooltip content="保存" placement="top" :effect="'light'">
                <div class="save-btn" @click="saveEditing('storyBasics')">
                  <el-icon>
                    <CircleCheck />
                  </el-icon>
                </div>
              </el-tooltip>
            </template>
          </div>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">故事标题</div>
            <div v-if="!editingSections.storyBasics" class="info-value">{{ storyDesign.storyBasics.title }}</div>
            <el-input v-else v-model="tempStoryDesign.storyBasics.title" placeholder="请输入故事标题"
              class="edit-input"></el-input>
          </div>
          <div class="info-item">
            <div class="info-label">故事类型</div>
            <div v-if="!editingSections.storyBasics" class="info-value">{{ storyDesign.storyBasics.genre }}</div>
            <el-input v-else v-model="tempStoryDesign.storyBasics.genre" placeholder="请输入故事类型"
              class="edit-input"></el-input>
          </div>
          <div class="info-item">
            <div class="info-label">目标受众</div>
            <div v-if="!editingSections.storyBasics" class="info-value">{{ storyDesign.storyBasics.targetAudience }}
            </div>
            <el-input v-else v-model="tempStoryDesign.storyBasics.targetAudience" placeholder="请输入目标受众"
              class="edit-input"></el-input>
          </div>
          <div class="info-item">
            <div class="info-label">章节数量</div>
            <div v-if="!editingSections.storyBasics" class="info-value">{{ storyDesign.storyBasics.length.chapters }} 章
            </div>
            <el-input-number v-else v-model="tempStoryDesign.storyBasics.length.chapters" :min="1" :max="100"
              class="edit-input-number"></el-input-number>
          </div>
        </div>
      </div>

      <!-- 主题与风格 -->
      <div class="design-section animation-delay-2" v-if="storyDesign.themeAndTone">
        <div class="section-header">
          <el-icon>
            <Star />
          </el-icon>
          <h3 class="section-title">主题与风格</h3>

          <div class="section-actions">
            <template v-if="!editingSections.themeAndTone">
              <el-tooltip content="编辑" placement="top" :effect="'light'">
                <div class="edit-btn" @click="startEditing('themeAndTone')">
                  <el-icon>
                    <Edit />
                  </el-icon>
                </div>
              </el-tooltip>
            </template>
            <template v-else>
              <el-tooltip content="取消" placement="top" :effect="'light'">
                <div class="cancel-btn" @click="cancelEditing('themeAndTone')">
                  <el-icon>
                    <Close />
                  </el-icon>
                </div>
              </el-tooltip>
              <el-tooltip content="保存" placement="top" :effect="'light'">
                <div class="save-btn" @click="saveEditing('themeAndTone')">
                  <el-icon>
                    <CircleCheck />
                  </el-icon>
                </div>
              </el-tooltip>
            </template>
          </div>
        </div>
        <div class="theme-container">
          <div class="theme-item">
            <div class="theme-label">主题</div>
            <div v-if="!editingSections.themeAndTone" class="theme-value">{{ storyDesign.themeAndTone.mainTheme }}</div>
            <el-input v-else v-model="tempStoryDesign.themeAndTone.mainTheme" type="textarea" :autosize="{ minRows: 2, maxRows: 12 }"
              placeholder="请输入故事主题" class="edit-textarea"></el-input>
          </div>
          <div class="theme-item">
            <div class="theme-label">故事风格</div>
            <div v-if="!editingSections.themeAndTone" class="theme-value">{{ storyDesign.themeAndTone.writingStyle }}
            </div>
            <el-input v-else v-model="tempStoryDesign.themeAndTone.writingStyle" type="textarea" :autosize="{ minRows: 2, maxRows: 12 }"
              placeholder="请输入写作风格" class="edit-textarea"></el-input>
          </div>

          <!-- 新增图像风格 -->
          <div class="theme-item" v-if="false">
            <div class="theme-label">图像风格</div>
            <div v-if="!editingSections.themeAndTone" class="theme-value">
              <template v-if="storyDesign.themeAndTone.imageStyle">
                {{ getStyleName(storyDesign.themeAndTone.imageStyle) }}
                <span class="style-prompt-text">{{ storyDesign.themeAndTone.imageStyle }}</span>
              </template>
              <template v-else>未选择</template>
            </div>
            <div v-else class="style-selector-container">
              <StyleSelector :styles="styles" v-model:selectedStyleId="selectedStyleId" :isLoading="isLoadingStyles"
                @style-select="handleStyleSelect" />
            </div>
          </div>

          <!-- 新增语音ID -->
          <div class="theme-item" v-if="false">
            <div class="theme-label">旁白音色</div>
            <div v-if="!editingSections.themeAndTone" class="theme-value">
              {{ getVoiceName(storyDesign.themeAndTone.voiceID) }}
              <span class="voice-id-note" v-if="storyDesign.themeAndTone.voiceID">(ID: {{
                storyDesign.themeAndTone.voiceID
              }})</span>
              <!-- 添加试听按钮 -->
              <div v-if="storyDesign.themeAndTone.voiceID" class="voice-play-button" 
                :class="{ 'playing': isPlaying(storyDesign.themeAndTone.voiceID) }" 
                @click.stop="playVoice(storyDesign.themeAndTone.voiceID)">
                <el-icon>
                  <VideoPause v-if="isPlaying(storyDesign.themeAndTone.voiceID)" />
                  <VideoPlay v-else />
                </el-icon>
                <span class="wave-animation" v-if="isPlaying(storyDesign.themeAndTone.voiceID)">
                  <span class="wave-bar"></span>
                  <span class="wave-bar"></span>
                  <span class="wave-bar"></span>
                  <span class="wave-bar"></span>
                </span>
              </div>
            </div>
            <div v-else class="voice-selector-container">
              <VoiceSelector :voices="voices" v-model:selectedVoice="tempStoryDesign.themeAndTone.voiceID"
                :isLoading="isLoadingVoices" @refresh-voices="$emit('refresh-voices')" />
            </div>
          </div>

          <!-- 新增图像尺寸 -->
          <div class="theme-item" v-if="false">
            <div class="theme-label">图像尺寸</div>
            <div v-if="!editingSections.themeAndTone" class="theme-value">{{ storyDesign.themeAndTone.size }}</div>
            <div v-else class="ratio-selector">
              <RatioSelector v-model="tempStoryDesign.themeAndTone.size" />
            </div>
          </div>

        </div>
      </div>

      <!-- 角色设定 -->
      <div class="design-section animation-delay-3" v-if="storyDesign.characters">
        <div class="section-header">
          <el-icon>
            <User />
          </el-icon>
          <h3 class="section-title">角色设定</h3>

          <div class="section-actions">
            <template v-if="!editingSections.characters">
              <el-tooltip content="编辑" placement="top" :effect="'light'">
                <div class="edit-btn" @click="startEditing('characters')">
                  <el-icon>
                    <Edit />
                  </el-icon>
                </div>
              </el-tooltip>
            </template>
            <template v-else>
              <el-tooltip content="取消" placement="top" :effect="'light'">
                <div class="cancel-btn" @click="cancelEditing('characters')">
                  <el-icon>
                    <Close />
                  </el-icon>
                </div>
              </el-tooltip>
              <el-tooltip content="保存" placement="top" :effect="'light'">
                <div class="save-btn" @click="saveEditing('characters')">
                  <el-icon>
                    <CircleCheck />
                  </el-icon>
                </div>
              </el-tooltip>
            </template>
          </div>
        </div>
        <div class="characters-container">
          <!-- 主角 -->
          <div class="character-card protagonist">
            <div class="character-header">
              <div class="character-avatar" style="background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%)">
                {{ editingSections.characters ? tempStoryDesign.characters.protagonist.name.charAt(0) :
                  storyDesign.characters.protagonist.name.charAt(0) }}
              </div>
              <div class="character-title">
                <div v-if="!editingSections.characters" class="character-name">{{
                  storyDesign.characters.protagonist.name }}
                </div>
                <el-input v-else v-model="tempStoryDesign.characters.protagonist.name" placeholder="主角名称"
                  class="edit-input character-name-input"></el-input>
                <div class="character-role">主角</div>
              </div>
            </div>
            <div v-if="!editingSections.characters" class="character-desc">{{
              storyDesign.characters.protagonist.description
            }}</div>
            <el-input v-else v-model="tempStoryDesign.characters.protagonist.description" type="textarea" :autosize="{ minRows: 2, maxRows: 12 }"
              placeholder="请输入主角描述" class="edit-textarea"></el-input>

            <!-- 新增主角背景 -->
            <template v-if="storyDesign.characters.protagonist.background">
              <div class="character-background-label">背景故事</div>
              <div v-if="!editingSections.characters" class="character-background">{{
                storyDesign.characters.protagonist.background }}</div>
              <el-input v-else v-model="tempStoryDesign.characters.protagonist.background" type="textarea" :autosize="{ minRows: 2, maxRows: 12 }"
                placeholder="请输入主角背景故事" class="edit-textarea"></el-input>
            </template>

          </div>

          <!-- 反派 -->
          <div class="character-card antagonist">
            <div class="character-header">
              <div class="character-avatar" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%)">
                {{ editingSections.characters ? tempStoryDesign.characters.antagonist.name.charAt(0) :
                  storyDesign.characters.antagonist.name.charAt(0) }}
              </div>
              <div class="character-title">
                <div v-if="!editingSections.characters" class="character-name">{{ storyDesign.characters.antagonist.name
                }}
                </div>
                <el-input v-else v-model="tempStoryDesign.characters.antagonist.name" placeholder="反派名称"
                  class="edit-input character-name-input"></el-input>
                <div class="character-role">反派</div>
              </div>
            </div>
            <div v-if="!editingSections.characters" class="character-desc">{{
              storyDesign.characters.antagonist.description }}
            </div>
            <el-input v-else v-model="tempStoryDesign.characters.antagonist.description" type="textarea" :autosize="{ minRows: 2, maxRows: 12 }"
              placeholder="请输入反派描述" class="edit-textarea"></el-input>

            <!-- 新增反派背景 -->
            <div class="character-background-label">背景故事</div>
            <div v-if="!editingSections.characters" class="character-background">{{
              storyDesign.characters.antagonist.background }}</div>
            <el-input v-else v-model="tempStoryDesign.characters.antagonist.background" type="textarea" :autosize="{ minRows: 2, maxRows: 12 }"
              placeholder="请输入反派背景故事" class="edit-textarea"></el-input>
          </div>

          <!-- 配角 -->
          <div class="supporting-characters">
            <div class="supporting-title">
              配角
              <el-button v-if="editingSections.characters" type="primary" size="small" circle
                @click="addSupportingCharacter" class="add-character-btn">
                <el-icon>
                  <Plus />
                </el-icon>
              </el-button>
            </div>
            <div class="supporting-list">
              <div
                v-for="(character, index) in (editingSections.characters ? tempStoryDesign.characters.supportingCharacters : storyDesign.characters.supportingCharacters)"
                :key="index" class="supporting-item">
                <div class="supporting-avatar" :style="getCharacterAvatarStyle(character)">
                  {{ character.charAt(0) }}
                </div>
                <div v-if="!editingSections.characters" class="supporting-name">{{ character }}</div>
                <el-input v-else v-model="tempStoryDesign.characters.supportingCharacters[index]" placeholder="配角名称"
                  class="edit-input supporting-name-input"></el-input>
                <el-button v-if="editingSections.characters" type="danger" size="small" circle
                  @click="removeSupportingCharacter(index)" class="remove-character-btn">
                  <el-icon>
                    <Close />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 新增叙事技巧部分 -->
      <div class="design-section animation-delay-4" v-if="storyDesign.adaptiveFeatures">
        <div class="section-header">
          <el-icon>
            <Connection />
          </el-icon>
          <h3 class="section-title">叙事技巧</h3>

          <div class="section-actions">
            <template v-if="!editingSections.adaptiveFeatures">
              <el-tooltip content="编辑" placement="top" :effect="'light'">
                <div class="edit-btn" @click="startEditing('adaptiveFeatures')">
                  <el-icon>
                    <Edit />
                  </el-icon>
                </div>
              </el-tooltip>
            </template>
            <template v-else>
              <el-tooltip content="取消" placement="top" :effect="'light'">
                <div class="cancel-btn" @click="cancelEditing('adaptiveFeatures')">
                  <el-icon>
                    <Close />
                  </el-icon>
                </div>
              </el-tooltip>
              <el-tooltip content="保存" placement="top" :effect="'light'">
                <div class="save-btn" @click="saveEditing('adaptiveFeatures')">
                  <el-icon>
                    <CircleCheck />
                  </el-icon>
                </div>
              </el-tooltip>
            </template>
          </div>
        </div>
        <div class="elements-container">
          <div class="element-item">
            <div class="element-label">结构元素</div>
            <div v-if="!editingSections.adaptiveFeatures" class="element-value">{{ storyDesign.adaptiveFeatures?.structuralElements }}</div>
            <el-input v-else v-model="tempStoryDesign.adaptiveFeatures.structuralElements" type="textarea" :autosize="{ minRows: 2, maxRows: 12 }"
              placeholder="请输入结构元素，如线索推进、闪回等" class="edit-textarea"></el-input>
          </div>
          <div class="element-item">
            <div class="element-label">修辞手法</div>
            <div v-if="!editingSections.adaptiveFeatures" class="element-value">{{ storyDesign.adaptiveFeatures?.rhetoricalDevices }}</div>
            <el-input v-else v-model="tempStoryDesign.adaptiveFeatures.rhetoricalDevices" type="textarea" :autosize="{ minRows: 2, maxRows: 12 }"
              placeholder="请输入修辞手法，如环境描写、对比、悬念设置等" class="edit-textarea"></el-input>
          </div>
          <div class="element-item">
            <div class="element-label">叙事方式</div>
            <div v-if="!editingSections.adaptiveFeatures" class="element-value">{{ storyDesign.adaptiveFeatures?.narrativeApproach }}</div>
            <el-input v-else v-model="tempStoryDesign.adaptiveFeatures.narrativeApproach" type="textarea" :autosize="{ minRows: 2, maxRows: 12 }"
              placeholder="请输入叙事方式，如第一人称、第三人称等" class="edit-textarea"></el-input>
          </div>
        </div>
      </div>

      <!-- 剧情大纲 -->
      <div class="design-section animation-delay-5" v-if="storyDesign.plotOutline">
        <div class="section-header">
          <el-icon>
            <Connection />
          </el-icon>
          <h3 class="section-title">剧情大纲</h3>

          <div class="section-actions">
            <template v-if="!editingSections.plotOutline">
              <el-tooltip content="编辑" placement="top" :effect="'light'">
                <div class="edit-btn" @click="startEditing('plotOutline')">
                  <el-icon>
                    <Edit />
                  </el-icon>
                </div>
              </el-tooltip>
            </template>
            <template v-else>
              <el-tooltip content="取消" placement="top" :effect="'light'">
                <div class="cancel-btn" @click="cancelEditing('plotOutline')">
                  <el-icon>
                    <Close />
                  </el-icon>
                </div>
              </el-tooltip>
              <el-tooltip content="保存" placement="top" :effect="'light'">
                <div class="save-btn" @click="saveEditing('plotOutline')">
                  <el-icon>
                    <CircleCheck />
                  </el-icon>
                </div>
              </el-tooltip>
            </template>
          </div>
        </div>
        <div class="plot-container">
          <div class="plot-item">
            <div class="plot-badge">开端</div>
            <div v-if="!editingSections.plotOutline" class="plot-content">{{ storyDesign.plotOutline.setup }}</div>
            <el-input v-else v-model="tempStoryDesign.plotOutline.setup" type="textarea" :autosize="{ minRows: 2, maxRows: 12 }" placeholder="请输入故事开端"
              class="edit-textarea"></el-input>
          </div>
          <div class="plot-arrow"><el-icon>
              <ArrowDown />
            </el-icon></div>
          <div class="plot-item">
            <div class="plot-badge">冲突</div>
            <div v-if="!editingSections.plotOutline" class="plot-content">{{ storyDesign.plotOutline.conflict }}</div>
            <el-input v-else v-model="tempStoryDesign.plotOutline.conflict" type="textarea" :autosize="{ minRows: 2, maxRows: 12 }"
              placeholder="请输入故事冲突" class="edit-textarea"></el-input>
          </div>
          <div class="plot-arrow"><el-icon>
              <ArrowDown />
            </el-icon></div>
          <div class="plot-item">
            <div class="plot-badge">高潮</div>
            <div v-if="!editingSections.plotOutline" class="plot-content">{{ storyDesign.plotOutline.climax }}</div>
            <el-input v-else v-model="tempStoryDesign.plotOutline.climax" type="textarea" :autosize="{ minRows: 2, maxRows: 12 }"
              placeholder="请输入故事高潮" class="edit-textarea"></el-input>
          </div>
          <div class="plot-arrow"><el-icon>
              <ArrowDown />
            </el-icon></div>
          <div class="plot-item">
            <div class="plot-badge">结局</div>
            <div v-if="!editingSections.plotOutline" class="plot-content">{{ storyDesign.plotOutline.resolution }}</div>
            <el-input v-else v-model="tempStoryDesign.plotOutline.resolution" type="textarea" :autosize="{ minRows: 2, maxRows: 12 }"
              placeholder="请输入故事结局" class="edit-textarea"></el-input>
          </div>
        </div>
      </div>

      <!-- 故事元素 -->
      <div class="design-section animation-delay-6" v-if="storyDesign.keyElements">
        <div class="section-header">
          <el-icon>
            <Picture />
          </el-icon>
          <h3 class="section-title">故事元素</h3>

          <div class="section-actions">
            <template v-if="!editingSections.keyElements">
              <el-tooltip content="编辑" placement="top" :effect="'light'">
                <div class="edit-btn" @click="startEditing('keyElements')">
                  <el-icon>
                    <Edit />
                  </el-icon>
                </div>
              </el-tooltip>
            </template>
            <template v-else>
              <el-tooltip content="取消" placement="top" :effect="'light'">
                <div class="cancel-btn" @click="cancelEditing('keyElements')">
                  <el-icon>
                    <Close />
                  </el-icon>
                </div>
              </el-tooltip>
              <el-tooltip content="保存" placement="top" :effect="'light'">
                <div class="save-btn" @click="saveEditing('keyElements')">
                  <el-icon>
                    <CircleCheck />
                  </el-icon>
                </div>
              </el-tooltip>
            </template>
          </div>
        </div>
        <div class="elements-container">
          <div class="element-item">
            <div class="element-label">情感历程</div>
            <div v-if="!editingSections.keyElements" class="element-value">{{ storyDesign.keyElements?.emotionalJourney
            }}
            </div>
            <el-input v-else v-model="tempStoryDesign.keyElements.emotionalJourney" type="textarea" :autosize="{ minRows: 2, maxRows: 12 }"
              placeholder="请输入情感历程" class="edit-textarea"></el-input>
          </div>
          <div class="element-item">
            <div class="element-label">
              章节规划
              <el-button v-if="editingSections.keyElements" type="primary" size="small" circle @click="addUniqueAspect"
                class="add-aspect-btn">
                <el-icon>
                  <Plus />
                </el-icon>
              </el-button>
            </div>
            <div v-if="!editingSections.keyElements" class="element-list">
              <div v-if="storyDesign.keyElements?.uniqueAspects"
                v-for="(aspect, index) in storyDesign.keyElements?.uniqueAspects" :key="index" class="unique-aspect">
                <el-icon>
                  <Check />
                </el-icon>
                <span>{{ aspect }}</span>
              </div>
            </div>
            <div v-else class="element-list editing">
              <div v-for="(aspect, index) in tempStoryDesign.keyElements.uniqueAspects" :key="index"
                class="unique-aspect editing">
                <el-input v-model="tempStoryDesign.keyElements.uniqueAspects[index]" placeholder="请输入章节特点"
                  class="edit-input aspect-input"></el-input>
                <el-button type="danger" size="small" circle @click="removeUniqueAspect(index)"
                  class="remove-aspect-btn">
                  <el-icon>
                    <Close />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 新增元数据部分 -->
      <div class="design-section metadata-section animation-delay-7" v-if="storyDesign.metadata">
        <!-- <div class="section-header">
          <el-icon>
            <Document />
          </el-icon>
          <h3 class="section-title">故事状态</h3>
          
          <div class="section-actions">
            <template v-if="!editingSections.metadata">
              <el-tooltip content="编辑" placement="top" :effect="'light'">
                <div class="edit-btn" @click="startEditing('metadata')">
                  <el-icon><Edit /></el-icon>
                </div>
              </el-tooltip>
            </template>
            <template v-else>
              <el-tooltip content="取消" placement="top" :effect="'light'">
                <div class="cancel-btn" @click="cancelEditing('metadata')">
                  <el-icon><Close /></el-icon>
                </div>
              </el-tooltip>
              <el-tooltip content="保存" placement="top" :effect="'light'">
                <div class="save-btn" @click="saveEditing('metadata')">
                  <el-icon><CircleCheck /></el-icon>
                </div>
              </el-tooltip>
            </template>
          </div>
        </div> -->
        <div class="metadata-container">
          <div class="metadata-item">
            <div class="metadata-label">版本</div>
            <div v-if="!editingSections.metadata" class="metadata-value">{{ storyDesign.metadata?.version }}</div>
            <el-input v-else v-model="tempStoryDesign.metadata.version" placeholder="请输入版本号"
              class="edit-input"></el-input>
          </div>
          <div class="metadata-item">
            <div class="metadata-label">最后更新日期</div>
            <div v-if="!editingSections.metadata" class="metadata-value">{{ storyDesign.metadata?.lastUpdated }}</div>
            <el-input v-else v-model="tempStoryDesign.metadata.lastUpdated" placeholder="请输入更新日期"
              class="edit-input"></el-input>
          </div>
          <div class="metadata-item">
            <div class="metadata-label">状态</div>
            <div v-if="!editingSections.metadata" class="metadata-value">{{ storyDesign.metadata?.status }}</div>
            <el-select v-else v-model="tempStoryDesign.metadata.status" placeholder="请选择状态" class="edit-select">
              <el-option label="草稿" value="draft"></el-option>
              <el-option label="审核中" value="review"></el-option>
              <el-option label="已完成" value="completed"></el-option>
              <el-option label="已发布" value="published"></el-option>
            </el-select>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, reactive, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { ThoughtChain as AXThoughtChain } from 'ant-design-x-vue'
import { Document, Star, User, Connection, Picture, Check, ArrowDown, Edit, Close, CircleCheck, Plus, VideoPause, VideoPlay } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
// 导入VoiceSelector组件
import VoiceSelector from '../selector/VoiceSelector.vue'
// 导入StyleSelector组件
import StyleSelector from '../selector/StyleSelector.vue'
// 导入RatioSelector组件
import RatioSelector from '../selector/RatioSelector.vue'
// 导入API
import { getImageStyleList } from '@/api/auth.js'

const props = defineProps({
  designResult: {
    type: Object,
    default: null
  },
  storyDesign: {
    type: Object,
    default: null
  },
  voices: {
    type: Array,
    default: () => []
  },
  isLoadingVoices: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:storyDesign', 'refresh-voices'])

// 编辑状态管理
const editingSections = reactive({
  storyBasics: false,
  themeAndTone: false,
  characters: false,
  plotOutline: false,
  keyElements: false,
  adaptiveFeatures: false,
  metadata: false
})

// 临时存储编辑数据
const tempStoryDesign = ref(null)

// 添加图像风格数据
const isLoadingStyles = ref(false)
const styles = ref([])
const selectedStyleId = ref(null)

// 获取画面风格列表
const fetchImageStyles = async () => {
  try {
    isLoadingStyles.value = true
    const response = await getImageStyleList()
    if (response.success) {
      // 转换API返回的风格数据为组件需要的格式
      styles.value = response.data.map(style => ({
        id: style.id,
        name: style.styleName,
        category: style.styleCode || '',
        preview: style.styleUrl || '',
        styleCategory: style.styleCategory || '',
        prompt: style.prompt || '' // 保存prompt字段
      }))
      
      // 如果已有设置的风格描述，尝试找到对应的风格ID
      if (tempStoryDesign.value?.themeAndTone?.imageStyle) {
        const matchingStyle = styles.value.find(style => 
          style.prompt === tempStoryDesign.value.themeAndTone.imageStyle)
        if (matchingStyle) {
          selectedStyleId.value = matchingStyle.id
        }
      }
    } else {
      console.error('获取画面风格列表失败:', response.data?.errMessage)
    }
  } catch (error) {
    console.error('获取画面风格列表异常:', error)
  } finally {
    isLoadingStyles.value = false
  }
}

// 选择风格时更新imageStyle
const handleStyleSelect = (styleId) => {
  selectedStyleId.value = styleId
  const selectedStyle = styles.value.find(style => style.id === styleId)
  if (selectedStyle && tempStoryDesign.value) {
    tempStoryDesign.value.themeAndTone.imageStyle = selectedStyle.prompt || ''
  }
}

// 监听storyDesign变化，更新临时数据
watch(() => props.storyDesign, (newVal) => {
  if (newVal) {
    tempStoryDesign.value = JSON.parse(JSON.stringify(newVal))
  }
}, { immediate: true, deep: true })

// 开始编辑某个部分
const startEditing = (section) => {
  // 确保tempStoryDesign已初始化
  if (!tempStoryDesign.value && props.storyDesign) {
    tempStoryDesign.value = JSON.parse(JSON.stringify(props.storyDesign))
  }
  editingSections[section] = true
  
  // 编辑themeAndTone时，需要重置selectedStyleId
  if (section === 'themeAndTone') {
    // 如果已有设置的风格描述，尝试找到对应的风格ID
    if (tempStoryDesign.value?.themeAndTone?.imageStyle) {
      const matchingStyle = styles.value.find(style => 
        style.prompt === tempStoryDesign.value.themeAndTone.imageStyle)
      if (matchingStyle) {
        selectedStyleId.value = matchingStyle.id
      } else {
        selectedStyleId.value = null
      }
    }
  }
}

// 取消编辑
const cancelEditing = (section) => {
  editingSections[section] = false
  // 重置临时数据
  if (props.storyDesign) {
    tempStoryDesign.value = JSON.parse(JSON.stringify(props.storyDesign))
  }
}

// 保存编辑
const saveEditing = (section) => {
  editingSections[section] = false
  // 提交更新
  emit('update:storyDesign', tempStoryDesign.value)
  // ElMessage.success('保存成功')
}

// 根据角色名生成头像背景色
const getCharacterAvatarStyle = (character) => {
  const colors = [
    ['#10b981', '#059669'], // 绿色
    ['#f59e0b', '#d97706'], // 橙色
    ['#8b5cf6', '#7c3aed'],  // 紫色
    ['#06b6d4', '#0891b2'], // 蓝绿色
    ['#ec4899', '#db2777']  // 粉色
  ]

  // 使用角色名的第一个字符的 Unicode 值来选择颜色
  const colorIndex = character.charCodeAt(0) % colors.length
  const [color1, color2] = colors[colorIndex]

  return {
    background: `linear-gradient(135deg, ${color1} 0%, ${color2} 100%)`
  }
}

// 添加/删除配角
const addSupportingCharacter = () => {
  if (!tempStoryDesign.value.characters.supportingCharacters) {
    tempStoryDesign.value.characters.supportingCharacters = []
  }
  tempStoryDesign.value.characters.supportingCharacters.push('新配角')
}

const removeSupportingCharacter = (index) => {
  tempStoryDesign.value.characters.supportingCharacters.splice(index, 1)
}

const insertTextSendMessage = (text) => {
  if (window.insertTextSendMessage && typeof window.insertTextSendMessage === 'function') {
    window.insertTextSendMessage(text);
  }
}

// 添加章节特点
const addUniqueAspect = () => {
  if (!tempStoryDesign.value.keyElements.uniqueAspects) {
    tempStoryDesign.value.keyElements.uniqueAspects = []
  }
  tempStoryDesign.value.keyElements.uniqueAspects.push('新特点')
}

// 删除章节特点
const removeUniqueAspect = (index) => {
  tempStoryDesign.value.keyElements.uniqueAspects.splice(index, 1)
}

// 获取风格名称
const getStyleName = (prompt) => {
  if (!prompt) return '未选择';
  const style = styles.value.find(s => s.prompt === prompt);
  return style ? style.name : '自定义风格';
}

// 获取语音名称
const getVoiceName = (voiceId) => {
  if (!voiceId) return '未选择'
  const voice = props.voices.find(v => v.id == voiceId)
  return voice ? voice.name : voiceId
}

// 当前播放的音频元素和ID
const currentAudio = ref(null)
const currentPlayingId = ref(null)

// 判断是否正在播放特定音色
const isPlaying = (voiceId) => {
  return currentPlayingId.value === voiceId && currentAudio.value && !currentAudio.value.paused
}

// 播放音色示例
const playVoice = (voiceId) => {
  // 如果正在播放当前音色，则暂停
  if (isPlaying(voiceId)) {
    currentAudio.value.pause()
    currentAudio.value = null
    currentPlayingId.value = null
    return
  }
  
  // 如果有其他正在播放的音频，先停止
  if (currentAudio.value) {
    currentAudio.value.pause()
    currentAudio.value = null
    currentPlayingId.value = null
  }
  
  // 查找选中的音色
  const voice = props.voices.find(v => v.id === voiceId)
  
  // 创建新的音频元素并播放
  if (voice && voice.audioUrl) {
    const audioUrl = voice.audioUrl
    const audio = new Audio(audioUrl)
    
    // 设置事件监听
    audio.addEventListener('play', () => {
      currentPlayingId.value = voiceId
    })
    
    audio.addEventListener('ended', () => {
      currentAudio.value = null
      currentPlayingId.value = null
    })
    
    audio.addEventListener('pause', () => {
      if (currentPlayingId.value === voiceId) {
        currentPlayingId.value = null
      }
    })
    
    audio.addEventListener('error', () => {
      console.error('音频播放错误')
      currentAudio.value = null
      currentPlayingId.value = null
    })
    
    // 播放
    audio.play().catch(error => {
      console.error('播放失败:', error)
      currentAudio.value = null
      currentPlayingId.value = null
    })
    
    currentAudio.value = audio
  }
}

// 组件挂载时加载音色列表和风格列表
onMounted(() => {
  fetchImageStyles()
})

// 组件卸载前清理
onBeforeUnmount(() => {
  // 停止正在播放的音频
  if (currentAudio.value) {
    currentAudio.value.pause()
    currentAudio.value = null
    currentPlayingId.value = null
  }
})
</script>

<style scoped>

.story-design-container {
  padding: 6px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  /* max-width: 960px; */
  margin: 0 auto;
}

.thought-content {
  padding: 10px;
  color: #606266;
}

.design-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.design-section {
  background-color: rgba(255, 255, 255, 0.484);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.2);
  transition: all 0.3s;
  animation: section-appear 0.6s ease-out both;
  /* 添加回退样式，确保内容在无动画时也能正确显示 */
  opacity: 1;
}

/* 添加对无动画设置的支持 */
@media (prefers-reduced-motion: reduce) {
  .design-section {
    animation: none;
    opacity: 1;
    transform: none;
  }
}

/* 添加模块出现动画关键帧 */
@keyframes section-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.design-section:hover {
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.15);
  transform: translateY(-2px);
}

.story-design-section {
  background: linear-gradient(135deg, #fff 0%, #9396fb52 100%);
}

body.dark .story-design-section {
  background: linear-gradient(135deg, var(--bg-card) 0%, #9396fb52 100%);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f1f1f1;
}

.section-header {
  color: #6366f1;
  font-size: 22px;
}

.section-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #334155;
}

/* 编辑按钮样式 */
.section-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.edit-btn,
.cancel-btn,
.save-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.edit-btn {
  background-color: #4f46e5;
  color: white;
}

.edit-btn:hover {
  /* background-color: #4f46e5; */
  transform: scale(1.1);
}

.cancel-btn {
  background-color: #f87171;
  color: white;
}

.cancel-btn:hover {
  /* background-color: #ef4444; */
  transform: scale(1.1);
}

.save-btn {
  background-color: #10b981;
  color: white;
}

.save-btn:hover {
  /* background-color: #059669; */
  transform: scale(1.1);
}

/* 编辑输入框样式 */
.edit-input {
  margin-top: 4px;
  width: 100%;
}

.edit-textarea {
  margin-top: 4px;
  width: 100%;
}

.edit-input-number {
  margin-top: 4px;
}

.character-name-input {
  width: 150px;
  margin-right: 8px;
}

.supporting-name-input {
  width: 120px;
}

.aspect-input {
  flex: 1;
}

.add-character-btn,
.remove-character-btn,
.add-aspect-btn,
.remove-aspect-btn {
  margin-left: 8px;
}

.supporting-item {
  position: relative;
}

.supporting-item .remove-character-btn {
  position: absolute;
  right: -16px;
  top: -16px;
  opacity: 0.8;
  transform: scale(0.8);
}

.unique-aspect.editing {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.element-list.editing {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 故事基础信息样式 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: center;
  align-items: center;
  justify-content: center;
}

.info-label {
  font-size: 14px;
  color: #64748b;
}

.info-value {
  font-size: 16px;
  font-weight: 500;
  color: #334155;
}

/* 主题与风格样式 */
.theme-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.theme-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.theme-label {
  font-size: 14px;
  color: #64748b;
  text-align: left;
}

.theme-value {
  font-size: 16px;
  color: #334155;
  padding: 10px 14px;
  background-color: #f8fafc55;
  border-radius: 8px;
  border-left: 1px solid #6366f1;
  text-align: left;
}

/* 角色设定样式 */
.characters-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.character-card {
  padding: 16px;
  border-radius: 10px;
  background-color: #f8fafc;
  border-left: 4px solid;
}

.character-card.protagonist {
  border-left-color: #6366f1;
}

.character-card.antagonist {
  border-left-color: #ef4444;
}

.character-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.character-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.character-title {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
}

.character-name {
  font-size: 16px;
  font-weight: 600;
  color: #334155;
}

.character-role {
  font-size: 12px;
  color: #64748b;
}

.character-desc {
  color: #475569;
  line-height: 1.5;
  text-align: left;
}

/* 新增背景故事样式 */
.character-background-label {
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  margin-top: 16px;
  margin-bottom: 4px;
  text-align: left;
}

.character-background {
  color: #475569;
  line-height: 1.5;
  text-align: left;
  padding: 10px 14px;
  background-color: #f8fafc;
  border-radius: 8px;
  border-left: 1px solid #6366f1;
}

.supporting-characters {
  margin-top: 8px;
}

.supporting-title {
  font-size: 16px;
  font-weight: 600;
  color: #334155;
  margin-bottom: 12px;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 8px;
}

.supporting-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.supporting-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f8fafc;
  border-radius: 8px;
}

.supporting-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 600;
}

.supporting-name {
  color: #334155;
  font-size: 14px;
}

body.dark .supporting-name {
  color: var(--text-secondary);
}

/* 剧情大纲样式 */
.plot-container {
  display: flex;
  flex-direction: column;
  gap: 0px;
  text-align: left;
}

.plot-item {
  width: 100%;
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.plot-badge {
  padding: 4px 10px;
  background-color: #6366f1;
  color: white;
  border-radius: 6px;
  font-size: 14px;
  white-space: nowrap;
}

.plot-content {
  flex: 1;
  padding: 10px 14px;
  background-color: #f8fafc;
  border-radius: 8px;
  color: #475569;
  line-height: 1.5;
  text-align: left;
}

.plot-arrow {
  color: #6366f1;
  font-size: 20px;
  margin-left: 12px;
}

/* 关键元素样式 */
.elements-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metadata-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.metadata-item {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metadata-label {
  font-size: 14px;
  color: #64748b;
  text-align: left;
}

.metadata-value {
  font-size: 16px;
  color: #334155;
  padding: 10px 14px;
  background-color: #f8fafc;
  border-radius: 8px;
  text-align: center;
}

.edit-select {
  width: 100%;
}

.element-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.element-label {
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 8px;
}

.element-value {
  padding: 10px 14px;
  background-color: #f8fafc;
  border-radius: 8px;
  color: #475569;
  line-height: 1.5;
  text-align: left;
}

.element-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.unique-aspect {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 14px;
  background-color: #f8fafc;
  border-radius: 8px;
  color: #475569;
  text-align: left;
}

.unique-aspect .el-icon {
  color: #10b981;
  flex-shrink: 0;
}

/* 图标按钮样式 */
.scene-group-icon-btn {
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  background-color: #8d8ff0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(99, 102, 241, 0.3);
}

.scene-group-icon-btn .el-icon {
  color: white;
}

.el-icon {
  font-size: 16px;
}

.scene-group-icon-btn:hover {
  background-color: #6366f1;
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(99, 102, 241, 0.5);
}

body.dark .scene-group-icon-btn {
  background-color: rgba(99, 102, 241, 0.7);
  box-shadow: 0 2px 6px rgba(99, 102, 241, 0.2);
}

body.dark .scene-group-icon-btn:hover {
  background-color: rgba(99, 102, 241, 0.9);
  box-shadow: 0 3px 8px rgba(99, 102, 241, 0.4);
}

/* Element Plus 组件样式覆盖 */
:deep(.el-input__inner) {
  border-radius: 8px;
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
  resize: none;
}

:deep(.el-input-number) {
  width: 120px;
}

/* 暗黑模式适配 */
body.dark .design-section {
  background-color: #1616187b;
  border-color: var(--border-color);
  box-shadow: 0 0 10px 0 #3a78ff7e;
}

body.dark .section-header {
  border-bottom-color: #2e2e3c;
}

body.dark .section-title {
  color: #e5e7eb;
}

body.dark .info-label,
body.dark .theme-label,
body.dark .element-label,
body.dark .metadata-label {
  color: #94a3b8;
}

body.dark .info-value,
body.dark .character-name,
body.dark .supporting-title,
body.dark .metadata-value {
  color: #e5e7eb;
}

body.dark .theme-value,
body.dark .character-card,
body.dark .supporting-item,
body.dark .plot-content,
body.dark .element-value,
body.dark .unique-aspect,
body.dark .metadata-value {
  color: var(--text-secondary);
  background-color: #1e1e1e25;
}

body.dark .character-role {
  color: #94a3b8;
}

body.dark .character-desc,
body.dark .character-background {
  color: #cbd5e1;
}

body.dark .character-background-label {
  color: #94a3b8;
}

body.dark .character-background {
  background-color: var(--bg-tertiary);
}

body.dark :deep(.el-input__inner),
body.dark :deep(.el-textarea__inner) {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

body.dark :deep(.el-input__inner:focus),
body.dark :deep(.el-textarea__inner:focus) {
  border-color: #6366f1;
}

body.dark :deep(.el-input-number__decrease),
body.dark :deep(.el-input-number__increase) {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border-color: var(--border-color);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .info-item{
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
  }
  .story-design-container {
    padding: 12px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .plot-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .plot-badge {
    margin-bottom: 4px;
  }

  .section-actions {
    flex-wrap: wrap;
  }
  
  /* 移动设备上缩短动画时间和延迟 */
  .design-section {
    animation-duration: 0.4s;
  }
  
  .animation-delay-1 { animation-delay: 0.05s; }
  .animation-delay-2 { animation-delay: 0.1s; }
  .animation-delay-3 { animation-delay: 0.15s; }
  .animation-delay-4 { animation-delay: 0.2s; }
  .animation-delay-5 { animation-delay: 0.25s; }
  .animation-delay-6 { animation-delay: 0.3s; }
  .animation-delay-7 { animation-delay: 0.35s; }
  
  /* 减小动画移动距离 */
  @keyframes section-appear {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

/* 语音选择器样式 */
.voice-selector-container {
  margin-top: 10px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fafafa;
  /* max-height: 300px; */
  overflow: hidden;
  padding: 10px;
}

body.dark .voice-selector-container {
  border-color: var(--border-color);
  background-color: var(--bg-tertiary);
}

.voice-id-note {
  font-size: 12px;
  color: #909399;
  margin-left: 5px;
} 

body.dark .voice-id-note {
  color: var(--text-secondary);
}

/* 比例选择器样式 */
.ratio-selector {
  margin-top: 0px;
}

/* Style Selector Styles */
.style-selector-container {
  margin-top: 10px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background-color: #fafafa;
  max-height: 300px;
  overflow: hidden;
  padding: 10px;
}

body.dark .style-selector-container {
  border-color: var(--border-color);
  background-color: var(--bg-tertiary);
}

.style-prompt-text {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  font-style: italic;
  word-break: break-word;
}

body.dark .style-prompt-text {
  color: var(--text-secondary);
}

/* 添加动画延迟类 */
.animation-delay-1 {
  animation-delay: 0.1s;
}

.animation-delay-2 {
  animation-delay: 0.2s;
}

.animation-delay-3 {
  animation-delay: 0.3s;
}

.animation-delay-4 {
  animation-delay: 0.4s;
}

.animation-delay-5 {
  animation-delay: 0.5s;
}

.animation-delay-6 {
  animation-delay: 0.6s;
}

.animation-delay-7 {
  animation-delay: 0.7s;
}

/* 修改旁白音色显示区域，添加播放按钮 */
.voice-play-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 8px;
}

.voice-play-button:hover {
  background-color: #f0f0f0;
}

body.dark .voice-play-button:hover {
  background-color: #3a3a3a;
}

.wave-animation {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.wave-bar {
  width: 2px;
  height: 10px;
  background-color: #6366f1;
  margin: 0 1px;
  animation: wave 1s infinite;
}

@keyframes wave {
  0% {
    height: 10px;
  }
  50% {
    height: 15px;
  }
  100% {
    height: 10px;
  }
}

body.dark .wave-bar {
  background-color: #e5e7eb;
}
</style>