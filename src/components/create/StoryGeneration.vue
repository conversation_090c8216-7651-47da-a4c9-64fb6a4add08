<template>
  <div class="result-content" v-if="scriptResult">
    <AXThoughtChain v-if="scriptResult.status === 'in-progress'" :loading="true" :thinking="true">
      <div class="thought-content">正在生成故事，请稍候...</div>
    </AXThoughtChain>
    <div class="story-section" ref="storyContainerRef" v-if="shotStory && shotStory.chapters && shotStory.chapters.length > 0">
      <!-- 故事场景列表 -->
      <div class="story-cards">
        <!-- 添加骨架屏加载效果，在列数计算完成前显示 -->
        <div v-if="!isColumnReady" class="skeleton-container">
          <div v-for="i in 3" :key="i" class="skeleton-card">
            <div class="skeleton-header"></div>
            <div class="skeleton-content"></div>
          </div>
        </div>
        
        <!-- 真实内容，仅在列数计算完成后显示 -->
        <div v-else v-for="(chapters, index) in shotStory.chapters" :key="chapters.chapterID" class="story-card">
          <!-- 故事概述部分 -->
          <div class="story-overview" @click="toggleChapter(chapters.chapterID)">
            <div class="overview-header">
              <div class="overview-title">
                <!-- <el-icon>
                  <Document />
                </el-icon> -->
                <div class="character-avatar" @click.stop="insertTextAtCursor('章节chapterID:' + chapters.chapterID)">
                  章节 {{ chapters.chapterID }}
                </div>
                <span v-if="!editingState.chapters[chapters.chapterID]">{{ chapters.chapterTitle }}</span>
                <el-input v-else v-model="getChapterById(chapters.chapterID).chapterTitle" placeholder="请输入章节标题"
                  class="edit-input chapter-title-input" @click.stop></el-input>

                <template v-if="!editingState.chapters[chapters.chapterID]">
                  <el-tooltip content="编辑" placement="top" :effect="'light'">
                    <div class="edit-btn" @click.stop="startEditingChapter(chapters.chapterID)">
                      <el-icon>
                        <Edit />
                      </el-icon>
                    </div>
                  </el-tooltip>
                </template>
                <template v-else>
                  <el-tooltip content="取消" placement="top" :effect="'light'">
                    <div class="cancel-btn" @click.stop="cancelEditingChapter(chapters.chapterID)">
                      <el-icon>
                        <Close />
                      </el-icon>
                    </div>
                  </el-tooltip>
                  <el-tooltip content="保存" placement="top" :effect="'light'">
                    <div class="save-btn" @click.stop="saveEditingChapter(chapters.chapterID)">
                      <el-icon>
                        <CircleCheck />
                      </el-icon>
                    </div>
                  </el-tooltip>
                </template>

                <div class="collapse-left">
                  <div class="scene-shots-count">共 {{ (chapters.scenes || []).length }} 个场景</div>
                  <el-icon class="collapse-icon" :class="{ 'is-active': !collapsedChapters[chapters.chapterID] }">
                    <ArrowDown />
                  </el-icon>
                </div>

                <div class="section-actions">
                  <el-tooltip content="生成章节分镜" placement="top" :effect="'light'">
                    <div class="scene-group-icon-btn"
                      @click.stop="insertTextSendMessage('调用工具生成章节【chapterID:' + chapters.chapterID + '】的分镜')">
                      生成分镜
                    </div>
                  </el-tooltip>


                </div>
              </div>
            </div>
          </div>

          <transition name="chapter-toggle">
            <div class="chapter-content" v-show="!collapsedChapters[chapters.chapterID]">
              <!-- 瀑布流布局 -->
              <div class="scenes-waterfall">
                <div class="waterfall-column" v-for="(column, colIndex) in sceneColumns[chapters.chapterID] || []" :key="colIndex">
                  <!-- 场景循环 -->
                  <div v-for="scene in column" :key="scene.id" class="story-card-item">
                    <div class="story-header">
                      <div class="story-info">
                        <div class="story-title-section">
                          <div class="story-badge" @click="insertTextAtCursor('故事场景id:' + scene.id)">场景 {{ scene.id }}</div>
                        </div>
                        <div class="story-actions">
                          <template v-if="!editingState.scenes[scene.id]">
                            <el-tooltip content="编辑" placement="top" :effect="'light'">
                              <div class="edit-btn" @click.stop="startEditingScene(scene.id)">
                                <el-icon>
                                  <Edit />
                                </el-icon>
                              </div>
                            </el-tooltip>
                          </template>
                          <template v-else>
                            <el-tooltip content="取消" placement="top" :effect="'light'">
                              <div class="cancel-btn" @click.stop="cancelEditingScene(scene.id)">
                                <el-icon>
                                  <Close />
                                </el-icon>
                              </div>
                            </el-tooltip>
                            <el-tooltip content="保存" placement="top" :effect="'light'">
                              <div class="save-btn" @click.stop="saveEditingScene(scene.id)">
                                <el-icon>
                                  <CircleCheck />
                                </el-icon>
                              </div>
                            </el-tooltip>
                          </template>
                        </div>
                      </div>
                      <div class="story-meta">
                        <div class="meta-item">
                          <el-icon>
                            <Clock />
                          </el-icon>
                          <span>预计时长：2-3分钟</span>
                        </div>
                        <div class="meta-item">
                          <el-icon>
                            <ChatDotRound />
                          </el-icon>
                          <span>对话数量：{{ scene.shots && scene.shots.length || 0 }}</span>
                        </div>
                      </div>
                      <div v-if="!editingState.scenes[scene.id]" class="story-description">
                        {{ scene.disc || '暂无描述' }}
                      </div>
                      <el-input v-else v-model="getSceneById(scene.id).disc" type="textarea" :autosize="{ minRows: 2, maxRows: 12 }" placeholder="请输入场景描述"
                        class="edit-textarea" @click.stop></el-input>
                    </div>
                    
                    <!-- 镜头列表区域 -->
                    <div class="dialogue-section" v-if="scene.shots && scene.shots.length > 0 && scene.shots.some(s => s.shotsProgression || s.summary || s.narration)">
                      <div class="dialogue-header">
                        <el-icon>
                          <Film />
                        </el-icon>
                        <span>分镜</span>
                      </div>
                      <div class="dialogue-list">
                        <div v-for="(shot, shotIndex) in scene.shots" :key="`${scene.id}_${shotIndex}`" 
                          class="dialogue-item" :class="{ 'alternate': shotIndex % 2 === 1 }">
                          <div class="dialogue-character" v-if="shot.shotsProgression || shot.summary || shot.narration">
                            <div class="shot-badge" @click="insertTextAtCursor('镜头shot:' + shot.shot)">
                              {{ shot.shot }}
                            </div>
                            <div class="dialogue-content">

                              <!-- 镜头进展 -->
                              <!-- <div class="shot-field-label">对白：</div> -->
                              <!-- <div v-if="!editingState.shots[`${scene.id}_${shotIndex}`]" class="dialogue-text">
                                <span class="shot-field-label">【规划】</span>{{ shot.shotsProgression || '暂无规划描述' }}
                              </div>
                              <el-input 
                                v-else 
                                v-model="getShotByIndex(scene.id, shotIndex).shotsProgression" 
                                type="textarea" 
                                :autosize="{ minRows: 1, maxRows: 12 }" 
                                placeholder="请输入规划描述"
                                class="edit-textarea" 
                                @click.stop
                              ></el-input> -->

                              <!-- 镜头旁白 -->
                              <!-- <div class="shot-field-label">旁白：</div> -->
                              <div v-if="!editingState.shots[`${scene.id}_${shotIndex}`]" class="dialogue-text">
                                <span class="shot-field-label">【旁白】</span>{{ shot.narration || '-' }}
                              </div>
                              <el-input 
                                v-else 
                                v-model="getShotByIndex(scene.id, shotIndex).narration" 
                                type="textarea" 
                                :autosize="{ minRows: 1, maxRows: 12 }" 
                                placeholder="请输入旁白描述"
                                class="edit-textarea" 
                                @click.stop
                              ></el-input>

                              <!-- 镜头摘要 -->
                              <!-- <div class="shot-field-label">镜头：</div> -->
                              <!-- <div v-if="!editingState.shots[`${scene.id}_${shotIndex}`]" class="dialogue-text">
                                <span class="shot-field-label">【镜头】</span>{{ shot.summary || '暂无特写描述' }}
                              </div>
                              <el-input 
                                v-else 
                                v-model="getShotByIndex(scene.id, shotIndex).summary" 
                                type="textarea" 
                                :rows="3" 
                                placeholder="请输入镜头描述"
                                class="edit-textarea" 
                                @click.stop
                              ></el-input> -->
                              
                            </div>
                            <div class="shot-actions">
                              <template v-if="!editingState.shots[`${scene.id}_${shotIndex}`]">
                                <el-tooltip content="编辑" placement="top" :effect="'light'">
                                  <div class="edit-btn small" @click.stop="startEditingShot(scene.id, shotIndex)">
                                    <el-icon>
                                      <Edit />
                                    </el-icon>
                                  </div>
                                </el-tooltip>
                              </template>
                              <template v-else>
                                <el-tooltip content="保存" placement="top" :effect="'light'">
                                  <div class="save-btn small" @click.stop="saveEditingShot(scene.id, shotIndex)">
                                    <el-icon>
                                      <CircleCheck />
                                    </el-icon>
                                  </div>
                                </el-tooltip>
                                <el-tooltip content="取消" placement="top" :effect="'light'">
                                  <div class="cancel-btn small" @click.stop="cancelEditingShot(scene.id, shotIndex)">
                                    <el-icon>
                                      <Close />
                                    </el-icon>
                                  </div>
                                </el-tooltip>
                              </template>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </transition>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ThoughtChain as AXThoughtChain } from 'ant-design-x-vue'
import { Edit, Delete, ChatLineRound, Connection, Document, Clock, ChatDotRound, Plus, ArrowDown, Film, Close, CircleCheck } from '@element-plus/icons-vue'
import { ref, reactive, onMounted, watch, nextTick, defineEmits, computed, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  scriptResult: {
    type: Object,
    default: null
  },
  shotStory: {
    type: Object,
    default: () => (null)
  },
  columnCount: {
    type: Number,
    default: 1 // 默认列数，作为初始值和最小值
  }
})

const emit = defineEmits(['update:shotStory'])

// 瀑布流布局相关变量
const storyContainerRef = ref(null) // 容器引用
const localColumnCount = ref(props.columnCount) // 动态计算的列数
const idealCardWidth = 700 // 理想的卡片宽度（像素）
const containerWidth = ref(0) // 容器宽度
// 标记列数是否已准备好，避免闪烁
const isColumnReady = ref(false)

// 根据窗口宽度更新列数
const updateColumnCount = () => {
  // 获取容器宽度
  if (storyContainerRef.value) {
    containerWidth.value = storyContainerRef.value.clientWidth
  } else {
    // 如果容器还未渲染，使用窗口宽度减去一些边距作为估计值
    containerWidth.value = window.innerWidth - 40 // 减去可能的边距
  }
  
  // 在移动设备上强制单列 (小于768px)
  if (containerWidth.value <= 768) {
    localColumnCount.value = 1
  } else {
    // 计算最佳列数（容器宽度除以理想卡片宽度，向下取整）
    let calculatedColumns = Math.floor(containerWidth.value / idealCardWidth)
    
    // 确保列数在合理范围内，至少为 props.columnCount，最多为 4
    calculatedColumns = Math.max(props.columnCount, Math.min(calculatedColumns, 4))
    
    // 更新列数
    localColumnCount.value = calculatedColumns
  }
  
  console.log('故事容器宽度:', containerWidth.value, '列数:', localColumnCount.value)
  
  // 标记列数已准备好
  isColumnReady.value = true
}

// 防抖函数
const debounce = (fn, delay) => {
  let timer = null
  return function() {
    const context = this
    const args = arguments
    clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(context, args)
    }, delay)
  }
}

// 防抖处理的列数更新函数
const debouncedUpdateColumnCount = debounce(updateColumnCount, 200)

// 存储 ResizeObserver 实例的引用
let resizeObserverInstance = null

// 计算瀑布流列数据
const sceneColumns = computed(() => {
  const result = {}
  
  if (props.shotStory?.chapters) {
    props.shotStory.chapters.forEach(chapter => {
      if (!chapter.scenes || !chapter.scenes.length) {
        result[chapter.chapterID] = []
        return
      }
      
      const columns = Array(localColumnCount.value).fill().map(() => [])
      
      chapter.scenes.forEach((scene, index) => {
        const columnIndex = index % localColumnCount.value
        columns[columnIndex].push(scene)
      })
      
      result[chapter.chapterID] = columns
    })
  }
  
  return result
})

// 控制章节折叠状态
const collapsedChapters = reactive({})

// 编辑状态管理
const editingState = reactive({
  chapters: {}, // 格式: { chapterID: boolean }
  scenes: {}, // 格式: { sceneId: boolean }
  shots: {} // 格式: { sceneId_shotIndex: boolean }
})

// 标记是否是通过编辑保存触发的数据更新
const isEditSave = ref(false)

// 临时存储编辑数据
const tempShotStory = ref(null)

// 监听shotStory变化，更新临时数据
watch(() => props.shotStory, (newVal) => {
  if (newVal) {
    tempShotStory.value = JSON.parse(JSON.stringify(newVal))
  }
}, { immediate: true, deep: true })

// 监听scriptResult变化，当组件被激活时（有scriptResult）重新计算列数
watch(() => props.scriptResult, (newVal) => {
  if (newVal) {
    // 重置列数准备状态
    isColumnReady.value = false
    
    // 在下一个渲染周期后立即计算列数
    nextTick(() => {
      updateColumnCount()
    })
  }
}, { immediate: true })

// 开始编辑章节
const startEditingChapter = (chapterId) => {
  // 确保tempShotStory已初始化
  if (!tempShotStory.value && props.shotStory) {
    tempShotStory.value = JSON.parse(JSON.stringify(props.shotStory))
  }
  editingState.chapters[chapterId] = true
  // 确保章节展开
  collapsedChapters[chapterId] = false
}

// 取消编辑章节
const cancelEditingChapter = (chapterId) => {
  editingState.chapters[chapterId] = false
  // 重置临时数据
  if (props.shotStory) {
    tempShotStory.value = JSON.parse(JSON.stringify(props.shotStory))
  }
}

// 保存编辑章节
const saveEditingChapter = (chapterId) => {
  editingState.chapters[chapterId] = false
  // 设置标志，表示这是编辑保存操作
  isEditSave.value = true
  // 提交更新
  emit('update:shotStory', tempShotStory.value)
  // ElMessage.success('章节编辑保存成功')
}

// 开始编辑场景
const startEditingScene = (sceneId) => {
  // 确保tempShotStory已初始化
  if (!tempShotStory.value && props.shotStory) {
    tempShotStory.value = JSON.parse(JSON.stringify(props.shotStory))
  }
  editingState.scenes[sceneId] = true
}

// 取消编辑场景
const cancelEditingScene = (sceneId) => {
  editingState.scenes[sceneId] = false
  // 重置临时数据
  if (props.shotStory) {
    tempShotStory.value = JSON.parse(JSON.stringify(props.shotStory))
  }
}

// 保存编辑场景
const saveEditingScene = (sceneId) => {
  editingState.scenes[sceneId] = false
  // 设置标志，表示这是编辑保存操作
  isEditSave.value = true
  // 提交更新
  emit('update:shotStory', tempShotStory.value)
  // ElMessage.success('场景编辑保存成功')
}

// 开始编辑镜头
const startEditingShot = (sceneId, shotIndex) => {
  const shotKey = `${sceneId}_${shotIndex}`
  // 确保tempShotStory已初始化
  if (!tempShotStory.value && props.shotStory) {
    tempShotStory.value = JSON.parse(JSON.stringify(props.shotStory))
  }
  editingState.shots[shotKey] = true
}

// 取消编辑镜头
const cancelEditingShot = (sceneId, shotIndex) => {
  const shotKey = `${sceneId}_${shotIndex}`
  editingState.shots[shotKey] = false
  // 重置临时数据
  if (props.shotStory) {
    tempShotStory.value = JSON.parse(JSON.stringify(props.shotStory))
  }
}

// 保存编辑镜头
const saveEditingShot = (sceneId, shotIndex) => {
  const shotKey = `${sceneId}_${shotIndex}`
  editingState.shots[shotKey] = false
  // 设置标志，表示这是编辑保存操作
  isEditSave.value = true
  // 提交更新
  emit('update:shotStory', tempShotStory.value)
  // ElMessage.success('镜头编辑保存成功')
}

// 获取章节对象
const getChapterById = (chapterId) => {
  if (!tempShotStory.value || !tempShotStory.value.chapters) return null
  return tempShotStory.value.chapters.find(chapter => chapter.chapterID === chapterId)
}

// 获取场景对象
const getSceneById = (sceneId) => {
  if (!tempShotStory.value || !tempShotStory.value.chapters) return null

  for (const chapter of tempShotStory.value.chapters) {
    if (!chapter.scenes) continue
    const scene = chapter.scenes.find(scene => scene.id === sceneId)
    if (scene) return scene
  }

  return null
}

// 获取指定场景中的镜头对象
const getShotByIndex = (sceneId, shotIndex) => {
  const scene = getSceneById(sceneId)
  if (!scene || !scene.shots || !scene.shots[shotIndex]) return null
  return scene.shots[shotIndex]
}

// 初始化所有章节为折叠状态，除第一个章节外
const initializeCollapsedState = () => {
  if (props.shotStory?.chapters && props.shotStory.chapters.length > 1) {
    // 先将所有章节设置为折叠状态
    props.shotStory.chapters.forEach(chapter => {
      if (chapter.chapterID) {
        collapsedChapters[chapter.chapterID] = true
      }
    })

    // 展开第一个章节 - 可以取消注释以自动展开第一个章节
    // const firstChapter = props.shotStory.chapters[0]
    // if (firstChapter && firstChapter.chapterID) {
    //   collapsedChapters[firstChapter.chapterID] = false
    // }
  }
}

// 切换章节折叠状态
const toggleChapter = (chapterId) => {
  // 如果正在编辑，不允许折叠
  if (editingState.chapters[chapterId]) return

  // 获取当前点击章节的状态
  const isCurrentChapterCollapsed = collapsedChapters[chapterId]

  // 如果当前章节已折叠，则展开它并关闭其他所有章节
  if (isCurrentChapterCollapsed) {
    // 先将所有章节设置为折叠状态
    Object.keys(collapsedChapters).forEach(id => {
      collapsedChapters[id] = true
    })
    // 然后展开当前点击的章节
    collapsedChapters[chapterId] = false
  } else {
    // 如果当前章节已展开，则折叠它
    collapsedChapters[chapterId] = true
  }
}

// 监听故事数据变化，当数据变化时初始化折叠状态
watch(() => props.shotStory, () => {
  nextTick(() => {
    // 如果是编辑保存触发的更新，不重新初始化折叠状态
    if (isEditSave.value) {
      // 重置标志
      isEditSave.value = false;
      return;
    }
    initializeCollapsedState()
  })
}, { deep: true })

// 组件挂载后初始化折叠状态
onMounted(() => {
  initializeCollapsedState()
  
  // 初始化瀑布流列数并添加窗口大小变化监听
  nextTick(() => {
    // 立即计算列数，避免延迟
    updateColumnCount()
    
    window.addEventListener('resize', debouncedUpdateColumnCount)
    
    // 使用 ResizeObserver 监测容器大小变化
    if (storyContainerRef.value && 'ResizeObserver' in window) {
      resizeObserverInstance = new ResizeObserver(debouncedUpdateColumnCount)
      resizeObserverInstance.observe(storyContainerRef.value)
    }
  })
})

// 在组件卸载前移除事件监听
onBeforeUnmount(() => {
  // 重置列数准备状态
  isColumnReady.value = false
  
  // 移除窗口大小变化监听
  window.removeEventListener('resize', debouncedUpdateColumnCount)
  
  // 清理 ResizeObserver
  if (resizeObserverInstance) {
    resizeObserverInstance.disconnect()
    resizeObserverInstance = null
  }
})

// 根据角色名生成头像背景色
const getCharacterAvatarStyle = (character) => {
  const colors = [
    ['#6366f1', '#4f46e5'], // 蓝紫色
    ['#10b981', '#059669'], // 绿色
    ['#f59e0b', '#d97706'], // 橙色
    ['#ef4444', '#dc2626'], // 红色
    ['#8b5cf6', '#7c3aed']  // 紫色
  ]

  // 使用角色名的第一个字符的 Unicode 值来选择颜色
  const colorIndex = character.charCodeAt(0) % colors.length
  const [color1, color2] = colors[colorIndex]

  return {
    background: `linear-gradient(135deg, ${color1} 0%, ${color2} 100%)`
  }
}

// 向输入框插入文本的方法
const insertTextAtCursor = (text) => {
  if (window.insertTextToChatInput && typeof window.insertTextToChatInput === 'function') {
    window.insertTextToChatInput(text);
  } else {
    console.log('插入文本功能未定义:', text);
  }
}

const insertTextSendMessage = (text) => {
  if (window.insertTextSendMessage && typeof window.insertTextSendMessage === 'function') {
    window.insertTextSendMessage(text);
  }
}

</script>

<style scoped>
.result-content {
  overflow: auto;
}

.scene-group-icon-btn {
  padding: 4px 8px;
  border-radius: 8px;
  background-color: #8d8ff0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(99, 102, 241, 0.3);
  font-size: 12px;
}

.scene-group-icon-btn:hover {
  background-color: #6366f1;
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(99, 102, 241, 0.5);
}

body.dark .scene-group-icon-btn {
  background-color: rgba(99, 102, 241, 0.7);
  box-shadow: 0 2px 6px rgba(99, 102, 241, 0.2);
}

body.dark .scene-group-icon-btn:hover {
  background-color: rgba(99, 102, 241, 0.9);
  box-shadow: 0 3px 8px rgba(99, 102, 241, 0.4);
}

/* 编辑按钮样式 */
.section-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

.edit-btn,
.cancel-btn,
.save-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  color: white;
}

.edit-btn.small,
.cancel-btn.small,
.save-btn.small {
  width: 24px;
  height: 24px;
  font-size: 14px;
}

.edit-btn {
  background-color: #6366f1;
  color: white;
}

.edit-btn:hover {
  transform: scale(1.1);
}

.cancel-btn {
  background-color: #f87171;
  color: white;
}

.cancel-btn:hover {
  transform: scale(1.1);
}

.save-btn {
  background-color: #10b981;
  color: white;
}

.save-btn:hover {
  transform: scale(1.1);
}

/* 编辑输入框样式 */
.edit-input {
  margin-top: 4px;
  width: 100%;
}

.edit-textarea {
  margin-top: 4px;
  width: 100%;
}

.chapter-title-input {
  width: 200px;
  margin-right: 8px;
}

.dialogue-line-input {
  margin-top: 4px;
  font-style: italic;
  font-size: 12px;
}

.shot-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-left: 8px;
  /* opacity: 0; */
  transition: opacity 0.3s ease; /* 添加过渡效果 */
}

.shot-card:hover .shot-actions,
.shot-item:hover .shot-actions {
  opacity: 1; /* 鼠标悬停时显示 */
}

/* Element Plus 组件样式覆盖 */
:deep(.el-input__inner) {
  border-radius: 8px;
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
  resize: none;
}

body.dark :deep(.el-input__inner),
body.dark :deep(.el-textarea__inner) {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

body.dark :deep(.el-input__inner:focus),
body.dark :deep(.el-textarea__inner:focus) {
  border-color: #6366f1;
}

.story-section {
  padding: 6px;
  overflow: auto;
  overflow-x: hidden;
  gap: 12px;
  display: flex;
  flex-direction: column;
  transition: background-color 0.3s;
}

.story-section::-webkit-scrollbar {
  width: 4px;
}

.story-section::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark .story-section::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

/* 故事概述样式 */
.story-overview {
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 2px 12px #6365f152;
  transition: background-color 0.3s, box-shadow 0.3s;
  /* background: linear-gradient(135deg, #fff 50%, #6365f152 100%); */
  background: linear-gradient(135deg, #fff 0%, #9396fb52 100%);
  cursor: pointer;
}

body.dark .story-overview {
  /* background-color: var(--bg-card); */
  /* background: linear-gradient(135deg, #9396fb52 0%, var(--bg-card) 50%); */
  background: linear-gradient(135deg, var(--bg-card) 0%, #9396fb52 100%);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.overview-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  transition: color 0.3s;
  width: 100%;
}

body.dark .overview-title {
  color: var(--text-primary);
}

.overview-content {
  font-size: 14px;
  line-height: 1.5;
  color: #334155;
  padding: 12px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  backdrop-filter: blur(8px);
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  text-align: left;
  transition: color 0.3s, background 0.3s;
}

body.dark .overview-content {
  color: var(--text-secondary);
}

.story-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.story-card {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.story-card-item {
  border-radius: 12px;
  box-shadow: 0 2px 12px #6365f152;
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: #ffffff79;
  border-left: 1px solid #6366f1;
}

body.dark .story-card-item {
  background-color: #1e1e1e25;
}

.story-card:hover {
  transform: translateY(-2px);
  /* box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12); */
}

/* body.dark .story-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25);
} */

.story-header {
  padding: 16px;
}

.story-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.story-title-section {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.story-badge {
  font-size: 13px;
  font-weight: 600;
  color: #6366f1;
  background-color: rgba(99, 102, 241, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
  transition: color 0.3s, background-color 0.3s;
  cursor: pointer;
}

body.dark .story-badge {
  color: var(--primary-color);
  background-color: rgba(99, 102, 241, 0.15);
}

.story-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  transition: color 0.3s;
}

body.dark .story-title {
  color: var(--text-primary);
}

.story-meta {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #64748b;
  font-size: 13px;
  transition: color 0.3s;
}

body.dark .meta-item {
  color: var(--text-tertiary);
}

.story-actions {
  display: flex;
  gap: 6px;
}

.story-description {
  font-size: 14px;
  line-height: 1.5;
  color: #1e293b;
  padding: 12px;
  background-color: rgba(255, 255, 255, 0.464);
  border-radius: 8px;
  backdrop-filter: blur(8px);
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  text-align: left;
  transition: color 0.3s, background 0.3s;
}

body.dark .story-description {
  color: var(--text-secondary);
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.5), rgba(30, 41, 59, 0.3));
}

.dialogue-section {
  padding: 0 16px 16px 16px;
}

.dialogue-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 12px;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s;
}

body.dark .dialogue-header {
  color: var(--text-tertiary);
}

.dialogue-tools {
  margin-left: auto;
}

.dialogue-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dialogue-item {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 10px;
  border-radius: 8px;
  background-color: #f8fafc55;
  transition: all 0.3s ease;
}

body.dark .dialogue-item {
  background-color: #1e1e1e25;
}

.dialogue-item.alternate {
  background-color: #ffffff77;
  border: 1px solid #e2e8f0;
}
body.dark .dialogue-item.alternate {
  background-color: #1e1e1e25;
  border-color: var(--border-color);
}

.dialogue-character {
  display: flex;
  align-items: flex-start;
  width: 100%;
}

.character-avatar {
  min-width: 32px;
  border-radius: 8px;
  padding: 2px 8px;
  color: #6366f1;
  background-color: rgb(187, 187, 255);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
}

.character-shengchenfen {
  min-width: 32px;
  border-radius: 10px;
  color: white;
  display: flex;
  align-items: center;
  padding: 4px 10px;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  background-color: #8d8ff0;
}

.character-shengchenfen:hover {
  background-color: #6366f1;
  transform: scale(1.05);
}

.character-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.character-name {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  transition: color 0.3s;
  text-align: left;
}

body.dark .character-name {
  color: var(--text-primary);
}

.dialogue-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.dialogue-text {
  font-size: 14px;
  line-height: 1.5;
  color: #334155;
  text-align: left;
  transition: color 0.3s;
  /* margin-bottom: 12px; */
}

body.dark .dialogue-text {
  color: var(--text-secondary);
}

.shot-field-label {
  font-size: 13px;
  font-weight: 600;
  color: #6366f1;
  margin-top: 8px;
  margin-bottom: 4px;
  text-align: left;
}

body.dark .shot-field-label {
  color: var(--primary-color);
}

.dialogue-action {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #64748b;
  font-style: italic;
  transition: color 0.3s;
}

body.dark .dialogue-action {
  color: var(--text-tertiary);
}

.dialogue-action .el-icon {
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .story-section {
    padding: 10px;
  }

  .story-header {
    padding: 12px;
  }

  .dialogue-section {
    padding: 12px;
  }

  .section-actions {
    flex-wrap: wrap;
  }

  .chapter-title-input {
    width: 150px;
  }
  
  /* 移动设备上强制单列显示 */
  .scenes-waterfall {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .scenes-waterfall {
    gap: 8px;
  }
  
  .waterfall-column {
    gap: 8px;
  }
}

/* 动画效果 */
.dialogue-item {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(8px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 思考链样式适配 */
:deep(.ax-thought-chain) {
  transition: background-color 0.3s, border-color 0.3s;
}

body.dark :deep(.ax-thought-chain) {
  background-color: var(--bg-card);
  border-color: var(--border-color);
}

.thought-content {
  transition: color 0.3s;
}

body.dark .thought-content {
  color: var(--text-secondary);
}

.collapse-left {
  margin-left: auto;
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.scene-shots-count {
  font-size: 14px;
  opacity: 0.8;
  font-weight: 400;
}

.collapse-icon {
  margin-left: auto;
  transition: transform 0.3s ease;
  font-size: 16px;
}

.collapse-icon.is-active {
  transform: rotate(180deg);
}

.chapter-content {
  position: relative;
  /* padding-left: 12px;
  margin-left: 6px; */
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 瀑布流布局样式 */
.scenes-waterfall {
  display: flex;
  gap: 10px;
  width: 100%;
}

.waterfall-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 0; /* 确保列可以收缩 */
}

/* .chapter-content::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #6365f1;
  opacity: 0;
  border-radius: 2px;
} */

body.dark .chapter-content::before {
  background-color: var(--primary-color);
  opacity: 0.4;
}

/* 折叠动画 */
.chapter-toggle-enter-active,
.chapter-toggle-leave-active {
  transition: all 0.3s ease;
  max-height: 2000px;
  opacity: 1;
  overflow: hidden;
}

.chapter-toggle-enter-from,
.chapter-toggle-leave-to {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
}

.shot-badge {
  position: absolute;
  top: -2px;
  left: -7px;
  font-size: 10px;
  font-weight: 600;
  min-width: 15px;
  min-height: 15px;
  text-align: center;
  color: #6366f1;
  background-color: rgba(99, 102, 241, 0.1);
  border-radius: 50%;
  display: inline-block;
  transition: color 0.3s, background-color 0.3s;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

body.dark .shot-badge {
  color: var(--primary-color);
  background-color: rgba(99, 102, 241, 0.15);
}

.shot-badge:hover {
  background-color: rgba(99, 102, 241, 0.2);
}

/* 骨架屏样式 */
.skeleton-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 8px 0;
}

.skeleton-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px #6365f152;
  overflow: hidden;
  padding: 16px;
  animation: skeleton-pulse 1.5s infinite;
}

.skeleton-header {
  height: 24px;
  background-color: #f0f0f0;
  border-radius: 4px;
  margin-bottom: 12px;
  width: 70%;
}

.skeleton-content {
  height: 100px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

@keyframes skeleton-pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

body.dark .skeleton-card {
  background-color: var(--bg-card);
}

body.dark .skeleton-header {
  background-color: rgba(60, 60, 60, 0.5);
}

body.dark .skeleton-content {
  background-color: rgba(50, 50, 50, 0.5);
}
</style>