<template>
  <div class="image-preview" :class="previewType" @click="handleImageClick" :style="previewStyle"
    v-loading="showLoading" :element-loading-text="loadingText">
    <!-- 已完成状态显示图片 -->
    <img v-if="imageUrl && !imageError && (props.state === 'COMPLETED' || props.state === 'FAILED')" :src="imageUrl"
      :alt="titleText" @error="handleImageError" />

    <!-- 无图片占位符 - 根据状态显示不同内容 -->
    <div class="no-image-placeholder" v-if="!imageUrl || imageError || props.state === 'PENDING_SUBMISSION'">
      <el-icon>
        <component :is="placeholderIcon" v-if="!imageUrl || imageError" />
        <Plus v-else-if="props.state === 'PENDING_SUBMISSION'" />
      </el-icon>
      <span v-if="!imageUrl || imageError">图片暂未生成</span>
      <span v-else-if="props.state === 'PENDING_SUBMISSION'">{{ stateInfo.text }}</span>
    </div>

    <!-- 提交任务按钮 - 仅在PENDING_SUBMISSION状态显示 -->
    <!-- <div class="submit-task-button" v-if="props.state === 'PENDING_SUBMISSION'" @click.stop="insertTextSendMessage">
      <el-icon><Plus /></el-icon>
      <span>生成图片</span>
    </div> -->

    <!-- 编辑按钮容器 - 仅在COMPLETED或FAILED状态显示  v-if="props.state === 'COMPLETED' || props.state === 'FAILED' || props.state === 'PENDING_SUBMISSION'" -->
    <div class="edit-image-button-container">
      <!-- AI修图按钮 v-if="imageUrl && !imageError" -->
      <div class="edit-image-button" @click.stop="handleEditImage">
        <el-icon>
          <MagicStick />
        </el-icon>
        <span>AI修图</span>
      </div>
      <!-- 重绘按钮 -->
      <!-- <div class="edit-image-button" @click.stop="handleRefreshImage">
        <el-icon>
          <Refresh />
        </el-icon>
        <span>重绘</span>
      </div> -->
    </div>
  </div>
</template>

<script setup>
import { UserFilled, Document, Picture, MagicStick, Refresh, Plus, Check, Warning, Loading } from '@element-plus/icons-vue'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus';
import { submitImageModify, getImageModifyResult } from '@/api/image.js';

const props = defineProps({
  imageUrl: {
    type: String,
    default: ''
  },
  title: {
    type: [String, Object],
    default: ''
  },
  itemId: {
    type: String,
    default: ''
  },
  isClick: {
    type: Boolean,
    default: true
  },
  type: {
    type: String,
    default: 'character', // character, scene, shot
    validator: (value) => ['character', 'scene', 'shot'].includes(value)
  },
  conversationId: {
    type: String,
    default: ''
  },
  customHeight: {
    type: String,
    default: ''
  },
  sceneId: {
    type: String,
    default: ''
  },
  // PENDING_SUBMISSION（无处理-待提交生成任务）（显示任务提交按钮）
  // PENDING （待处理-排队中）（loading-提示排队中）
  // PROCESSING（处理中-生成中）（loading-提示生成中）
  // COMPLETED（已完成）（显示图片和修图重绘按钮）
  // FAILED（失败）（显示修图重绘按钮）
  state: {
    type: String,
    default: 'COMPLETED', // PENDING_SUBMISSION, PENDING, PROCESSING, COMPLETED, FAILED
    validator: (value) => ['PENDING_SUBMISSION', 'PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'].includes(value)
  }
})

const emit = defineEmits(['error', 'edit', 'submit-task'])

// 图片错误状态
const imageError = ref(false)

// 根据类型决定占位图标
const placeholderIcon = computed(() => {
  switch (props.type) {
    case 'character':
      return UserFilled
    case 'scene':
      return Document
    case 'shot':
      return Picture
    default:
      return UserFilled
  }
})

// 根据类型生成 CSS 类名
const previewType = computed(() => `type-${props.type}`)

// 自定义样式
const previewStyle = computed(() => {
  if (props.customHeight) {
    return { height: props.customHeight }
  }
  return {}
})

// 处理title属性，支持字符串和对象格式
const titleText = computed(() => {
  if (typeof props.title === 'string') {
    return props.title
  } else if (typeof props.title === 'object' && props.title !== null) {
    return props.title.title || ''
  }
  return ''
})

// 格式化title信息用于图片预览
const titleInfo = computed(() => {
  if (typeof props.title === 'string') {
    return props.title
  } else if (typeof props.title === 'object' && props.title !== null) {
    return {
      alt: props.title.title || '',
      title: props.title.title || '',
      description: props.title.description || ''
    }
  }
  return ''
})

const insertTextSendMessage = () => {
  // var text = ''
  // switch (props.type) {
  //   case 'character':
  //     text = '生成角色【charID:' + props.itemId+'】图片'
  //   case 'scene':
  //     text = '生成场景【sceneID:' + props.itemId+'】图片'
  //   case 'shot':
  //     text = '生成分镜【shotID:' + props.itemId+'】图片'
  // }
  // if (text && window.insertTextSendMessage && typeof window.insertTextSendMessage === 'function') {
  //   window.insertTextSendMessage(text);
  // }
}

// 根据状态返回对应的信息
const stateInfo = computed(() => {
  switch (props.state) {
    case 'PENDING_SUBMISSION':
      return {
        text: '',
        icon: 'Plus'
      }
    case 'PENDING':
      return {
        text: '排队中',
        icon: 'Loading'
      }
    case 'PROCESSING':
      return {
        text: '生成中',
        icon: 'Loading'
      }
    case 'COMPLETED':
      return {
        text: '已完成',
        icon: 'Check'
      }
    case 'FAILED':
      return {
        text: '生成失败',
        icon: 'Warning'
      }
    default:
      return {
        text: '未知状态',
        icon: 'Question'
      }
  }
})

// 根据状态决定是否显示加载状态
const showLoading = computed(() => {
  return props.state === 'PENDING' || props.state === 'PROCESSING' || loading.value
})

// 根据状态返回加载提示文本
const loadingText = computed(() => {
  if (loading.value) return '处理中...'

  switch (props.state) {
    case 'PENDING':
      return '排队中...'
    case 'PROCESSING':
      return '生成中...'
    default:
      return '加载中...'
  }
})

// 处理图片加载错误
const handleImageError = () => {
  imageError.value = true
  emit('error', props.itemId)
}

// 处理图片点击事件
const handleImageClick = () => {
  if (props.imageUrl && !imageError.value && props.isClick) {
    window.openImagePreview(props.imageUrl, titleInfo.value)
  }
}

// 处理AI修图事件
const handleEditImage = () => {
  // 根据类型传递不同的参数
  if (props.type === 'shot') {
    // 分镜类型需要传递场景ID
    window.openAIImageEditor(props.imageUrl, props.conversationId, props.type, props.itemId, props.sceneId)
  } else {
    // 其他类型正常传参
    window.openAIImageEditor(props.imageUrl, props.conversationId, props.type, props.itemId)
  }
  emit('edit', props.imageUrl)
}
// 处理AI重绘事件
const handleRefreshImage = () => {
  // ElMessage.success('敬请期待.');
  applyEdit()
}

// 应用重绘
const applyEdit = async () => {

  try {
    loading.value = true;

    // 构建带参考图的请求参数
    const requestData = {
      sourceImageUrl: props.imageUrl,
      prompt: ".",
      conversationId: props.conversationId,
      contentType: getContentTypeByMode(), // 根据模式确定内容类型
      primaryId: getPrimaryId(),
      // secondaryId: getSecondaryId(),
      functionType: "redraw" // 重绘
    };

    console.log('提交图片修改请求:', requestData);

    // 提交图片修改请求
    const result = await submitImageModify(requestData);

    if (result && result.modifyCode) {
      currentModifyCode.value = result.modifyCode;
      // 开始轮询查询结果
      startPolling();
    } else {
      throw new Error('提交修改请求失败，未获取到修改码');
    }
  } catch (error) {
    console.error('编辑图片失败:', error);
    ElMessage.error('图片修改失败，请重试');
    loading.value = false;
  }
};


// 用于存储当前修改请求的modifyCode
const currentModifyCode = ref('');
// 轮询间隔(毫秒)
const pollingInterval = ref(5000);
// 轮询计时器
const pollingTimer = ref(null);
// 轮询最大次数
const maxPollingCount = ref(40); // 最多轮询30次，约60秒
// 当前轮询次数
const currentPollingCount = ref(0);
// 加载状态
const loading = ref(false);

// 开始轮询查询修改结果
const startPolling = () => {
  // 重置轮询计数
  currentPollingCount.value = 0;

  // 清除可能存在的之前的计时器
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
  }

  // 设置轮询计时器
  pollingTimer.value = setInterval(async () => {
    try {
      currentPollingCount.value++;
      console.log(`正在查询修改结果，第 ${currentPollingCount.value} 次`);

      // 超过最大轮询次数
      if (currentPollingCount.value > maxPollingCount.value) {
        stopPolling();
        throw new Error('图片生成超时，请重试');
      }

      // 查询修改结果
      const result = await getImageModifyResult(currentModifyCode.value);

      if (result) {
        // 状态：处理状态 (0:处理中, 1:成功, 2:失败)
        if (result.status === 1 && result.modifiedImageUrl) {
          // 生成成功
          stopPolling();

          // 保存新生成图片的修改码映射关系
          if (result.modifiedImageUrl && currentModifyCode.value) {
            // props.imageUrl = result.modifiedImageUrl
            // 发出全局事件，通知相应组件图片已更新
            window.dispatchEvent(new CustomEvent('ai-image-updated', {
              detail: {
                originalUrl: result.sourceImageUrl,
                newUrl: result.modifiedImageUrl
              }
            }));
          }

          ElMessage.success('图片修改成功');
        } else if (result.status === 2) {
          // 生成失败
          stopPolling();
          throw new Error(result.failureReason || '图片修改失败');
        }
        // 状态为0时继续轮询
      }
    } catch (error) {
      stopPolling();
      console.error('查询修改结果失败:', error);
      ElMessage.error(error.message || '图片修改失败，请重试');
      loading.value = false;
    }
  }, pollingInterval.value);
};


// 停止轮询
const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
  }
  // 重置loading状态
  loading.value = false;
};


// 根据模式获取内容类型
const getContentTypeByMode = () => {
  switch (props.type) {
    case 'scene':
      return 2; // 场景
    case 'character':
      return 3; // 角色
    case 'shot':
      return 4; // 分镜
    default:
      return 3; // 默认为角色
  }
};


// 获取主ID
const getPrimaryId = () => {
  // 根据编辑模式获取不同的主ID
  switch (props.type) {
    case 'scene':
      // 场景模式：从场景数据中获取ID
      return props.itemId;
    case 'character':
      // 角色模式：从角色数据中获取ID
      return props.itemId;
    case 'shot':
      // 分镜模式：需要从父组件传递相关数据
      // 这里需要根据实际情况实现
      return props.itemId;
    // return props.sceneId;
  }
  return '';
};

// 获取二级ID (可选)
const getSecondaryId = () => {
  // 根据编辑模式获取不同的主ID
  switch (props.type) {
    case 'shot':
      // 分镜模式：需要从父组件传递相关数据
      // 这里需要根据实际情况实现
      return props.itemId;
  }
  return '';
};

// 处理提交任务
const handleSubmitTask = () => {
  if (props.state === 'PENDING_SUBMISSION') {
    emit('submit-task', props.itemId)
  }
}

</script>

<style scoped>
:deep(.el-loading-mask) {
  z-index: 2;
  background-color: rgba(52, 52, 52, 0.195);
  opacity: 1;
  backdrop-filter: blur(3px);
}


.image-preview {
  position: relative;
  overflow: hidden;
  cursor: zoom-in;
  transition: all 0.3s ease;
  height: 100%;
  /* box-shadow: 0 1px 1px #6365f152; */
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

body.dark .image-preview img {
  filter: brightness(0.9);
}

/* 角色图片预览 */
.type-character {
  height: 100%;
  flex-shrink: 0;
}

/* 场景图片预览 */
.type-scene {
  width: 220px;
  height: 100%;
  border-radius: 0;
  flex-shrink: 0;
}

.type-scene img:hover {
  transform: scale(1.02);
}

/* 分镜图片预览 */
.type-shot {
  /* width: 260px; */
  border-radius: 12px 0 0px 0px;
  flex-shrink: 0;
}

.edit-image-button-container {
  display: flex;
  flex-direction: row;
  gap: 6px;
  position: absolute;
  bottom: 6px;
  right: 6px;
  z-index: 2;
}

/* AI修图按钮样式 */
.edit-image-button {
  background-color: rgb(29, 110, 190);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.408);
  opacity: 0;
  transform: translateY(10px);
}

.image-preview:hover .edit-image-button {
  opacity: 1;
  transform: translateY(0);
}

.edit-image-button:hover {
  background-color: #053c73;
  transform: scale(1.05);
}

/* 提交任务按钮样式 */
.submit-task-button {
  background-color: rgb(29, 110, 190);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 20%);
  z-index: 3;
}

.submit-task-button:hover {
  background-color: #053c73;
  transform: translate(-50%, 20%) scale(1.05);
}

/* 无图占位符样式 */
.no-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8fafc;
  color: #64748b8d;
  font-size: 16px;
  gap: 12px;
}

body.dark .no-image-placeholder {
  background-color: var(--bg-tertiary);
  color: var(--text-tertiary);
}

.no-image-placeholder .el-icon {
  font-size: 36px;
  opacity: 0.6;
}
</style>