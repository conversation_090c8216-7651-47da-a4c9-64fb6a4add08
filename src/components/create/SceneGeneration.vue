<template>
  <div class="result-content" v-if="voiceResult">
    <AXThoughtChain v-if="voiceResult.status === 'in-progress'" :loading="true" :thinking="true">
      <div class="thought-content">正在生成场景，请稍候...</div>
    </AXThoughtChain>
    <div class="scene-design-section">
      <div class="scene-cards">
        <div v-for="scene in shotScenes.scenes" :key="scene.ID" class="scene-card">
          <div class="scene-header">
            <div class="scene-basic">
              <div class="scene-info">
                <h4>{{ scene.name }}</h4>
                <el-tag size="small" type="primary" effect="plain" @click="insertTextAtCursor('场景ID:'+scene.ID)">ID: {{ scene.ID }}</el-tag>
              </div>
            </div>
            <div class="scene-actions">
              <!-- <el-button type="primary" size="small" circle>
                <el-icon>
                  <Edit />
                </el-icon>
              </el-button> -->
              <el-button type="danger" size="small" circle>
                <el-icon>
                  <Delete />
                </el-icon>
              </el-button>
            </div>
          </div>
          <div class="scene-content-wrapper">
            <div class="scene-image-container">
              <!-- 使用封装的ImagePreview组件 -->
              <ImagePreview 
                type="scene"
                :imageUrl="scene.image"
                :title="scene.name"
                :itemId="scene.ID" 
                :conversationId="conversationId"
                @error="handleImageError"
              />
              
              <!-- 本地上传按钮 -->
              <!-- <div class="upload-image-button" @click="openUploadDialog(scene.ID)" v-if="!scene.image || scene.imageError">
                <el-icon><Upload /></el-icon>
                <span>本地上传</span>
              </div> -->
            </div>
            
            <div class="scene-content">
              <div class="scene-section">
                <div class="section-title">
                  <el-icon>
                    <Document />
                  </el-icon>
                  <span>场景描述</span>
                </div>
                <div class="description-content">
                  {{ scene.description }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 图片上传对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传场景图片"
      width="500px"
    >
      <el-upload
        class="image-uploader"
        action="#"
        :auto-upload="false"
        :show-file-list="true"
        :on-change="handleFileChange"
        accept="image/*"
      >
        <div class="upload-trigger">
          <el-icon class="upload-icon"><Plus /></el-icon>
          <div class="upload-text">点击或拖拽图片上传</div>
        </div>
      </el-upload>
      
      <div class="upload-preview" v-if="uploadedImageUrl">
        <img :src="uploadedImageUrl" alt="上传预览" />
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="confirmUpload" 
            :loading="uploadLoading"
            :disabled="!uploadedImageUrl"
          >
            确认上传
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ThoughtChain as AXThoughtChain } from 'ant-design-x-vue'
import { Edit, Delete, Document, Upload, Plus } from '@element-plus/icons-vue'
import { ref, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import ImagePreview from './ImagePreview.vue'
import { uploadToOSS } from '@/api/oss.js'

const props = defineProps({
  voiceResult: {
    type: Object,
    default: null
  },
  shotScenes: {
    type: Object,
    default: null
  },
  conversationId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['add-scene', 'delete-scene', 'update-scene', 'update:isEditing'])

// 上传相关状态
const uploadDialogVisible = ref(false);
const currentSceneId = ref(null);
const uploadedFile = ref(null);
const uploadedImageUrl = ref('');
const uploadLoading = ref(false);

// 处理图片加载失败
const handleImageError = (sceneId) => {
  console.log('处理场景图片加载失败:', sceneId)
  
  // 查找失败的图片所在的场景并标记
  props.shotScenes?.scenes?.forEach(scene => {
    if (scene.ID === sceneId) {
      scene.imageError = true
    }
  })
}

// 打开上传对话框
const openUploadDialog = (sceneId) => {
  currentSceneId.value = sceneId;
  uploadDialogVisible.value = true;
  // 重置上传状态
  uploadedFile.value = null;
  uploadedImageUrl.value = '';
};

// 处理文件变更
const handleFileChange = (file) => {
  const isImage = file.raw.type.indexOf('image/') !== -1;
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!');
    return false;
  }

  uploadedFile.value = file.raw;
  uploadedImageUrl.value = URL.createObjectURL(file.raw);
  return false;
};

// 确认上传
const confirmUpload = async () => {
  if (!uploadedFile.value || !currentSceneId.value) {
    ElMessage.warning('请选择图片');
    return;
  }

  uploadLoading.value = true;
  try {
    // 上传图片到OSS
    const result = await uploadToOSS(uploadedFile.value, 'scene-images');
    if (result && result.url) {
      // 更新场景图片
      updateSceneImage(currentSceneId.value, result.url);
      ElMessage.success('图片上传成功');
      uploadDialogVisible.value = false;
    } else {
      throw new Error('上传失败');
    }
  } catch (error) {
    console.error('上传图片失败:', error);
    ElMessage.error('图片上传失败，请重试');
  } finally {
    uploadLoading.value = false;
  }
};

// 更新场景图片
const updateSceneImage = (sceneId, imageUrl) => {
  // 更新场景图片
  props.shotScenes?.scenes?.forEach(scene => {
    if (scene.ID === sceneId) {
      scene.image = imageUrl;
      scene.imageError = false;
    }
  });

  // 这里可以调用API来更新场景图片
  // 省略API调用实现...
};

// 处理AI修图更新事件
const handleAIImageUpdate = (event) => {
  if (!event.detail) return;
  
  const { originalUrl, newUrl } = event.detail;
  console.log('场景组件收到AI修图更新事件:', originalUrl, '->', newUrl);
  
  // 更新场景图片
  if (props.shotScenes && props.shotScenes.scenes) {
    props.shotScenes.scenes.forEach(scene => {
      if (scene.image === originalUrl) {
        console.log('更新场景图片:', scene.name, scene.ID);
        scene.image = newUrl;
        scene.imageError = false;
      }
    });
  }
};

// 向输入框插入文本的方法
const insertTextAtCursor = (text) => {
  window.insertTextToChatInput(text);
}

// 在组件挂载时添加事件监听
onMounted(() => {
  // 监听AI修图完成事件
  window.addEventListener('ai-image-updated', handleAIImageUpdate);
});

// 在组件卸载前移除事件监听
onBeforeUnmount(() => {
  // 移除AI修图事件监听
  window.removeEventListener('ai-image-updated', handleAIImageUpdate);
});
</script>

<style scoped>
.scene-design-section {
  padding: 12px;
  overflow: scroll;
  overflow-x: hidden;
  transition: background-color 0.3s;
}

.scene-design-section::-webkit-scrollbar {
  width: 6px;
}

.scene-design-section::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 6px;
  transition: background-color 0.3s;
}

body.dark .scene-design-section::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

.scene-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(800px, 1fr));
  gap: 12px;
}

.scene-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px #6365f152;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

body.dark .scene-card {
  background-color: var(--bg-card);
  /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2); */
}

.scene-card:hover {
  transform: translateY(-4px);
  /* box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15); */
}

/* body.dark .scene-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25);
} */

.scene-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8fafc;
  transition: background-color 0.3s, border-color 0.3s;
}

body.dark .scene-header {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

.scene-basic {
  display: flex;
  align-items: center;
  gap: 12px;
}

.scene-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 14px;
}

.scene-info h4 {
  margin: 0;
  font-size: 18px;
  color: #1e293b;
  font-weight: 600;
  transition: color 0.3s;
}

body.dark .scene-info h4 {
  color: var(--text-primary);
}

.scene-actions {
  display: flex;
  gap: 8px;
}

.scene-content-wrapper {
  display: flex;
  flex: 1;
}

:deep(.el-tag) {
  cursor: pointer;
}

.scene-content {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  flex: 1;
  overflow-y: auto;
  transition: background-color 0.3s;
  padding: 12px;
}

body.dark .scene-content {
  background-color: var(--bg-card);
}

.scene-section {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  transition: background-color 0.3s;
  height: 100%;
}

body.dark .scene-section {
  background-color: var(--bg-tertiary);
}

.description-content {
  color: #1e293b;
  line-height: 1.8;
  font-size: 15px;
  margin-top: 8px;
  white-space: pre-line;
  text-align: left;
  transition: color 0.3s;
}

body.dark .description-content {
  color: var(--text-secondary);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s;
}

body.dark .section-title {
  color: var(--text-tertiary);
}

@media (max-width: 1200px) {
  .scene-cards {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .scene-card {
    flex-direction: column;
  }

  .scene-content-wrapper {
    flex-direction: column;
  }

  :deep(.type-scene) {
    width: 100%;
    height: 250px;
  }
}

/* 思考链样式适配 */
:deep(.ax-thought-chain) {
  transition: background-color 0.3s, border-color 0.3s;
}

body.dark :deep(.ax-thought-chain) {
  background-color: var(--bg-card);
  border-color: var(--border-color);
}

.thought-content {
  transition: color 0.3s;
}

body.dark .thought-content {
  color: var(--text-secondary);
}

/* 场景图片容器 */
.scene-image-container {
  position: relative;
  width: 280px;
  flex-shrink: 0;
}

/* 上传按钮样式 */
.upload-image-button {
  position: absolute;
  bottom: 12px;
  right: 12px;
  background-color: #3880ff;
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  z-index: 2;
}

.upload-image-button:hover {
  background-color: #3171e0;
  transform: scale(1.05);
}

/* 上传区域样式 */
.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 10px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  background-color: #fafafa;
  padding: 30px;
  text-align: center;
}

.image-uploader:hover {
  border-color: #6366f1;
  background-color: #f8f8ff;
}

body.dark .image-uploader {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

body.dark .image-uploader:hover {
  border-color: #818cf8;
  background-color: var(--bg-quaternary, #2d3748);
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.upload-icon {
  font-size: 28px;
  color: #8c8c8c;
}

.upload-text {
  color: #8c8c8c;
  font-size: 14px;
}

body.dark .upload-icon,
body.dark .upload-text {
  color: var(--text-tertiary);
}

.upload-preview {
  margin-top: 20px;
  border-radius: 8px;
  overflow: hidden;
  max-height: 300px;
}

.upload-preview img {
  width: 100%;
  object-fit: contain;
}
</style>