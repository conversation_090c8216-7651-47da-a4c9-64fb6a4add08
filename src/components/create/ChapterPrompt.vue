<template>
  <div class="chapter-prompt" v-if="dialogVisible">
    <div class="prompt-backdrop" @click="handleClose"></div>
    <div class="prompt-container">
      <div class="prompt-header">
        <h3>{{ dialogTitle }}</h3>
        <el-icon class="close-icon" @click="handleClose">
          <Close />
        </el-icon>
      </div>

      <div class="prompt-body">
        <div class="prompt-content-wrapper" v-loading="isLoading">
          <div v-if="promptData" class="prompt-content">
            <div class="prompt-header-content">
              <div class="prompt-label">
                <el-icon><EditPen /></el-icon>
                <span>章节提示词</span>
                <el-tooltip content="复制内容" placement="top" :effect="'light'" v-if="promptData">
                  <div class="copy-btn" @click="copyPromptContent(promptData)">
                    <el-icon><CopyDocument /></el-icon>
                  </div>
                </el-tooltip>
              </div>
              <div class="script-content markdown-content" v-html="renderMarkdown(promptData || '暂无提示词')"></div>
            </div>
          </div>
          <div v-else-if="!isLoading" class="empty-prompt">
            <el-icon><InfoFilled /></el-icon>
            <span>暂无章节提示词</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { EditPen, CopyDocument, InfoFilled, Close } from '@element-plus/icons-vue'
import { getSegmentPrompt } from '@/api/auth.js'
import markdownit from 'markdown-it'

// 初始化 markdown-it
const md = markdownit({
  html: true,
  breaks: true,
  linkify: true
})

// 渲染Markdown内容
const renderMarkdown = (content) => {
  if (!content) return ''
  return md.render(content)
}

// 定义props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  segmentId: {
    type: String,
    default: ''
  },
  chapterName: {
    type: String,
    default: ''
  }
})

// 定义emit
const emit = defineEmits(['update:visible'])

// 对话框标题
const dialogTitle = ref('')

// 对话框可见性
const dialogVisible = ref(false)

// 加载状态
const isLoading = ref(false)

// 提示词数据
const promptData = ref('')

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.segmentId) {
    fetchPromptData()
  }
})

// 监听对话框可见性变化
watch(() => dialogVisible.value, (newVal) => {
  emit('update:visible', newVal)
})

// 监听segmentId变化
watch(() => props.segmentId, (newVal) => {
  if (newVal && dialogVisible.value) {
    fetchPromptData()
  }
})

// 监听chapterName变化
watch(() => props.chapterName, (newVal) => {
  updateDialogTitle()
})

// 更新对话框标题
const updateDialogTitle = () => {
  dialogTitle.value = props.chapterName ? `${props.chapterName}` : '章节提示词详情'
}

// 获取提示词数据
const fetchPromptData = async () => {
  if (!props.segmentId) return

  try {
    isLoading.value = true
    updateDialogTitle()
    
    const response = await getSegmentPrompt(props.segmentId)
    if (response && response.success && response.data) {
      promptData.value = response.data.content
    } else {
      promptData.value = ''
      console.error('获取章节提示词失败:', response)
    }
  } catch (error) {
    console.error('获取章节提示词异常:', error)
    ElMessage.error('获取章节提示词失败')
    promptData.value = ''
  } finally {
    isLoading.value = false
  }
}

// 复制提示词内容
const copyPromptContent = (content) => {
  if (!content) return
  
  navigator.clipboard.writeText(content)
    .then(() => {
      ElMessage.success('内容已复制到剪贴板')
    })
    .catch(err => {
      console.error('复制失败:', err)
      ElMessage.error('复制失败，请手动选择并复制')
    })
}

// 关闭弹框
const handleClose = () => {
  dialogVisible.value = false
}

// 组件挂载时更新对话框标题
onMounted(() => {
  updateDialogTitle()
})
</script>

<style scoped>
.chapter-prompt {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.prompt-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 2000;
  cursor: pointer;
}

.prompt-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 1000px;
  max-height: 80vh;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  z-index: 2001;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: fadeIn 0.3s ease;
  transition: background-color 0.3s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -45%);
  }

  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

body.dark .prompt-container {
  background-color: var(--bg-card, #1a202c);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  font-size: 16px;
  font-weight: 600;
  position: relative;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.prompt-header h3 {
  margin: 0;
  color: white;
  font-size: 18px;
}

.close-icon {
  cursor: pointer;
  font-size: 20px;
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.close-icon:hover {
  color: white;
  transform: scale(1.1);
}

.prompt-body {
  flex-grow: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: white;
  transition: background-color 0.3s;
}

body.dark .prompt-body {
  background-color: var(--bg-card, #1a202c);
}

.prompt-content-wrapper {
  min-height: 200px;
  max-height: calc(80vh - 120px);
  overflow-y: auto;
}

.prompt-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.prompt-header-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.prompt-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  color: #1e293b;
  font-weight: 600;
  position: relative;
}

.prompt-label .el-icon {
  font-size: 18px;
  color: #6366f1;
}

body.dark .prompt-label {
  color: #e2e8f0;
}

body.dark .prompt-label .el-icon {
  color: #818cf8;
}

.copy-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #4f46e5;
  color: white;
  margin-left: auto;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.copy-btn:hover {
  transform: scale(1.1);
  background-color: #4338ca;
}

body.dark .copy-btn {
  background-color: #6366f1;
}

body.dark .copy-btn:hover {
  background-color: #818cf8;
}

.script-content {
  font-size: 16px;
  color: #334155;
  padding: 14px;
  background-color: #f8fafc55;
  border-radius: 8px;
  border-left: 1px solid #6366f1;
  text-align: left;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  overflow-y: auto;
  font-family: 'Courier New', Courier, monospace;
  line-height: 1.5;
}

body.dark .script-content {
  color: #e5e7eb;
  background-color: #37415127;
}

.empty-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px;
  color: #64748b;
  font-size: 16px;
}

.empty-prompt .el-icon {
  font-size: 48px;
  color: #cbd5e1;
}

body.dark .empty-prompt {
  color: #94a3b8;
}

body.dark .empty-prompt .el-icon {
  color: #475569;
}

/* Markdown 内容样式 */
:deep(.markdown-content) {
  font-size: 14px;
  color: #334155;
}

:deep(.markdown-content p) {
  margin: 8px 0;
  line-height: 1.6;
}

:deep(.markdown-content h1),
:deep(.markdown-content h2),
:deep(.markdown-content h3),
:deep(.markdown-content h4),
:deep(.markdown-content h5),
:deep(.markdown-content h6) {
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: 600;
  line-height: 1.25;
}

:deep(.markdown-content h1) {
  font-size: 20px;
}

:deep(.markdown-content h2) {
  font-size: 18px;
}

:deep(.markdown-content h3) {
  font-size: 16px;
}

:deep(.markdown-content h4),
:deep(.markdown-content h5),
:deep(.markdown-content h6) {
  font-size: 14px;
}

:deep(.markdown-content ul),
:deep(.markdown-content ol) {
  margin: 8px 0;
  padding-left: 20px;
}

:deep(.markdown-content li) {
  margin: 4px 0;
}

:deep(.markdown-content blockquote) {
  margin: 8px 0;
  padding: 0 16px;
  color: #666;
  border-left: 4px solid #ddd;
}

:deep(.markdown-content pre) {
  margin: 8px 0;
  padding: 12px;
  background-color: #f6f8fa;
  border-radius: 6px;
  overflow-x: auto;
}

:deep(.markdown-content code) {
  font-family: 'Courier New', Courier, monospace;
  background-color: #f6f8fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 90%;
}

:deep(.markdown-content pre code) {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  font-size: 100%;
}

:deep(.markdown-content a) {
  color: #0366d6;
  text-decoration: none;
}

:deep(.markdown-content a:hover) {
  text-decoration: underline;
}

:deep(.markdown-content table) {
  margin: 8px 0;
  border-collapse: collapse;
  width: 100%;
}

:deep(.markdown-content table th),
:deep(.markdown-content table td) {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

:deep(.markdown-content table th) {
  background-color: #f6f8fa;
  font-weight: 600;
}

:deep(.markdown-content table tr:nth-child(2n)) {
  background-color: #f8f8f8;
}

:deep(.markdown-content img) {
  max-width: 100%;
  box-sizing: border-box;
  border-radius: 4px;
}

:deep(.markdown-content hr) {
  height: 1px;
  margin: 16px 0;
  background-color: #e1e4e8;
  border: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .prompt-container {
    width: 95%;
    max-height: 90vh;
    margin: 0;
  }
  
  .prompt-body {
    padding: 16px;
  }
  
  .script-content {
    font-size: 14px;
    padding: 10px;
  }
  
  .prompt-content-wrapper {
    max-height: calc(90vh - 100px);
  }
}
</style> 