<template>
  <div class="chat-input">
    <!-- :sendButtonProps="{
        type: 'primary',
        disabled: !inputValue.trim() || isStreaming
      }"
      :disabled="isStreaming" -->

    <transition name="fade-expand">
      <div v-if="openAttachment" class="attachment-container">
        <!-- 文件上传操作区 -->
        <div class="file-upload-container">
          <div class="upload-area" @click="triggerFileUpload" v-if="!multipleFilesUploading && multipleFiles.length <= 0">
            <el-icon class="upload-icon">
              <Upload />
            </el-icon>
            <div class="upload-text">点击上传文件</div>
            <div class="upload-hint">支持常见文档、图片和视频格式（最大5MB）</div>
          </div>

          <!-- 隐藏的文件输入 -->
          <input ref="fileInputRef" type="file" style="display: none" @change="handleMultipleFilesChange" multiple
            accept="image/*,video/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt" />

          <!-- 上传进度指示器 -->
          <div class="upload-progress" v-if="multipleFilesUploading">
            <el-progress type="circle" :percentage="50" :width="50" />
            <div class="upload-progress-text">正在上传文件...</div>
          </div>

          <!-- 文件列表 -->
          <div class="files-list" v-if="multipleFiles.length > 0">
            <div class="files-list-header">
              <div class="files-list-title">已上传文件 ({{multipleFiles.filter(f => f.status === 'success').length}}/{{
                multipleFiles.length }})</div>
              <el-button type="danger" plain size="small" @click="clearAllFiles" :disabled="multipleFilesUploading">
                清除所有文件
              </el-button>
            </div>

            <!-- 文件项列表 -->
            <div class="files-grid">
              <div v-for="file in multipleFiles" :key="file.id" class="file-item" :class="{
                'is-uploading': file.status === 'uploading',
                'is-success': file.status === 'success',
                'is-error': file.status === 'error'
              }">
                <!-- 文件预览 -->
                <div class="file-item-preview">
                  <!-- 图片预览 -->
                  <img v-if="file.type === FILE_TYPES.IMAGE" :src="file.url" class="file-preview-image" />

                  <!-- 视频预览 -->
                  <video v-else-if="file.type === FILE_TYPES.VIDEO" :src="file.url" class="file-preview-video"
                    controls></video>

                  <!-- 其他文件类型 -->
                  <div v-else class="file-preview-icon">
                    <el-icon>
                      <component :is="getFileIcon(file.type)" />
                    </el-icon>
                  </div>

                  <!-- 上传状态覆盖层 -->
                  <div class="file-status-overlay" v-if="file.status !== 'success'">
                    <el-progress v-if="file.status === 'uploading'" type="circle"
                      :percentage="uploadProgress[file.id] || 0" :width="40" :stroke-width="6" status="success" />
                    <el-icon v-else-if="file.status === 'error'" class="error-icon">
                      <CircleCloseFilled />
                    </el-icon>
                  </div>
                </div>

                <!-- 文件信息 -->
                <div class="file-item-info">
                  <div class="file-item-name">{{ file.file.name }}</div>
                  <div class="file-item-size">{{ (file.file.size / 1024).toFixed(1) }} KB</div>

                  <!-- 文件操作按钮 -->
                  <div class="file-item-actions">
                    <el-button type="danger" size="small" circle @click="removeFile(file.id)"
                      :disabled="multipleFilesUploading">
                      <el-icon>
                        <Delete />
                      </el-icon>
                    </el-button>
                  </div>
                </div>
              </div>

              <!-- 添加更多文件按钮 -->
              <div class="file-item add-more-files" v-if="!multipleFilesUploading && multipleFiles.length < 5"
                @click="triggerFileUpload">
                <div class="add-files-button">
                  <el-icon>
                    <Plus />
                  </el-icon>
                  <span>添加更多文件</span>
                  <span>（最大5MB）</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>

    <div class="chat-input-container">

      <!-- 修改为下拉菜单按钮 -->
      <div class="menu-dropdown" v-if="false">
        <div class="menu-button" @click="toggleMenu">
          <el-icon>
            <MoreFilled />
          </el-icon>
        </div>
        <transition name="fade-slide">
          <div class="menu-content" v-if="showMenu">
            <div class="menu-item" @click="handleAttachment" v-if="false">
              <el-icon><Paperclip /></el-icon>
              <span>添加附件</span>
            </div>
            <div class="menu-item" @click="handleClearContext">
              <el-icon><Delete /></el-icon>
              <span>清空上下文</span>
            </div>
          </div>
        </transition>
      </div>

      <AXSender ref="senderRef" v-model:value="inputValue" placeholder="描述你想创作的内容..." :showSendButton="true"
        sendButtonText="发送" @submit="handleSubmit" @cancel="handleCancel" :loading="isStreaming"
        :autoSize="{ minRows: 1, maxRows: 1 }" :suggestions="suggestions" @suggestion-select="handleSuggestionSelect">
        <!-- <template #footer>
          <div class="input-tips">
            按 Enter 发送，Shift + Enter 换行
            <span v-if="isStreaming" class="streaming-indicator">AI正在思考中...</span>
          </div>
        </template> -->
      </AXSender>

    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted, onBeforeUnmount, computed, h } from 'vue'
import { Sender as AXSender } from 'ant-design-x-vue'
import { Button, Badge, Progress } from 'ant-design-vue';
import { LinkOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { UploadOutlined, PictureOutlined, VideoCameraOutlined, FileOutlined, CloseCircleFilled } from '@ant-design/icons-vue';
import { Delete, Paperclip, MoreFilled, Upload, Plus, CircleCloseFilled } from '@element-plus/icons-vue'
// import { uploadToOSS } from '@/api/oss.js'
import { ElMessage } from 'element-plus'

const props = defineProps({
  isStreaming: {
    type: Boolean,
    default: false
  },
  suggestions: {
    type: Array,
    default: () => []
  },
  conversationId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits([
  'send-message',
  'cancel-message',
  'update:uploadedFiles',
  'clear-context'
])

const inputValue = ref('')
const senderRef = ref(null)
const openAttachment = ref(false);
const items = ref([]);
const showMenu = ref(false);

const fileInputRef = ref(null)
const multipleFiles = ref([])
const multipleFilesUploading = ref(false)
const uploadProgress = ref({})

// 切换菜单显示状态
const toggleMenu = () => {
  showMenu.value = !showMenu.value;
}

// 处理附件功能
const handleAttachment = () => {
  openAttachment.value = !openAttachment.value;
  showMenu.value = false;
}

// 处理清空上下文功能
const handleClearContext = () => {
  ElMessage.info('正在清空上下文...');
  emit('clear-context');
  showMenu.value = false;
}

const FILE_TYPES = {
  IMAGE: 'image',
  VIDEO: 'video',
  OTHER: 'other'
}

const badgeNode = computed(() =>
  h(Badge, { dot: multipleFiles.value.filter(f => f.status === 'success').length > 0 }, {
    default: () => h(Button, {
      onClick: () => openAttachment.value = !openAttachment.value,
      icon: h(LinkOutlined)
    })
  })
);

// 获取文件类型
const getFileType = (file) => {
  if (!file) return FILE_TYPES.OTHER

  const type = file.type || ''

  if (type.startsWith('image/')) {
    return FILE_TYPES.IMAGE
  } else if (type.startsWith('video/')) {
    return FILE_TYPES.VIDEO
  } else {
    return FILE_TYPES.OTHER
  }
}

// 获取文件图标
const getFileIcon = (fileType) => {
  switch (fileType) {
    case FILE_TYPES.IMAGE:
      return PictureOutlined
    case FILE_TYPES.VIDEO:
      return VideoCameraOutlined
    default:
      return FileOutlined
  }
}

// 触发文件上传
const triggerFileUpload = () => {
  if (fileInputRef.value) {
    fileInputRef.value.click();
  }
}

// 处理多文件上传变化
const handleMultipleFilesChange = (event) => {
  const files = event.target.files
  if (!files || files.length === 0) return

  const invalidFiles = []
  const validFiles = []

  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    const isLt5M = file.size / 1024 / 1024 < 5

    if (isLt5M) {
      validFiles.push({
        file,
        id: `file_${Date.now()}_${i}`,
        url: URL.createObjectURL(file),
        type: getFileType(file),
        status: 'pending',
        progress: 0
      })
    } else {
      invalidFiles.push(file.name)
    }
  }

  if (invalidFiles.length > 0) {
    ElMessage.warning(`${invalidFiles.join(', ')} 超过5MB限制，已跳过`)
  }

  if (validFiles.length > 0) {
    multipleFiles.value = [...multipleFiles.value, ...validFiles]
    startUploadToOSS()
  }

  event.target.value = ''
}

// 开始上传到OSS
const startUploadToOSS = async () => {
  if (!props.conversationId) {
    ElMessage.error('会话ID不存在，无法上传文件')
    return
  }

  const pendingFiles = multipleFiles.value.filter(f => f.status === 'pending')
  if (pendingFiles.length === 0) return

  multipleFilesUploading.value = true

  try {
    pendingFiles.forEach(file => {
      file.status = 'uploading'
      uploadProgress.value[file.id] = 0
    })

    // 模拟上传过程，实际项目中应调用后端API
    // 注：实际实现应替换为uploadToOSS调用
    await new Promise(resolve => {
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        pendingFiles.forEach(file => {
          uploadProgress.value[file.id] = progress;
        });

        if (progress >= 100) {
          clearInterval(interval);
          resolve();
        }
      }, 300);
    });

    pendingFiles.forEach(fileObj => {
      fileObj.status = 'success'
      fileObj.ossUrl = fileObj.url // 模拟URL，实际应使用返回的OSS URL
      fileObj.objectName = `mock-object-${Date.now()}` // 模拟objectName
      uploadProgress.value[fileObj.id] = 100
    });

    const successFiles = multipleFiles.value
      .filter(f => f.status === 'success')
      .map(f => ({
        url: f.ossUrl,
        objectName: f.objectName,
        name: f.file.name,
        type: f.type,
        size: f.file.size
      }))

    emit('update:uploadedFiles', successFiles)
    ElMessage.success(`成功上传 ${pendingFiles.length} 个文件`)

  } catch (error) {
    console.error('批量上传文件失败:', error)
    ElMessage.error('批量上传文件失败，请重试')

    pendingFiles.forEach(fileObj => {
      fileObj.status = 'error'
      fileObj.error = '上传失败'
    });
  } finally {
    multipleFilesUploading.value = false
  }
}

// 移除文件
const removeFile = (fileId) => {
  const index = multipleFiles.value.findIndex(f => f.id === fileId)
  if (index !== -1) {
    if (multipleFiles.value[index].url) {
      URL.revokeObjectURL(multipleFiles.value[index].url)
    }

    multipleFiles.value.splice(index, 1)

    const successFiles = multipleFiles.value
      .filter(f => f.status === 'success')
      .map(f => ({
        url: f.ossUrl,
        objectName: f.objectName,
        name: f.file.name,
        type: f.type,
        size: f.file.size
      }))

    emit('update:uploadedFiles', successFiles)
  }
}

// 清除所有文件
const clearAllFiles = () => {
  multipleFiles.value.forEach(f => {
    if (f.url) {
      URL.revokeObjectURL(f.url)
    }
  })

  multipleFiles.value = []
  emit('update:uploadedFiles', [])
}

// 向输入框插入文本的方法
const insertTextAtCursor = (text) => {
  if (!text || !senderRef.value) return;

  // 获取当前输入框的值和光标位置
  const currentValue = inputValue.value || '';
  const textarea = senderRef.value.$el.querySelector('textarea');
  const { selectionStart, selectionEnd } = textarea;

  // 在光标位置插入文本
  const newValue = currentValue.substring(0, selectionStart) + "【" + text + "】" + currentValue.substring(selectionEnd);
  inputValue.value = newValue;

  // 设置新的光标位置
  nextTick(() => {
    const newCursorPosition = inputValue.value.length;
    textarea.focus();
    textarea.setSelectionRange(newCursorPosition, newCursorPosition);
  });
};

// 快捷发送消息
const insertTextSendMessage = (text) => {
  handleSubmit(text)
}

// 处理建议选择
const handleSuggestionSelect = (suggestion) => {
  inputValue.value = suggestion.text
  // 选择建议后自动聚焦到输入框
  nextTick(() => {
    senderRef.value?.focus({ cursor: 'end' })
  })
}

// 处理提交消息
const handleSubmit = (value) => {
  if (!value || !value.trim()) return;

  // 发送消息
  emit('send-message', value);

  // 确保清空输入框
  inputValue.value = '';

  // 发送消息后自动聚焦到输入框
  nextTick(() => {
    if (senderRef.value) {
      senderRef.value.focus();
      // 双重确认输入框被清空
      inputValue.value = '';
    }
  });
};

// 处理取消消息
const handleCancel = () => {
  console.log('取消消息 handleCancel')
  // 发送消息
  emit('cancel-message');
}

// 在组件挂载时添加全局事件监听
onMounted(() => {
  // 注册全局方法用于插入文本
  window.insertTextToChatInput = insertTextAtCursor;
  window.insertTextSendMessage = insertTextSendMessage;
});

// 在组件卸载前移除全局事件监听
onBeforeUnmount(() => {
  // 移除全局方法
  window.insertTextToChatInput = null;
  window.insertTextSendMessage = null;
});
</script>

<style scoped>

/* 动画相关样式 */
/* 下拉菜单的淡入滑出动画 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from,
.fade-slide-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 附件容器的展开动画 */
.fade-expand-enter-active,
.fade-expand-leave-active {
  transition: all 0.4s ease;
  max-height: 1000px;
  opacity: 1;
  overflow: hidden;
}

.fade-expand-enter-from,
.fade-expand-leave-to {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
}

.chat-input-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}

/* 菜单下拉样式 */
.menu-dropdown {
  position: relative;
  margin-right: 10px;
}

.menu-button {
  width: 42px;
  height: 42px;
  /* border: 1px solid #dcdfe6; */
  color: #606266;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.menu-button:hover {
  background-color: #f5f7fa;
  border-color: #c0c4cc;
  color: #409eff;
}

.menu-content {
  position: absolute;
  bottom: 45px;
  left: 0;
  width: 160px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 10;
}

body.dark .menu-content {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}

.menu-item {
  padding: 10px 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
}

.menu-item:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

body.dark .menu-item {
  color: var(--text-secondary);
}

body.dark .menu-item:hover {
  background-color: var(--bg-hover);
  color: var(--primary-color);
}

.menu-item .el-icon {
  font-size: 16px;
}

.open-attachment-button{
  width: 40px;
  height: 30px;
  border: 1px solid #dcdfe6;
  color: #4b4545;
  padding: 5px 10px;
  border-radius: 5px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  margin-right: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.open-attachment-button:hover {
  background-color: #f5f7fa;
  border-color: #2654ae;
}


.attachment-container {
  padding: 0 0 12px 0;
  transition: border-color 0.3s;
}

.chat-input {
  padding: 12px 16px;
  /* border-top: 1px solid #ebeef5; */
  transition: border-color 0.3s;
}

body.dark .chat-input {
  border-color: var(--border-color);
}

.input-tips {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  transition: color 0.3s;
}

body.dark .input-tips {
  color: var(--text-tertiary);
}

.streaming-indicator {
  color: #409eff;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.6;
  }
}

/* 修复 AXSender 样式 */
:deep(.ax-sender-textarea) {
  max-height: 100px;
  overflow-y: auto;
}

:deep(.ax-sender-wrapper) {
  border-radius: 8px;
  border-color: #dcdfe6;
  transition: all 0.3s;
}

body.dark :deep(.ax-sender-wrapper) {
  border-color: var(--border-color);
  background-color: var(--bg-tertiary);
}

body.dark :deep(.ax-sender-wrapper:hover) {
  border-color: var(--border-color-hover);
}

body.dark :deep(.ax-sender-wrapper:focus-within) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

body.dark :deep(.ax-sender-textarea) {
  color: var(--text-primary);
  background-color: transparent;
}

body.dark :deep(.ax-sender-textarea::placeholder) {
  color: var(--text-tertiary);
}

/* 文件上传区域样式 */
.file-upload-container {
  width: 100%;
}

.upload-area {
  height: 140px;
  border: 1px dashed #c0c4cc;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: rgba(64, 158, 255, 0.02);
}

body.dark .upload-area {
  border-color: var(--border-color);
  background-color: rgba(64, 158, 255, 0.05);
}

.upload-area:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}

body.dark .upload-area:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
}

.upload-icon {
  font-size: 28px;
  color: #409eff;
  margin-bottom: 8px;
}

.upload-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 4px;
}

body.dark .upload-text {
  color: var(--text-primary);
}

.upload-hint {
  font-size: 12px;
  color: #909399;
}

body.dark .upload-hint {
  color: var(--text-secondary);
}

/* 上传进度指示器 */
.upload-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  gap: 12px;
}

.upload-progress-text {
  color: #409eff;
  font-size: 14px;
  font-weight: 500;
}

/* 文件列表样式 */
.files-list {
  overflow: hidden;
  transition: border-color 0.3s;
}

.files-list-header {
  padding: 0 0 12px 0px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s, border-color 0.3s;
}

.files-list-title {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  transition: color 0.3s;
}

body.dark .files-list-title {
  color: var(--text-secondary);
}

.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 16px;
}

/* 文件项样式 */
.file-item {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
  background-color: #ffffff;
  position: relative;
}

body.dark .file-item {
  border-color: var(--border-color);
  background-color: var(--bg-tertiary);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.file-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #c0c4cc;
}

body.dark .file-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: var(--border-color-light, #4c4c4c);
}

.file-item.is-uploading {
  border-color: #409eff;
}

.file-item.is-error {
  border-color: #f56c6c;
}

.file-item-preview {
  height: 100px;
  position: relative;
  overflow: hidden;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

body.dark .file-item-preview {
  background-color: var(--bg-quaternary, #2d3748);
}

.file-preview-image,
.file-preview-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-preview-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

body.dark .file-preview-icon {
  background-color: rgba(64, 158, 255, 0.2);
}

.file-preview-icon .ant-icon {
  font-size: 24px;
}

.file-status-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  transition: background-color 0.3s;
}

body.dark .file-status-overlay {
  background-color: rgba(45, 55, 72, 0.8);
}

.error-icon {
  font-size: 40px;
  color: #f56c6c;
}

.file-item-info {
  padding: 6px;
  position: relative;
}

.file-item-name {
  font-size: 12px;
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
  transition: color 0.3s;
}

body.dark .file-item-name {
  color: var(--text-primary);
}

.file-item-size {
  font-size: 11px;
  color: #909399;
  transition: color 0.3s;
}

body.dark .file-item-size {
  color: var(--text-tertiary);
}

.file-item-actions {
  position: absolute;
  top: -18px;
  right: 6px;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.file-item:hover .file-item-actions {
  opacity: 1;
}

/* 添加更多文件按钮 */
.file-item.add-more-files {
  border: 1px dashed #c0c4cc;
  box-shadow: none;
  cursor: pointer;
  background-color: transparent;
}

body.dark .file-item.add-more-files {
  border-color: var(--border-color);
}

.file-item.add-more-files:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
}

body.dark .file-item.add-more-files:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
}

.add-files-button {
  width: 100%;
  height: 100%;
  display: flex;
  box-sizing: border-box;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: #909399;
  transition: color 0.3s;
}

body.dark .add-files-button {
  color: var(--text-tertiary);
}

.add-files-button .ant-icon {
  font-size: 24px;
}

.file-item.add-more-files:hover .add-files-button {
  color: #409eff;
}

body.dark .file-item.add-more-files:hover .add-files-button {
  color: #67a9ff;
}

@media (max-width: 768px) {
  .upload-area {
    height: 100px;
  }

  .upload-icon {
    font-size: 24px;
  }

  .upload-text {
    font-size: 14px;
  }

  .files-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
  }

  .file-item-preview {
    height: 80px;
  }
}

@media (max-width: 480px) {
  .upload-area {
    height: 90px;
  }

  .upload-text {
    font-size: 13px;
  }

  .upload-hint {
    font-size: 11px;
  }

  .files-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
  }

  .file-item-preview {
    height: 70px;
  }

  .add-files-button {
    font-size: 12px;
  }

  .add-files-button .ant-icon {
    font-size: 20px;
  }
}
</style>