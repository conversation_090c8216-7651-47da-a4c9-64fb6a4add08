<template>
  <div class="chat-container">
    <!-- 创作概要区域 -->
    <!-- <div class="creation-summary" v-if="summary">
      <div class="summary-header">
        <el-icon><Document /></el-icon>
        <span>创作概要</span>
      </div>
      <div class="summary-content">
        <div class="summary-item">
          <span class="label">创作风格：</span>
          <span class="value">{{ summary.styleName }}</span>
        </div>
        <div class="summary-item">
          <span class="label">主要角色：</span>
          <span class="value">{{ summary.actorNames.join('、') }}</span>
        </div>
        <div class="summary-item">
          <span class="label">创意描述：</span>
          <span class="value">{{ summary.creativeText }}</span>
        </div>
      </div>
    </div> -->

    <!-- 添加折叠控制区域 -->
    <!-- <div class="chat-controls">
      <div class="control-item" @click="toggleFoldableControls">
        <span class="icon">{{ showFoldableControls ? '▼' : '▶' }}</span>
        <span>折叠控制</span>
      </div>
      <div v-if="showFoldableControls" class="control-options">
        <div class="control-item" @click="togglePreDisplay">
          <span class="icon">{{ showPreElements ? '✓' : '✗' }}</span>
          <span>代码块</span>
        </div>
        <div class="control-item" @click="toggleBlockquoteDisplay">
          <span class="icon">{{ showBlockquoteElements ? '✓' : '✗' }}</span>
          <span>引用内容</span>
        </div>
      </div>
    </div> -->

    <div class="chat-messages" ref="chatContainer">
      <div class="messages-container">
        <!-- 添加历史消息加载中的指示器 -->
        <div v-if="loading" class="history-loading-indicator">
          <div class="loading-spinner"></div>
          <span>正在加载会话...</span>
        </div>

        <!-- 使用单独的MessageItem组件 -->
        <template v-for="(message, index) in transformedMessages" :key="message.id">
          <MessageItem 
            :message="message" 
            :isStreaming="isStreaming" 
            :streamingMessageId="streamingMessageId"
            :showPreElements="showPreElements"
            :showBlockquoteElements="showBlockquoteElements"
            @send-message="handleSubmit"
          />
        </template>

        <!-- 加载指示器 -->
        <div v-if="isTyping || isStreaming" class="typing-indicator">
          <span>AI创作中,请稍等...</span>
          <div class="loader-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <!-- <div class="chat-controls">
      <el-switch
        v-model="showPreElements"
        active-text=""
        inactive-text="工具过程"
        class="pre-toggle-switch"
        @change="togglePreDisplay"
      />
    </div> -->

    <!-- 使用单独的ChatInput组件 -->
    <ChatInput 
      :isStreaming="isStreaming"
      :suggestions="suggestions"
      @send-message="handleSubmit"
      @cancel-message="handleCancel"
      @clear-context="handleClearContext"
    />
  </div>
</template>

<script setup>
import { ref, watchEffect, nextTick, computed, onMounted, watch, onBeforeUnmount } from 'vue'
import { Document } from '@element-plus/icons-vue'
import MessageItem from './MessageItem.vue'
import ChatInput from './ChatInput.vue'

const props = defineProps({
  messages: {
    type: Array,
    required: true
  },
  summary: {
    type: Object,
    default: () => ({
      styleName: '',
      actorNames: [],
      creativeText: ''
    })
  },
  isStreaming: {
    type: Boolean,
    default: false
  },
  streamingMessageId: {
    type: String,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['send-message', 'retry-message', 'cancel-message', 'clear-context'])
const chatContainer = ref(null)
const isTyping = ref(false)
// 新增变量，用于跟踪是否应该自动滚动到底部
const shouldAutoScroll = ref(true)
// 新增变量，用于存储滚动位置信息
const scrollInfo = ref({
  lastScrollTop: 0,
  lastScrollHeight: 0
})
// 新增变量，控制pre标签的显示
const showPreElements = ref(false)
// 新增变量，用于控制blockquote标签的显示
const showBlockquoteElements = ref(false)
// 新增折叠控制区域的显示状态
const showFoldableControls = ref(false)

// 建议的创意提示，使用静态定义避免异步加载问题
const suggestions = [
  { id: '1', text: '一个小兔子在森林里遇到了会魔法的松鼠的故事' },
  { id: '2', text: '讲述未来世界里机器人和人类共同生活的日常' },
  { id: '3', text: '海底世界的探险故事，主角是一只勇敢的小鱼' },
  { id: '4', text: '太空冒险，宇航员在遥远星球发现神秘文明' }
]

// 头像样式
const userAvatarStyle = {
  color: '#1a73e8',
  backgroundColor: '#e8f0fe'
}

const aiAvatarStyle = {
  color: '#34a853',
  backgroundColor: '#e6f4ea'
}

// MutationObserver实例
let observer = null;

// 修改切换pre标签的显示状态函数
const togglePreDisplay = () => {
  showPreElements.value = !showPreElements.value;
}

// 新增切换blockquote标签的显示状态函数
const toggleBlockquoteDisplay = () => {
  showBlockquoteElements.value = !showBlockquoteElements.value;
}

// 新增切换折叠控制区域的显示状态函数
const toggleFoldableControls = () => {
  showFoldableControls.value = !showFoldableControls.value;
}

// 转换消息格式以适配 MessageItem 组件
const transformedMessages = computed(() => {
  return props.messages.map((message) => {
    // 构建基本消息对象
    const transformedMessage = {
      id: message.id || String(Date.now() + Math.random()),
      role: message.type === 'user' ? 'user' : 'assistant',
      content: message.text || '',
      meta: {},
      suggestedQuestions: message.suggestedQuestions || []
    }

    // 添加加载状态
    if (message.status && message.type === 'assistant') {
      if (message.status.progress < 100) {
        transformedMessage.meta.progress = message.status.progress
        transformedMessage.meta.statusText = message.status.text
      } else {
        transformedMessage.meta.progress = 100
        transformedMessage.meta.statusText = message.status.text
      }
    }

    // 添加提示词信息
    if (message.prompt) {
      transformedMessage.meta.prompt = message.prompt
    }

    // 添加usage信息
    if (message.usage) {
      transformedMessage.meta.usage = message.usage
    }

    return transformedMessage
  })
})

// 监听消息变化，自动滚动到底部
watch(() => props.messages, () => {
  try {
    nextTick(() => {
      if (shouldAutoScroll.value) {
        scrollToBottom();
      }
    });
  } catch (error) {
    console.error('Error in messages watch:', error);
  }
}, { deep: true });

// 单独监听消息的usage属性变化
watch(() => props.messages.map(msg => msg.usage), () => {
  // console.log('usage信息更新，重新计算transformed');
}, { deep: true });

// 滚动到底部函数
const scrollToBottom = () => {
  try {
    if (chatContainer.value) {
      chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
      // 更新最后的滚动位置信息
      scrollInfo.value.lastScrollTop = chatContainer.value.scrollTop;
      scrollInfo.value.lastScrollHeight = chatContainer.value.scrollHeight;
    }
  } catch (error) {
    console.error('Error during scrollToBottom:', error);
  }
};

// 在组件挂载时初始化滚动和观察器
onMounted(() => {
  try {
    // 初始滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
    
    // 创建MutationObserver监听消息容器的变化
    observer = new MutationObserver(() => {
      if (shouldAutoScroll.value && props.isStreaming) {
        scrollToBottom();
      }
    });
    
    if (chatContainer.value) {
      observer.observe(chatContainer.value, {
        childList: true,
        subtree: true,
        characterData: true
      });
      
      // 添加滚动事件监听器
      chatContainer.value.addEventListener('scroll', handleScroll);
    }
  } catch (error) {
    console.error('Error during onMounted setup:', error);
  }
});

// 处理滚动事件
const handleScroll = () => {
  if (!chatContainer.value) return;
  
  const { scrollTop, scrollHeight, clientHeight } = chatContainer.value;
  const isAtBottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 10;
  
  // 判断是否是用户滚动（而不是程序触发的滚动）
  const isUserScroll = scrollInfo.value.lastScrollTop !== scrollTop || 
                      scrollInfo.value.lastScrollHeight !== scrollHeight;
  
  if (isUserScroll) {
    // 用户手动滚动，如果滚到底部则启用自动滚动，否则禁用
    shouldAutoScroll.value = isAtBottom;
  }
  
  // 更新最后的滚动位置信息
  scrollInfo.value.lastScrollTop = scrollTop;
  scrollInfo.value.lastScrollHeight = scrollHeight;
};

// 在组件卸载时清理 observer 和事件监听器
onBeforeUnmount(() => {
  if (observer) {
    observer.disconnect();
  }

  // 清除可能存在的定时器
  if (window._scrollInterval) {
    clearInterval(window._scrollInterval);
    window._scrollInterval = null;
  }
  
  // 移除滚动事件监听器
  if (chatContainer.value) {
    chatContainer.value.removeEventListener('scroll', handleScroll);
  }
});

// 修改handleSubmit函数，在发送消息后滚动到底部并启用自动滚动
const handleSubmit = (value) => {
  if (!value || !value.trim()) return;

  // 发送消息时启用自动滚动
  shouldAutoScroll.value = true;
  
  // 发送消息
  emit('send-message', value);

  // 发送消息后滚动到底部
  nextTick(() => {
    // 滚动到底部
    scrollToBottom();
  });
};

// 处理重试
const handleRetry = (messageId) => {
  emit('retry-message', messageId)
}

// 处理清空上下文
const handleClearContext = () => {
  console.log('ChatPanel: 转发清空上下文消息事件')
  emit('clear-context')
  console.log('ChatPanel: clear-context 事件已触发')
}

// 处理取消消息
const handleCancel = () => {
  console.log('ChatPanel: 转发取消消息事件')
  emit('cancel-message')
}
</script>

<style scoped>
.chat-container {
  height: 100%;
  /* background-color: #fff; */
  /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); */
  display: flex;
  flex-direction: column;
  transition: background-color 0.3s, box-shadow 0.3s;
  /* height: calc(100% - 32px);
  margin: 0 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  box-sizing: border-box; */
}

body.dark .chat-container {
  /* background-color: var(--bg-card); */
  /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2); */
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  margin: 0px 0;
  position: relative;
}

.messages-container {
  padding: 16px;
  display: flex;
  flex-direction: column;
  min-height: 200px;
  max-height: 100%;
}

.chat-container ::-webkit-scrollbar {
  width: 4px;
}

.chat-container ::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark .chat-container ::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

/* 创作概要样式 */
.creation-summary {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  margin: 16px;
  border: 1px solid #e2e8f0;
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: #1e293b;
  font-weight: 500;
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-item {
  display: flex;
  gap: 8px;
}

.summary-item .label {
  color: #64748b;
  min-width: 80px;
}

.summary-item .value {
  color: #1e293b;
  flex: 1;
  word-break: break-all;
  text-align: left;
}

/* 打字指示器样式 */
.typing-indicator {
  color: #909399;
  display: flex;
  align-items: center;
  font-size: 14px;
  margin: 10px 0;
  gap: 8px;
}

body.dark .typing-indicator {
  color: var(--text-tertiary);
}

/* 新的加载动画样式 */
.loader-dots {
  display: flex;
  align-items: center;
  gap: 4px;
}

.loader-dots span {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #6366f1;
  animation: loader-dots 1.4s ease-in-out infinite;
}

.loader-dots span:nth-child(1) {
  animation-delay: 0s;
}

.loader-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.loader-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

body.dark .loader-dots span {
  background-color: var(--primary-color);
}

@keyframes loader-dots {
  0%, 100% {
    transform: scale(0.6);
    opacity: 0.4;
  }
  50% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 历史消息加载指示器样式 */
.history-loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  margin: 20px auto;
  color: #909399;
  font-size: 14px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(99, 102, 241, 0.2);
  border-radius: 50%;
  border-top-color: #6366f1;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

body.dark .loading-spinner {
  border-color: rgba(99, 102, 241, 0.1);
  border-top-color: var(--primary-color);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .creation-summary {
    margin: 8px;
    padding: 12px;
  }

  .summary-item {
    flex-direction: column;
    gap: 4px;
  }

  .summary-item .label {
    min-width: auto;
  }
}

/* 添加折叠控制区域样式 */
.chat-controls {
  display: flex;
  padding: 8px 16px;
  gap: 12px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f8f9fa;
  transition: background-color 0.3s, border-color 0.3s;
  align-items: center;
}

body.dark .chat-controls {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

.control-options {
  display: flex;
  gap: 16px;
  align-items: center;
}

.control-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #606266;
  cursor: pointer;
  user-select: none;
  transition: color 0.3s;
}

body.dark .control-item {
  color: var(--text-tertiary);
}

.control-item:hover {
  color: #409eff;
}

body.dark .control-item:hover {
  color: var(--primary-color);
}

.control-item .icon {
  font-size: 14px;
}
</style>