<template>
  <div class="record-component">
    <div class="records-container">
      <!-- 上方预览 -->
      <div class="preview-container" v-if="filteredRecords.length > 0">
        <div v-if="selectedRecord && selectedRecordStoryboards" class="video-preview-wrapper">
          <!-- 添加操作按钮组到右上角 -->
          <div class="preview-actions" @click.stop>
            <!-- 0未初始化 1生成中 2成功 3失败 -->
            <el-button v-if="selectedRecord.videoState != 2 && selectedRecord.videoState != 1" type="warning" size="small" class="preview-btn"
              @click="renderVideo(selectedRecordIndex)">
              生成视频
            </el-button>
            <el-button v-if="selectedRecord.videoState == 1" type="warning" size="small" class="preview-btn">
              视频生成中
            </el-button>
            <el-button v-else-if="selectedRecord.videoState === 2" type="success" size="small" class="preview-btn"
              @click="downloadVideo(selectedRecordIndex)">
              查看视频
            </el-button>
            <el-button v-if="selectedRecord.publishStatus === 0" type="success" size="small"
              @click="publishRecord(selectedRecordIndex)" class="preview-btn">
              发布
            </el-button>
            <el-button v-else type="warning" size="small" @click="unpublishRecord(selectedRecordIndex)" class="preview-btn">
              发布详情
            </el-button>
            <el-button type="danger" size="small" @click="confirmDelete(selectedRecordIndex)" class="preview-btn">
              删除
            </el-button>
          </div>
          
          <VideoPreview :storyboards="selectedRecordStoryboards" :creative-text="selectedRecord.title"
            :style-name="selectedRecord.styleName" :video-progress="videoProgress"
            @update:video-progress="videoProgress = $event" :show-generate-video="true"
            @generate-video="generateVideo"
            :imageSize="imageSize" />
        </div>
        <div v-else class="no-preview">
          <el-empty description="请选择一项记录进行预览" />
        </div>
      </div>

      <!-- 下方记录列表 - 支持左右滚动 -->
      <div class="records-drawer" :class="{ 'collapsed': isCollapsed && isMobile }">
        <!-- 抽屉控制按钮 -->
        <!-- <div class="drawer-control" v-if="isMobile" @click="toggleCollapse">
          <div class="drawer-handle">
            <el-icon class="drawer-icon" :class="{ 'rotate-90': !isCollapsed }">
              <ArrowDown />
            </el-icon>
            <span class="drawer-text">{{ isCollapsed ? '展开记录列表' : '收起记录列表' }}</span>
          </div>
        </div> -->
        
        <div class="drawer-content">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-state">
            <el-skeleton :rows="5" animated />
            <div class="loading-text">加载记录列表...</div>
          </div>

          <!-- 空状态 -->
          <div v-else-if="filteredRecords.length === 0" class="empty-state">
            <el-empty description="暂无生成记录" />
          </div>

          <!-- 记录列表 - 水平滚动 -->
          <div v-else class="records-list">
            <div class="records-scroll-container">
              <div v-for="(record, index) in filteredRecords" :key="index" class="record-item"
                :class="{ 'active': selectedRecordIndex === index }" @click="selectRecord(index)">
                <div class="record-image">
                  <img :src="record.coverImage" :alt="record.title" v-if="record.coverImage"/>
                  <div class="status-badge" :class="{ 'published': record.publishStatus === 1 }"
                    v-if="record.publishStatus === 1">
                    {{ record.publishStatus === 1 ? '已发布' : '未发布' }}
                  </div>
                  <!-- 添加视频生成中的蒙层 -->
                  <div class="video-generating-overlay" v-if="record.videoState === 1">
                    <div class="overlay-content">
                      <span class="generating-text">视频生成中，预计十分钟内完成...</span>
                      <span class="generating-text">生成成功后可下载视频 {{ record.videoProgress }}%</span>
                    </div>
                  </div>

                  <!-- 添加视频生成失败的蒙层 -->
                  <div class="video-generating-overlay" v-else-if="record.videoState === 3">
                    <div class="overlay-content">
                      <span class="generating-text">视频生成失败，请稍后再试...</span>
                    </div>
                  </div>
                </div>
                <div class="record-info">
                  <h3 class="record-title">{{ record.subtitle2 }}</h3>
                  <!-- {{ record.title }} -->
                  <!-- <p class="record-subtitle">.</p> -->
                  <div class="record-footer">
                    <span class="record-date">{{ record.updateTime }}</span>
                  </div>
                  <!-- 仅在非活跃状态下显示操作按钮 -->
                  <div class="record-actions" @click.stop v-if="selectedRecordIndex !== index">
                    <!-- 0未初始化 1生成中 2成功 3失败 -->
                      <el-button v-if="record.videoState != 2 && record.videoState != 1" type="warning" size="small" class="text-btn"
                        @click="renderVideo(index)">
                        生成视频
                      </el-button>
                      <el-button v-else-if="record.videoState === 2" type="success" size="small" class="text-btn"
                        @click="downloadVideo(index)">
                        查看视频
                      </el-button>
                      <el-button v-if="record.publishStatus === 0" type="success" size="small"
                        @click="publishRecord(index)" class="text-btn">
                        发布
                      </el-button>
                      <el-button v-else type="warning" size="small" @click="unpublishRecord(index)" class="text-btn">
                        发布详情
                      </el-button>
                      <el-button type="danger" size="small" @click="confirmDelete(index)" class="text-btn">
                        删除
                      </el-button>
                    </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加删除确认对话框 -->
    <div class="custom-modal" v-if="deleteDialogVisible" @click="deleteDialogVisible = false">
      <!-- 遮罩层 -->
      <div class="modal-overlay"></div>
      
      <!-- 弹窗内容 -->
      <div class="modal-content" @click.stop>
        <!-- 关闭按钮 -->
        <div class="modal-close" @click="deleteDialogVisible = false">
          <el-icon><Close /></el-icon>
        </div>
        
        <!-- 标题 -->
        <h3 class="modal-title">确认删除</h3>
        
        <!-- 内容 -->
        <div class="delete-dialog-content">
          <el-icon class="delete-icon">
            <WarningFilled />
          </el-icon>
          <p>确定要删除该生成记录吗？该操作不可恢复。</p>
        </div>
        
        <!-- 底部按钮 -->
        <div class="modal-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="deleteRecord" :loading="deleteLoading">
            确认删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 添加发布成功提示对话框 -->
    <div class="custom-modal" v-if="publishDialogVisible" @click="publishDialogVisible = false">
      <!-- 遮罩层 -->
      <div class="modal-overlay"></div>
      
      <!-- 弹窗内容 -->
      <div class="modal-content" @click.stop>
        <!-- 关闭按钮 -->
        <div class="modal-close" @click="publishDialogVisible = false">
          <el-icon><Close /></el-icon>
        </div>
        
        <!-- 标题 -->
        <h3 class="modal-title">发布成功</h3>
        
        <!-- 内容 -->
        <div class="publish-dialog-content">
          <QrcodeVue 
              :value="shareLink" 
              :size="200" 
              level="H" 
              render-as="svg"
              class="qrcode-image"
            />
          <!-- <el-icon class="success-icon">
            <CircleCheckFilled />
          </el-icon> -->
          
          <p></p>
          <p>恭喜！您的故事已成功发布。手机扫一扫分享</p>
          <!-- <div class="share-link-container">
            <p class="share-label">分享链接:</p>
            <div class="share-link-box">
              <el-input v-model="shareLink" readonly></el-input>
              <el-button type="primary" @click="copyShareLink">复制</el-button>
            </div>
          </div> -->
        </div>
      </div>
    </div>

    <!-- 添加取消发布确认对话框 -->
    <div class="custom-modal" v-if="unpublishDialogVisible" @click="unpublishDialogVisible = false">
      <!-- 遮罩层 -->
      <div class="modal-overlay"></div>
      
      <!-- 弹窗内容 -->
      <div class="modal-content" @click.stop>
        <!-- 关闭按钮 -->
        <div class="modal-close" @click="unpublishDialogVisible = false">
          <el-icon><Close /></el-icon>
        </div>
        
        <!-- 标题 -->
        <h3 class="modal-title">手机扫一扫分享</h3>
        
        <!-- 内容 -->
        <div class="unpublish-dialog-content">
          <div class="qrcode-container">
            <QrcodeVue 
              :value="shareLink" 
              :size="200" 
              level="H" 
              render-as="svg"
              class="qrcode-image"
            />
          </div>

          <!-- <p></p> -->
          <!-- <div class="share-link-container">
            <p class="share-label">分享链接:</p>
            <div class="share-link-box">
              <el-input v-model="shareLink" readonly></el-input>
              <el-button type="primary" @click="copyShareLink">复制</el-button>
            </div>
          </div> -->
        </div>
        
        <!-- 底部按钮 -->
        <div class="modal-footer">
          <el-button type="warning" @click="confirmUnpublish" :loading="unpublishLoading">
            取消发布
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeMount, onBeforeUnmount, defineProps, defineEmits, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowDown, WarningFilled, CircleCheckFilled, Close } from '@element-plus/icons-vue'
import VideoPreview from '@/components/VideoPreview.vue'
import QrcodeVue from 'qrcode.vue'
import { getVisualList, aiCreationPublish, aiCreationVisualRecord, deleteVisualRecord } from '@/api/auth.js'

const props = defineProps({
  conversationId: {
    type: String,
    default: ''
  },
  activeTab: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update-records-count'])

const route = useRoute()
const router = useRouter()
const loading = ref(true)
const records = ref([]) // 存储生成记录列表
const searchQuery = ref('')
const selectedRecordIndex = ref(null)
const isCollapsed = ref(true) // 默认抽屉收起
const isMobile = ref(false)
const windowWidth = ref(window.innerWidth)
const showVideoCover = ref(true) // 控制视频封面显示
const imageSize = ref('16:9') // 默认比例

// 删除对话框状态
const deleteDialogVisible = ref(false)
const deleteLoading = ref(false)
const recordToDeleteIndex = ref(null)

// 发布对话框状态
const publishDialogVisible = ref(false)
const publishLoading = ref(false)
const shareLink = ref('')

// 取消发布对话框状态
const unpublishDialogVisible = ref(false)
const unpublishLoading = ref(false)
const recordToUnpublishIndex = ref(null)

// 视频进度
const videoProgress = ref(0)

// 生成视频 - 从VideoPreview组件的事件
const generateVideo = () => {
  if (selectedRecord.value) {
    renderVideo(selectedRecordIndex.value)
  }
}

// 监听窗口大小变化
const handleResize = () => {
  windowWidth.value = window.innerWidth
  isMobile.value = windowWidth.value < 1200

  // 在移动到桌面视图时，重置抽屉状态
  if (!isMobile.value) {
    isCollapsed.value = false
  } else {
    // 在移动视图中，默认收起
    isCollapsed.value = true
  }
}

// 切换抽屉的收起/展开状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

// 加载记录
const loadRecords = async () => {
  try {
    loading.value = true

    // 获取会话记录
    const response = await getVisualList(props.conversationId);

    if (response && response.success && response.data) {
      records.value = response.data
      
      // 通知父组件记录数量
      emit('update-records-count', records.value.length)
    } else {
      // 如果没有记录，通知父组件记录数量为0
      emit('update-records-count', 0)
    }

    // 默认选中第一条
    if (records.value.length > 0) {
      selectRecord(0)
    }

    loading.value = false
  } catch (error) {
    console.error('加载记录列表失败:', error)
    ElMessage.error('加载记录列表失败')
    loading.value = false
    
    // 出错时通知父组件记录数量为0
    emit('update-records-count', 0)
  }
}

// 下载视频
const downloadVideo = async (index) => {
  window.open(records.value[index].videoUrl, '_blank')
}

// 根据搜索词过滤记录
const filteredRecords = computed(() => {
  if (!searchQuery.value) return records.value

  const query = searchQuery.value.toLowerCase()
  return records.value.filter(record =>
    record.title.toLowerCase().includes(query) ||
    record.subtitle2.toLowerCase().includes(query)
  )
})

// 选中的记录
const selectedRecord = computed(() => {
  if (selectedRecordIndex.value === null) return null
  return filteredRecords.value[selectedRecordIndex.value]
})

// 选中记录的故事板数据
const selectedRecordStoryboards = computed(() => {
  if (!selectedRecord.value) return null
  return JSON.parse(selectedRecord.value.contentData)
})

// 选择记录
const selectRecord = (index) => {
  selectedRecordIndex.value = index

  // 每次切换记录时，重新显示封面
  showVideoCover.value = true

  // 在移动视图中，选择记录后自动收起抽屉
  if (isMobile.value && !isCollapsed.value) {
    isCollapsed.value = true
  }

  handleSelectRecord(selectedRecord.value)
}

// 处理选中记录的函数
const handleSelectRecord = (record) => {
  selectedRecord.value = record
  try {
    if (record.contentData) {
      selectedRecordStoryboards.value = JSON.parse(record.contentData)
      
      imageSize.value = record.imageSize || '16:9'
    } else {
      selectedRecordStoryboards.value = null
    }
  } catch (error) {
    console.error('解析storyboards数据出错:', error)
    selectedRecordStoryboards.value = null
  }
}

// 确认删除记录
const confirmDelete = (index) => {
  recordToDeleteIndex.value = index
  deleteDialogVisible.value = true
}

// 删除记录
const deleteRecord = async () => {
  if (recordToDeleteIndex.value === null) return

  try {
    deleteLoading.value = true

    const response = await deleteVisualRecord(records.value[recordToDeleteIndex.value].visualRecordCode);
    if (response && response.success) {

      // 如果正在查看该记录，清除选中状态
      if (selectedRecordIndex.value === recordToDeleteIndex.value) {
        selectedRecordIndex.value = null
      }

      // 调整选中索引（因为数组长度变化）
      if (selectedRecordIndex.value !== null && selectedRecordIndex.value > recordToDeleteIndex.value) {
        selectedRecordIndex.value--
      }

      // 从数组中移除该记录
      records.value.splice(recordToDeleteIndex.value, 1)

      // 通知父组件记录数量已更新
      emit('update-records-count', records.value.length)

      ElMessage.success('记录已成功删除')
      recordToDeleteIndex.value = null
    } else {
      ElMessage.error('删除记录失败')
    }
  } catch (error) {
    console.error('删除记录失败:', error)
    ElMessage.error('删除记录失败')
  }
  deleteDialogVisible.value = false
  deleteLoading.value = false
}

// 渲染视频
const renderVideo = async (index) => {
  const response = await aiCreationVisualRecord(records.value[index].visualRecordCode);
  if (response && response.success) {
    records.value[index].videoState = 1
    ElMessage.success('视频渲染中...')
  } else {
    ElMessage.error('视频渲染失败')
  }
}

// 发布记录
const publishRecord = async (index) => {
  try {
    publishLoading.value = true

    // 发布记录
    const response = await aiCreationPublish(true, records.value[index].id);

    if (response && response.success && response.data) {

      // 更新记录的发布状态
      records.value[index].publishStatus = 1
      // 生成分享链接
      shareLink.value = response.data.shareUrl

      publishLoading.value = false

      ElMessage.success('记录已成功发布')
    }
  } catch (error) {
    console.error('发布记录失败:', error)
    ElMessage.error('发布记录失败')
  }
  publishDialogVisible.value = true
  publishLoading.value = false
}

// 复制分享链接
const copyShareLink = () => {
  navigator.clipboard.writeText(shareLink.value)
    .then(() => {
      ElMessage.success('链接已复制到剪贴板')
    })
    .catch(() => {
      ElMessage.error('复制失败，请手动复制')
    })
}

// 显示取消发布确认对话框
const unpublishRecord = (index) => {
  recordToUnpublishIndex.value = index
  shareLink.value = records.value[index].shareUrl
  unpublishDialogVisible.value = true
}

// 确认取消发布
const confirmUnpublish = async () => {
  if (recordToUnpublishIndex.value === null) return

  try {
    unpublishLoading.value = true

    // 取消发布
    const response = await aiCreationPublish(false, records.value[recordToUnpublishIndex.value].id);
    if (response && response.success) {
      // 更新记录的发布状态
      records.value[recordToUnpublishIndex.value].publishStatus = 0

      unpublishDialogVisible.value = false
      unpublishLoading.value = false
      recordToUnpublishIndex.value = null

      ElMessage.success('记录已取消发布')
    } else {
      ElMessage.error('取消发布失败')
    }
  } catch (error) {
    console.error('取消发布失败:', error)
    ElMessage.error('取消发布失败')
  }
  unpublishLoading.value = false
}

// 组件挂载前设置初始状态
onBeforeMount(() => {
  // 初始化窗口大小检测
  handleResize()
})

// 组件挂载时加载数据
onMounted(() => {
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
  
  // 立即执行一次 handleResize 来初始化布局
  handleResize()
  
  // 如果已经有 conversationId，立即加载记录
  if (props.conversationId) {
    loadRecords()
  }
})

// 监听记录数量变化，更新父组件中的计数
watch(() => records.value.length, (newCount) => {
  if (newCount !== undefined) {
    emit('update-records-count', newCount)
  }
}, { immediate: true })

// 组件卸载前清理
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})

// 监视activeTab的变化，当切换到final标签页时自动进入全屏模式
let isFirstLoad = true
watch(() => props.activeTab, (newValue) => {
  console.log('RecordComponent中activeTab变化为:', newValue)
  
  const executeTabActions = () => {
    if (newValue === 'records') {
      // 加载记录数据
      loadRecords()
    }
  }
  
  // 只有第一次加载时延迟执行，避免首次加载时conversationId为null
  if (isFirstLoad) {
    setTimeout(() => {
      executeTabActions()
      isFirstLoad = false
    }, 300)
  } else {
    executeTabActions()
  }
}, { immediate: true })

// 监听 conversationId 变化，重新加载记录
watch(() => props.conversationId, (newId) => {
  if (newId) {
    loadRecords()
  } else {
    // 如果 conversationId 无效，清空记录并通知父组件
    records.value = []
    emit('update-records-count', 0)
  }
}, { immediate: false })

</script>

<style scoped>
.record-component {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.records-container {
  display: flex;
  flex-direction: column;
  /* margin: 0 auto; */
  padding: 0;
  height: 100%;
  position: relative;
}

/* 上方预览部分 */
.preview-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
  border-radius: 16px;
  margin: 10px;
  position: relative;
}

body.dark .preview-container {
  background-color: var(--bg-card);
}

.video-preview-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 16px;
}

/* 预览区域右上角操作按钮 */
.preview-actions {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  z-index: 100;
  transition: all 0.3s ease;
  padding: 0;
  border-radius: 8px;
  max-width: 80%;
  justify-content: flex-end;
}

.preview-btn {
  font-weight: 500 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.25s cubic-bezier(0.215, 0.61, 0.355, 1) !important;
  margin: 0 !important;
  padding: 8px 16px !important;
  font-size: 13px !important;
  border-radius: 6px !important;
  border: none !important;
  backdrop-filter: blur(4px);
  letter-spacing: 0.3px;
  position: relative;
  overflow: hidden;
}

/* 按钮类型的样式优化 */
.preview-btn.el-button--warning {
  background: linear-gradient(135deg, #ff9a3c, #ff7744) !important;
  color: white !important;
  border: none !important;
}

.preview-btn.el-button--success {
  background: linear-gradient(135deg, #42b883, #2f9c6a) !important;
  color: white !important;
  border: none !important;
}

.preview-btn.el-button--danger {
  background: linear-gradient(135deg, #ff5e5e, #ff3a3a) !important;
  color: white !important;
  border: none !important;
}

.preview-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25) !important;
  filter: brightness(1.05);
}

.preview-btn:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15) !important;
  filter: brightness(0.95);
}

/* 移动设备上的按钮样式调整 */
@media (max-width: 768px) {
  .preview-actions {
    padding: 0;
    gap: 8px;
    max-width: 100%;
    flex-wrap: wrap;
    justify-content: center;
    top: auto;
    bottom: 16px;
    left: 16px;
    right: 16px;
  }
  
  .preview-btn {
    font-size: 12px !important;
    padding: 4px 8px !important;
  }
}

body.dark .preview-actions {
  /* 移除深色模式下的背景色 */
  /* background-color: rgba(0, 0, 0, 0.6); */
}

.no-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  color: var(--text-tertiary, #909399);
}

/* 下方列表部分 */
.records-drawer {
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
  position: relative;
  z-index: 10;
  /* height: 300px; */
}

.drawer-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

body.dark .records-drawer {
  /* background-color: var(--bg-card);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2); */
}

/* 水平滚动列表 */
.records-list {
  /* flex: 1; */
  padding: 0 10px 10px 10px;
}

.records-scroll-container {
  display: flex;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  gap: 4px;
  /* margin: 10px 10px; */
  height: 100%;
}

.records-scroll-container::-webkit-scrollbar {
  height: 6px;
}

.records-scroll-container::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark .records-scroll-container::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

/* 记录项 - 改为固定宽度以适合水平滚动 */
.record-item {
  flex: 0 0 300px;
  max-width: 240px;
  min-width: 180px;
  min-height: 140px;
  max-height: 140px;
  display: flex;
  flex-direction: column;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  background-color: var(--bg-card, #fff);
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  scroll-snap-align: start;
  position: relative;
  overflow: hidden;
}

.record-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

body.dark .record-item {
  border: 2px solid rgba(255, 255, 255, 0.05);
}

body.dark .record-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 选中状态样式增强 */
.record-item.active {
  background-color: rgba(64, 158, 255, 0.05);
  border: 2px solid #409eff;
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 20px rgba(64, 158, 255, 0.25);
  position: relative;
  z-index: 5;
}

/* 添加选中指示器 */
.record-item.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #79bbff);
  z-index: 6;
  border-radius: 4px 4px 0 0;
}

/* 暗色模式下的选中状态 */
body.dark .record-item.active {
  background-color: rgba(30, 107, 184, 0.1);
  border: 2px solid #1e6bb8;
  box-shadow: 0 8px 20px rgba(30, 107, 184, 0.3);
}

body.dark .record-item.active::before {
  background: linear-gradient(90deg, #1e6bb8, #409eff);
}

/* 恢复记录图片样式 */
.record-image {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 8px;
  flex-shrink: 0;
  position: relative;
}

.record-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
  transform: scale(1.1);
}

.record-item:hover .record-image img {
  transform: scale(1.2);
}

/* 发布状态徽章 */
.status-badge {
  position: absolute;
  top: 0px;
  right: 0px;
  padding: 4px 8px;
  border-radius: 0 4px 0 16px;
  font-size: 12px;
  font-weight: 500;
  background-color: rgba(245, 108, 108, 0.25);
  color: white;
  z-index: 2;
}

.status-badge.published {
  background-color: rgba(103, 194, 58, 0.783);
}

/* 记录信息样式调整 - 改为绝对定位覆盖在图片上，鼠标悬停显示 */
.record-info {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgb(0, 0, 0));
  color: white;
  padding: 12px;
  /* transform: translateY(100%);
  backdrop-filter: blur(4px); */
  transition: transform 0.3s ease;
  border-radius: 0 0 8px 8px;
  z-index: 3;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.record-item:hover .record-info {
  transform: translateY(0);
  backdrop-filter: blur(4px);
}

.record-item.active .record-info {
  /* transform: translateY(0); */
}

.record-title {
  margin: auto 0 0px;
  font-size: 19px;
  font-weight: 600;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  align-self: center;
  text-overflow: ellipsis;
  width: 100%;
}

body.dark .record-title {
  color: white;
}

.record-subtitle {
  margin: 0 0 0 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  align-self: center;
  text-align: center;
}

body.dark .record-subtitle {
  color: rgba(255, 255, 255, 0.8);
}

.record-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.record-date {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  text-align: right;
}

body.dark .record-date {
  color: rgba(255, 255, 255, 0.6);
}

.record-actions {
  display: none;
  gap: 4px;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 8px;
  transition: all 0.3s;
}

.record-item:hover .record-actions {
  display: flex;
}

.text-btn {
  padding: 4px 8px !important;
  height: 24px !important;
  font-size: 12px !important;
  line-height: 1 !important;
}

/* 抽屉控制按钮 */
.drawer-control {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 40;
  cursor: pointer;
}

.drawer-handle {
  background-color: var(--bg-card, #fff);
  padding: 6px 16px;
  border-radius: 0 0 15px 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

body.dark .drawer-handle {
  background-color: var(--bg-card);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.drawer-handle:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(2px);
}

.drawer-icon {
  font-size: 16px;
  color: var(--text-primary, #333);
  transition: transform 0.3s;
}

.drawer-text {
  font-size: 14px;
  color: var(--text-primary, #333);
}

body.dark .drawer-icon,
body.dark .drawer-text {
  color: var(--text-primary);
}

.rotate-90 {
  transform: rotate(-90deg);
}

/* 响应式调整 */
/* @media (max-width: 1200px) {
  .records-drawer {
    transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  }

  body.dark .records-drawer {
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
  }

  .records-drawer.collapsed {
    height: 0;
  }
} */

/* 加载状态 */
.loading-state {
  padding: 20px;
}

.loading-text {
  text-align: center;
  margin-top: 10px;
  color: var(--text-tertiary, #909399);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.empty-create-button {
  margin-top: 20px;
}

/* 对话框样式 */
.delete-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 10px 0;
}

.delete-icon {
  font-size: 48px;
  color: #f56c6c;
  margin-bottom: 15px;
}

.publish-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 10px 0;
}

.success-icon {
  font-size: 48px;
  color: #67c23a;
  margin-bottom: 15px;
}

.share-link-container {
  width: 100%;
  margin-top: 15px;
}

.share-label {
  text-align: left;
  margin-bottom: 8px;
  font-weight: 500;
}

.share-link-box {
  display: flex;
  gap: 10px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
}

::deep(el-icon) {
  color: #fff;
}

/* 提高对话框的z-index，确保在Header组件上方显示 */
:deep(.el-dialog) {
  z-index: 3000 !important;
}

:deep(.el-overlay) {
  z-index: 2999 !important;
}

/* 添加取消发布对话框的样式 */
.unpublish-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 10px 0;
}

.unpublish-dialog-content .qrcode-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.unpublish-dialog-content .qrcode-image {
  border: 1px solid #eaeaea;
  padding: 10px;
  background: #fff;
}

body.dark .unpublish-dialog-content .qrcode-image {
  background: #2c2c2c;
  border-color: #444;
}

/* 添加视频生成中的蒙层样式 */
.video-generating-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.379);
  display: flex;
  justify-content: center;
  align-items: top;
  padding-top: 10px;
  z-index: 1;
  backdrop-filter: blur(6px);
}

.overlay-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.loading-icon {
  font-size: 48px;
  color: #fff;
  margin-bottom: 10px;
}

.generating-text {
  font-size: 14px;
  color: #fff;
  font-weight: 500;
}

/* 自定义弹窗样式 */
.custom-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  background-color: #fff;
  padding: 30px;
  border-radius: 16px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  z-index: 2001;
  animation: slideUp 0.3s ease;
}

body.dark .modal-content {
  background-color: #2c2c2c;
  color: #e0e0e0;
}

.modal-close {
  position: absolute;
  top: 15px;
  right: 15px;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background-color: #e0e0e0;
}

body.dark .modal-close {
  background-color: #3c3c3c;
}

body.dark .modal-close:hover {
  background-color: #4c4c4c;
}

.modal-close .el-icon {
  font-size: 16px;
  color: #666;
}

body.dark .modal-close .el-icon {
  color: #ccc;
}

.modal-title {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 20px;
  text-align: center;
  color: #333;
}

body.dark .modal-title {
  color: #e0e0e0;
}

.modal-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}

/* 修改对话框内容样式 */
.delete-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 10px 0 20px;
}

.delete-dialog-content p {
  margin: 10px 0 0;
  color: #666;
  font-size: 16px;
}

body.dark .delete-dialog-content p {
  color: #aaa;
}

.delete-icon {
  font-size: 60px;
  color: #f56c6c;
  margin-bottom: 10px;
  filter: drop-shadow(0 4px 6px rgba(245, 108, 108, 0.3));
}

.publish-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 10px 0;
}

.success-icon {
  font-size: 60px;
  color: #67c23a;
  margin-bottom: 10px;
  filter: drop-shadow(0 4px 6px rgba(103, 194, 58, 0.3));
}

.publish-dialog-content p {
  margin: 10px 0 20px;
  color: #666;
  font-size: 16px;
}

body.dark .publish-dialog-content p {
  color: #aaa;
}

.share-link-container {
  width: 100%;
  margin-top: 20px;
}

.share-label {
  text-align: left;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

body.dark .share-label {
  color: #bbb;
}

.share-link-box {
  display: flex;
  gap: 10px;
}

/* 修改取消发布对话框的样式 */
.unpublish-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 10px 0;
}

.unpublish-dialog-content .qrcode-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.unpublish-dialog-content .qrcode-image {
  border: 1px solid #eaeaea;
  padding: 10px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

body.dark .unpublish-dialog-content .qrcode-image {
  background: #fff;
  border-color: #444;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .modal-content {
    padding: 20px 15px;
  }
  
  .modal-title {
    font-size: 20px;
  }
  
  .delete-icon, .success-icon {
    font-size: 50px;
  }
  
  .delete-dialog-content p, .publish-dialog-content p {
    font-size: 15px;
  }
}
</style> 