<template>
  <div class="ai-image-editor" v-if="visible">
    <div class="editor-backdrop" @click="handleClose"></div>
    <div class="editor-container">
      <div class="editor-header">
        <h3>AI修图</h3>
        <el-icon class="close-icon" @click="handleClose">
          <Close />
        </el-icon>
      </div>

      <div class="editor-content">
        <!-- 左侧：图片预览和历史图 -->
        <div class="editor-left-panel">
          <div class="image-preview" v-loading="loading">
            <img :src="currentImage" alt="当前图片" v-if="currentImage" @click="handleImageClick(currentImage)" />
            <div class="no-image-placeholder" v-if="!currentImage && !loading">
              <el-icon>
                <Picture />
              </el-icon>
              <span>暂无图片</span>
            </div>
          </div>
          <!-- v-if="imageHistory.length > 1" -->
          <div class="editor-history" v-if="imageHistory.length > 0">
            <div class="history-title">
              <span>历史图片</span>
              <span class="history-count">{{ imageHistory.length > 0 ? `${imageHistory.length}张` : '' }}</span>
            </div>
            <div class="history-items">
              <div v-for="(item, index) in imageHistory" :key="index" class="history-item"
                :class="{ 'active': currentImageIndex === index }" @click="selectHistoryImage(index)">
                <img :src="item.modifiedImageUrl" alt="历史版本" v-if="item.modifiedImageUrl" />
                <div v-else class="no-image-placeholder">
                  <el-icon>
                    <Picture />
                  </el-icon>
                </div>
                <div class="history-info">
                  <span class="history-index">{{ index + 1 }}</span>
                  <el-tooltip :content="item.modifyCode || '无修改码'" placement="top">
                    <span class="history-tag">
                      <el-icon>
                        <PictureFilled />
                      </el-icon>
                    </span>
                  </el-tooltip>
                </div>
              </div>

              <!-- 上传按钮 -->
              <div class="history-item upload-item" @click="handleHistoryImageUpload"
                v-if="props.editorMode != 'character'">
                <div class="upload-placeholder">
                  <el-icon>
                    <Plus />
                  </el-icon>
                  <div class="upload-text">上传</div>
                </div>
              </div>

              <!-- 隐藏的文件输入 -->
              <input type="file" ref="historyFileInput" style="display: none" accept="image/*"
                @change="onHistoryFileSelected" />
            </div>
          </div>
        </div>

        <!-- 右侧：参考图、描述和按钮 -->
        <div class="editor-right-panel">
          <!-- 参考图选择区域 -->
          <div class="reference-images-section">
            <div class="section-title">
              <!-- 参考图 (可选) -->
              <span>重绘</span>
              <div class="reference-settings">
                <!-- <el-tooltip content="设置参考图数量" placement="top">
                  <el-button
                    type="primary"
                    link
                    @click="showSlotCountSettings = true"
                  >
                    <el-icon><Setting /></el-icon>
                  </el-button>
                </el-tooltip> -->
              </div>
            </div>

            <!-- 选择现有参考图模式（仅用于分镜） -->
            <!-- <div class="reference-images-container" v-if="editorMode === 'shot'">
              <div class="reference-slot" v-for="(slot, index) in referenceSlots" :key="index">
                <div class="slot-header">
                  <span>{{ slot.title }}</span>
                  <el-button 
                    v-if="slot.image" 
                    type="danger" 
                    size="small" 
                    circle 
                    @click="removeReferenceImage(index)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
                <div 
                  class="reference-image-container" 
                  :class="{ 'has-image': slot.image }"
                  @click="openReferenceSelector(index)"
                >
                  <img v-if="slot.image" :src="slot.image" alt="参考图" @click="handleImageClick(slot.image)"/>
                  <div v-else class="add-reference">
                    <el-icon><Plus /></el-icon>
                    <span>添加参考</span>
                  </div>
                </div>
              </div>
            </div> -->

            <!-- 本地上传参考图模式（角色和场景） -->
            <!-- <div class="local-upload-container" v-if="editorMode === 'character' || editorMode === 'scene'"> -->
            <!-- <div class="upload-slots">
                <div 
                  v-for="(image, index) in localUploadedImages" 
                  :key="index"
                  class="upload-slot-item"
                >
                  <div class="upload-slot-image">
                    <img :src="image" alt="上传参考图" @click="handleImageClick(image)"/>
                    <div class="upload-slot-actions">
                      <el-button 
                        type="danger" 
                        circle 
                        size="small"
                        @click="removeLocalImage(index)"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>
                </div>
                
                <div 
                  class="upload-slot-item add-slot"
                  v-if="localUploadedImages.length < maxLocalImages"
                  @click="handleLocalImageUpload"
                >
                  <div class="upload-add-button">
                    <el-icon><Plus /></el-icon>
                    <span>上传图片</span>
                  </div>
                </div>
              </div> -->

            <!-- 隐藏的文件上传器 -->
            <!-- <input 
                type="file" 
                ref="fileInput" 
                style="display: none" 
                accept="image/*"
                @change="onFileSelected"
              />
            </div> -->
          </div>

          <div class="editor-update-controls">
            <div class="prompt-input">
              <el-input v-model="drawingPrompt" type="textarea" :autosize="{ minRows: 2, maxRows: 9 }" placeholder="描述你想重绘的图片..."
                :disabled="loading" />
            </div>

            <div class="action-buttons">
              <button class="custom-button primary-button" @click="applyEdit('redraw')"
                :disabled="!drawingPrompt || loading" :class="{ 'disabled': !drawingPrompt || loading }">
                <el-icon>
                  <Brush />
                </el-icon>
                <span>重绘（消耗30积分）</span>
              </button>
            </div>
          </div>

          <div class="editor-controls" v-if="props.editorMode != 'character'">
            <div class="section-title">
              <span>修图</span>
            </div>
            <div class="prompt-input">
              <el-input v-model="editPrompt" type="textarea" :rows="1" placeholder="描述你想对图片做的修改..."
                :disabled="loading" />
            </div>

            <!-- 添加快捷标签 -->
            <div class="quick-tags">
              <div v-for="(tag, index) in quickTags" :key="index" class="quick-tag" @click="applyQuickTag(tag)">
                {{ tag.label }}
              </div>
            </div>

            <div class="action-buttons">
              <button class="custom-button primary-update-button" @click="applyEdit('edit')"
                :disabled="!editPrompt || loading" :class="{ 'disabled': !editPrompt || loading }">
                <el-icon>
                  <Brush />
                </el-icon>
                <span>修图（消耗30积分）</span>
              </button>

            </div>
          </div>

          <!-- currentImageIndex === 0 ||  -->
          <button class="custom-button success-button" @click="confirmEdit" :disabled="loading"
            :class="{ 'disabled': loading }">
            <!-- currentImageIndex === 0 ||  -->
            <el-icon>
              <Check />
            </el-icon>
            <span>确认</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 参考图选择对话框 -->
    <el-dialog v-model="referenceSelectorVisible" title="选择参考图" width="70%" destroy-on-close>
      <div class="reference-selector">
        <div class="selector-tabs">
          <div class="selector-tab" :class="{ 'active': activeSelectorTab === 'characters' }"
            @click="activeSelectorTab = 'characters'">
            角色
          </div>
          <!-- <div 
            class="selector-tab" 
            :class="{ 'active': activeSelectorTab === 'scenes' }"
            @click="activeSelectorTab = 'scenes'"
          >
            场景
          </div> -->
          <!-- <div 
            class="selector-tab" 
            :class="{ 'active': activeSelectorTab === 'upload' }"
            @click="activeSelectorTab = 'upload'"
          >
            本地上传
          </div> -->
        </div>

        <div class="reference-grid" v-if="activeSelectorTab !== 'upload'">
          <!-- 角色选项 -->
          <template v-if="activeSelectorTab === 'characters'">
            <div v-for="character in availableCharacters" :key="character.charID" class="reference-option"
              @click="selectReference(character.image)">
              <img :src="character.image" :alt="character.name" />
              <div class="option-info">{{ character.name }}</div>
            </div>
          </template>

          <!-- 场景选项 -->
          <template v-else>
            <div v-for="scene in availableScenes" :key="scene.ID" class="reference-option"
              @click="selectReference(scene.image)">
              <img :src="scene.image" :alt="scene.name" />
              <div class="option-info">{{ scene.name }}</div>
            </div>
          </template>
        </div>

        <!-- 本地上传区域 -->
        <div class="upload-section" v-if="activeSelectorTab === 'upload'">
          <el-upload class="image-uploader" action="#" :auto-upload="false" :show-file-list="true"
            :on-change="handleFileChange" accept="image/*">
            <div class="upload-trigger">
              <el-icon class="upload-icon">
                <Plus />
              </el-icon>
              <div class="upload-text">点击或拖拽图片上传</div>
            </div>
          </el-upload>

          <div class="upload-preview" v-if="uploadedImageUrl">
            <img :src="uploadedImageUrl" alt="上传预览" />
            <div class="upload-actions">
              <el-button type="primary" @click="confirmUpload" :loading="uploadLoading">
                确认使用
              </el-button>
              <el-button @click="cancelUpload">取消</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 参考图数量设置对话框 -->
    <el-dialog v-model="showSlotCountSettings" title="设置参考图数量" width="400px">
      <el-form>
        <el-form-item label="参考图数量">
          <el-input-number v-model="slotCountSetting" :min="1" :max="10" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showSlotCountSettings = false">取消</el-button>
          <el-button type="primary" @click="applySlotCountSetting">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted, onBeforeUnmount } from 'vue';
import { Picture, Close, Check, Delete, Brush, Plus, Setting, Upload, PictureFilled } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { getAiConversationsContentList, uploadAiImageToOSS } from '@/api/auth.js';
import { uploadToOSS } from '@/api/oss.js';
import { submitImageModify, getImageModifyResult, getSourceImageList, confirmImageModify } from '@/api/image.js';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  imageUrl: {
    type: String,
    default: ''
  },
  characters: {
    type: Array,
    default: () => []
  },
  scenes: {
    type: Array,
    default: () => []
  },
  conversationId: {
    type: String,
    default: ''
  },
  editorMode: {
    type: String,
    default: 'character'
  },
  primaryId: {
    type: String,
    default: ''
  },
  secondaryId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['close', 'update']);

// 编辑提示文本
const editPrompt = ref('');
// 重绘文本
const drawingPrompt = ref('');

// 加载状态
const loading = ref(false);

// 图片历史记录
const imageHistory = ref([]);
const currentImageIndex = ref(0);

// 参考图相关状态
const referenceSlots = ref([
  { title: '角色A', image: '' },
  // { title: '角色B', image: '' },
  // { title: '场景', image: '' }
]);

// 参考图数量设置
const showSlotCountSettings = ref(false);
const slotCountSetting = ref(3);

// 参考图选择器
const referenceSelectorVisible = ref(false);
const activeSelectorTab = ref('characters');
const currentEditingSlotIndex = ref(0);

// 上传图片相关状态
const uploadedFile = ref(null);
const uploadedImageUrl = ref('');
const uploadLoading = ref(false);
const fileInput = ref(null); // 引用隐藏的文件输入元素
const historyFileInput = ref(null); // 引用历史图片上传的文件输入元素

// 获取到的角色和场景数据
const fetchedCharacters = ref([]);
const fetchedScenes = ref([]);

// 当前可用的角色和场景
const availableCharacters = computed(() => {
  // 如果通过props传入了角色数据，则优先使用props数据
  if (props.characters && props.characters.length > 0) {
    return props.characters.filter(char => char.image && !char.imageError);
  }
  // 否则使用从API获取的数据
  return fetchedCharacters.value.filter(char => char.image);
});

const availableScenes = computed(() => {
  // 如果通过props传入了场景数据，则优先使用props数据
  if (props.scenes && props.scenes.length > 0) {
    return props.scenes.filter(scene => scene.image && !scene.imageError);
  }
  // 否则使用从API获取的数据
  return fetchedScenes.value.filter(scene => scene.image);
});

// 处理图片点击事件
const handleImageClick = (image) => {
  window.openImagePreview(image, "")
}

// 当前显示的图片
const currentImage = computed(() => {
  if (imageHistory.value.length === 0) return '';
  return imageHistory.value[currentImageIndex.value]?.modifiedImageUrl || '';
});

// 本地上传的参考图
const localUploadedImages = ref([]);
const maxLocalImages = ref(1);

// 快捷标签
const quickTags = ref([
  { label: '加实体', prompt: '图中添加一道彩虹' },
  { label: '删实体', prompt: '删除图上的女孩' },
  { label: '修实体', prompt: '把手里的鸡腿变成汉堡' },
  { label: '修风格', prompt: '改成漫画风格' },
  { label: '修色彩', prompt: '把衣服改成粉色的' },
  { label: '修动作', prompt: '让他哭/笑/生气' },
  { label: '修环境', prompt: '背景换成海边/在星空下/在森林里' }
]);

// 用于存储当前修改请求的modifyCode
const currentModifyCode = ref('');
// 轮询间隔(毫秒)
const pollingInterval = ref(5000);
// 轮询计时器
const pollingTimer = ref(null);
// 轮询最大次数
const maxPollingCount = ref(40); // 最多轮询30次，约60秒
// 当前轮询次数
const currentPollingCount = ref(0);

// 存储URL到修改码的映射
const modifyCodeMap = ref({});

// 监听图片URL变化，初始化历史记录
watch(() => props.imageUrl, (newUrl) => {
  if (newUrl && props.visible) {
    // 重置所有状态
    imageHistory.value = [];
    currentImageIndex.value = 0;
    editPrompt.value = '';
    drawingPrompt.value = '';
    // 清空参考图
    resetReferenceImages();
  }
}, { immediate: true });

// 监听visible变化，重置状态
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.imageUrl) {
    // 重置所有状态
    imageHistory.value = [];
    currentImageIndex.value = 0;
    editPrompt.value = '';
    drawingPrompt.value = '';

    // 清空参考图
    resetReferenceImages();
    // 清空本地上传图片
    localUploadedImages.value = [];

    // 当编辑器显示时，如果有会话ID，则获取角色和场景数据
    if (props.conversationId) {
      fetchCharactersAndScenes();
      // 获取历史图片列表
      fetchSourceImageList();
    }
  }
});

// 监听primaryId和conversationId变化，重新获取图片历史
watch([() => props.primaryId, () => props.conversationId], ([newPrimaryId, newConversationId]) => {
  if (props.visible && newPrimaryId && newConversationId) {
    // 只有当编辑器可见且ID有效时才获取历史
    fetchSourceImageList();
  }
});

// 获取角色和场景数据
const fetchCharactersAndScenes = async () => {
  if (!props.conversationId) return;

  try {
    // 获取角色数据
    const charactersResponse = await getAiConversationsContentList(3, props.conversationId);
    if (charactersResponse && charactersResponse.data && charactersResponse.data.characters) {
      fetchedCharacters.value = charactersResponse.data.characters;
      console.log('获取角色数据成功:', fetchedCharacters.value);
    }

    // 获取场景数据
    const scenesResponse = await getAiConversationsContentList(2, props.conversationId);
    if (scenesResponse && scenesResponse.data && scenesResponse.data.scenes) {
      fetchedScenes.value = scenesResponse.data.scenes;
      console.log('获取场景数据成功:', fetchedScenes.value);
    }
  } catch (error) {
    console.error('获取角色或场景数据失败:', error);
    ElMessage.error('获取参考图数据失败');
  }
};

// 重置参考图
const resetReferenceImages = () => {
  referenceSlots.value.forEach(slot => {
    slot.image = '';
  });
};

var imageUrl = ref(props.imageUrl)

// 选择历史图片
const selectHistoryImage = (index) => {
  if (loading.value) return;
  currentImageIndex.value = index;
  imageUrl.value = imageHistory.value[index].modifiedImageUrl;
  console.log('selectHistoryImage', imageUrl.value)
};

// 打开参考图选择器
const openReferenceSelector = (slotIndex) => {
  currentEditingSlotIndex.value = slotIndex;
  // 设置默认标签页，角色A和B默认显示角色标签，场景默认显示场景标签
  activeSelectorTab.value = slotIndex === 2 ? 'scenes' : 'characters';
  referenceSelectorVisible.value = true;
};

// 选择参考图
const selectReference = (imageUrl) => {
  if (!imageUrl) return;

  referenceSlots.value[currentEditingSlotIndex.value].image = imageUrl;
  referenceSelectorVisible.value = false;
};

// 移除参考图
const removeReferenceImage = (slotIndex) => {
  referenceSlots.value[slotIndex].image = '';
};

// 处理文件变更
const handleFileChange = (file) => {
  const isImage = file.raw.type.indexOf('image/') !== -1;
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return false;
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!');
    return false;
  }

  uploadedFile.value = file.raw;
  uploadedImageUrl.value = URL.createObjectURL(file.raw);
  return false;
};

// 确认使用上传的图片
const confirmUpload = async () => {
  if (!uploadedFile.value) {
    ElMessage.warning('请先选择图片');
    return;
  }

  uploadLoading.value = true;
  try {
    // 上传图片到OSS
    const result = await uploadToOSS(uploadedFile.value, props.conversationId);
    if (result && result.url) {
      // 使用上传后的图片URL
      selectReference(result.url);
      // ElMessage.success('图片上传成功');
      // 清空上传状态
      cancelUpload();
    } else {
      throw new Error('上传失败');
    }
  } catch (error) {
    console.error('上传图片失败:', error);
    ElMessage.error('图片上传失败，请重试');
  } finally {
    uploadLoading.value = false;
  }
};

// 取消上传
const cancelUpload = () => {
  uploadedFile.value = null;
  uploadedImageUrl.value = '';
};

// 打开本地文件选择器
const handleLocalImageUpload = () => {
  fileInput.value.click();
};

// 处理文件选择
const onFileSelected = async (event) => {
  const file = event.target.files[0];
  if (!file) return;

  const isImage = file.type.indexOf('image/') !== -1;
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return;
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!');
    return;
  }

  // 直接上传文件
  uploadLoading.value = true;
  try {
    // 上传图片到OSS
    const result = await uploadToOSS(file, props.conversationId);
    if (result && result.url) {
      // 添加到本地上传图片列表
      localUploadedImages.value.push(result.url);
      ElMessage.success('图片上传成功');
    } else {
      throw new Error('上传失败');
    }
  } catch (error) {
    console.error('上传图片失败:', error);
    ElMessage.error('图片上传失败，请重试');
  } finally {
    uploadLoading.value = false;
    // 重置文件输入
    event.target.value = '';
  }
};

// 移除本地上传图片
const removeLocalImage = (index) => {
  localUploadedImages.value.splice(index, 1);
};

// 更新自定义宽高比
const updateCustomAspectRatio = () => {
  if (customWidth.value > 0 && customHeight.value > 0) {
    customAspectRatio.value = customWidth.value / customHeight.value;
    cropperAspectRatio.value = customAspectRatio.value;
  }
};

// 添加切换模式的函数
// 切换到裁剪模式
const switchToCropMode = () => {
  if (isBrushMode.value) {
    // 保存当前画布内容为图片
    saveBrushCanvasToImage();
    isBrushMode.value = false;
  }
};

// 切换到涂抹模式
const switchToBrushMode = () => {
  if (!isBrushMode.value) {
    isBrushMode.value = true;
    // 在下一次DOM更新后初始化画布
    setTimeout(() => {
      initBrushCanvas();
    }, 0);
  }
};

// 修改初始化画布函数，使用固定尺寸并考虑设备像素比
const initBrushCanvas = () => {
  if (!brushCanvas.value) return;

  // 获取画布上下文
  brushCtx.value = brushCanvas.value.getContext('2d');

  // 设置画布大小
  const img = new Image();
  img.onload = () => {
    // 获取容器尺寸
    const container = brushCanvas.value.parentElement;
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;

    // 计算图片的显示尺寸，保持原始宽高比
    const imgRatio = img.width / img.height;
    let canvasWidth, canvasHeight;

    if (containerWidth / containerHeight > imgRatio) {
      // 高度限制
      canvasHeight = containerHeight;
      canvasWidth = canvasHeight * imgRatio;
    } else {
      // 宽度限制
      canvasWidth = containerWidth;
      canvasHeight = canvasWidth / imgRatio;
    }

    // 设置画布CSS尺寸
    brushCanvas.value.style.width = `${canvasWidth}px`;
    brushCanvas.value.style.height = `${canvasHeight}px`;

    // 获取设备像素比
    const dpr = window.devicePixelRatio || 1;

    // 设置画布实际尺寸（考虑设备像素比以提高清晰度）
    brushCanvas.value.width = canvasWidth * dpr;
    brushCanvas.value.height = canvasHeight * dpr;

    // 缩放上下文以匹配设备像素比
    brushCtx.value.scale(dpr, dpr);

    // 绘制原始图片到画布
    brushCtx.value.drawImage(img, 0, 0, canvasWidth, canvasHeight);

    // 保存初始状态及尺寸信息
    canvasImg.value = img;
    canvasInfo.value = {
      width: canvasWidth,
      height: canvasHeight,
      dpr: dpr,
      imgRatio: imgRatio
    };

    // 清空绘画历史
    drawActions.value = [];
  };
  img.src = selectedFileUrl.value;
};

// 修改开始绘制（鼠标）函数
const startDrawing = (e) => {
  isDrawing.value = true;

  // 保存当前画布状态，用于撤销
  saveCanvasState();

  // 获取鼠标相对于画布的位置（考虑画布的实际显示尺寸）
  const rect = brushCanvas.value.getBoundingClientRect();
  const canvasX = (e.clientX - rect.left);
  const canvasY = (e.clientY - rect.top);

  lastX.value = canvasX;
  lastY.value = canvasY;

  // 单点绘制，避免零长度线段
  drawDot(canvasX, canvasY);
};

// 修改绘制（鼠标）函数
const draw = (e) => {
  if (!isDrawing.value) return;

  // 获取鼠标当前位置
  const rect = brushCanvas.value.getBoundingClientRect();
  const canvasX = (e.clientX - rect.left);
  const canvasY = (e.clientY - rect.top);

  // 绘制线条
  drawLine(lastX.value, lastY.value, canvasX, canvasY);

  // 更新上一次位置
  lastX.value = canvasX;
  lastY.value = canvasY;
};

// 修改开始绘制（触摸）函数
const startDrawingTouch = (e) => {
  isDrawing.value = true;

  // 阻止默认行为（如滚动）
  e.preventDefault();

  // 保存当前画布状态，用于撤销
  saveCanvasState();

  // 获取触摸点相对于画布的位置
  const rect = brushCanvas.value.getBoundingClientRect();
  const touch = e.touches[0];
  const canvasX = (touch.clientX - rect.left);
  const canvasY = (touch.clientY - rect.top);

  lastX.value = canvasX;
  lastY.value = canvasY;

  // 单点绘制，避免零长度线段
  drawDot(canvasX, canvasY);
};

// 修改绘制（触摸）函数
const drawTouch = (e) => {
  if (!isDrawing.value) return;

  // 阻止默认行为（如滚动）
  e.preventDefault();

  // 获取触摸点当前位置
  const rect = brushCanvas.value.getBoundingClientRect();
  const touch = e.touches[0];
  const canvasX = (touch.clientX - rect.left);
  const canvasY = (touch.clientY - rect.top);

  // 绘制线条
  drawLine(lastX.value, lastY.value, canvasX, canvasY);

  // 更新上一次位置
  lastX.value = canvasX;
  lastY.value = canvasY;
};

// 添加单点绘制函数
const drawDot = (x, y) => {
  if (!brushCtx.value) return;

  // 设置绘制样式
  brushCtx.value.fillStyle = `rgba(255, 0, 0, ${brushOpacity.value / 100})`;

  // 绘制一个圆点
  brushCtx.value.beginPath();
  brushCtx.value.arc(x, y, brushSize.value / 2, 0, Math.PI * 2);
  brushCtx.value.fill();
  brushCtx.value.closePath();
};

// 修改绘制线条函数以使用globalCompositeOperation属性
const drawLine = (x1, y1, x2, y2) => {
  if (!brushCtx.value) return;

  // 保存当前上下文状态
  brushCtx.value.save();

  // 设置绘制模式为"source-over"，这样重叠区域不会增加透明度
  brushCtx.value.globalCompositeOperation = 'source-over';

  // 设置绘制样式
  brushCtx.value.beginPath();
  brushCtx.value.strokeStyle = `rgba(255, 0, 0, ${brushOpacity.value / 100})`;
  brushCtx.value.lineWidth = brushSize.value;
  brushCtx.value.lineCap = 'round';
  brushCtx.value.lineJoin = 'round';

  // 绘制线条
  brushCtx.value.moveTo(x1, y1);
  brushCtx.value.lineTo(x2, y2);
  brushCtx.value.stroke();
  brushCtx.value.closePath();

  // 恢复上下文状态
  brushCtx.value.restore();
};

// 停止绘制
const stopDrawing = () => {
  isDrawing.value = false;
};

// 保存画布状态
const saveCanvasState = () => {
  if (!brushCanvas.value) return;
  drawActions.value.push(brushCanvas.value.toDataURL());
};

// 撤销最后一次绘制
const undoLastDraw = () => {
  if (drawActions.value.length === 0) return;

  // 移除最后一次绘制的状态
  const lastState = drawActions.value.pop();

  // 恢复到上一个状态
  const img = new Image();
  img.onload = () => {
    if (!canvasInfo.value) return;

    // 清空画布（考虑设备像素比）
    brushCtx.value.clearRect(0, 0, brushCanvas.value.width / canvasInfo.value.dpr, brushCanvas.value.height / canvasInfo.value.dpr);

    // 绘制上一个状态
    brushCtx.value.drawImage(img, 0, 0, canvasInfo.value.width, canvasInfo.value.height);
  };
  img.src = lastState;
};

// 修改清除画布函数
const clearCanvas = () => {
  if (!brushCanvas.value || !canvasImg.value || !canvasInfo.value) return;

  // 清空画布
  brushCtx.value.clearRect(0, 0, brushCanvas.value.width / canvasInfo.value.dpr, brushCanvas.value.height / canvasInfo.value.dpr);

  // 重新绘制原始图片
  brushCtx.value.drawImage(
    canvasImg.value,
    0,
    0,
    canvasInfo.value.width,
    canvasInfo.value.height
  );

  // 清空绘画历史
  drawActions.value = [];
};

// 修改保存涂抹画布到图像函数
const saveBrushCanvasToImage = () => {
  if (!brushCanvas.value) return;

  // 将画布内容转换为URL，指定原始图片类型
  const imageType = selectedFile.value.type || 'image/png';
  selectedFileUrl.value = brushCanvas.value.toDataURL(imageType, 1.0);
};

// 应用编辑
const applyEdit = async (functionType) => {
  if (functionType === 'edit' && !editPrompt.value.trim() || functionType === 'redraw' && !drawingPrompt.value.trim() || loading.value) return;

  loading.value = true;

  try {
    // 构建带参考图的请求参数
    const requestData = {
      sourceImageUrl: imageUrl.value == '' ? props.imageUrl : imageUrl.value,
      prompt: functionType === 'edit' ? editPrompt.value.trim() : drawingPrompt.value.trim(),
      conversationId: props.conversationId,
      contentType: getContentTypeByMode(), // 根据模式确定内容类型
      primaryId: getPrimaryId(), // 主ID
      // secondaryId: props.secondaryId || getSecondaryId(), // 二级ID
      functionType: functionType // edit-编辑   redraw-重绘
    };

    // 根据模式添加参考图
    if (props.editorMode === 'character' || props.editorMode === 'scene') {
      // 本地上传模式，添加参考图
      if (localUploadedImages.value.length > 0) {
        if (props.editorMode === 'character') {
          if (localUploadedImages.value[0]) requestData.characterImageUrl1 = removeDomain(localUploadedImages.value[0]);
          if (localUploadedImages.value[1]) requestData.characterImageUrl2 = removeDomain(localUploadedImages.value[1]);
        } else if (props.editorMode === 'scene') {
          if (localUploadedImages.value[0]) requestData.sceneImageUrl1 = removeDomain(localUploadedImages.value[0]);
        }
      }
    } else {
      // 分镜模式，添加角色和场景参考图
      const characterSlots = referenceSlots.value.filter((slot, index) => index < 2 && slot.image);
      const sceneSlot = referenceSlots.value.find((slot, index) => index === 2 && slot.image);

      if (characterSlots.length > 0) requestData.characterImageUrl1 = removeDomain(characterSlots[0].image);
      if (characterSlots.length > 1) requestData.characterImageUrl2 = removeDomain(characterSlots[1].image);
      if (sceneSlot) requestData.sceneImageUrl1 = removeDomain(sceneSlot.image);
    }

    console.log('提交图片修改请求:', requestData);

    // 提交图片修改请求
    const result = await submitImageModify(requestData);

    if (result && result.modifyCode) {
      currentModifyCode.value = result.modifyCode;
      // 开始轮询查询结果
      startPolling();
    } else {
      throw new Error('提交修改请求失败，未获取到修改码');
    }
  } catch (error) {
    console.error('编辑图片失败:', error);
    ElMessage.error('图片修改失败，请重试');
    loading.value = false;
  }
};

// 去除URL中的域名部分，只保留路径
const removeDomain = (url) => {
  if (!url) return '';

  try {
    // 尝试创建URL对象
    const urlObj = new URL(url);
    // 返回pathname部分（包括前导的/）
    return urlObj.pathname;
  } catch (error) {
    // 如果URL格式不正确，返回原始URL
    console.warn('无法解析URL:', url);
    return url;
  }
};

// 开始轮询查询修改结果
const startPolling = () => {
  // 重置轮询计数
  currentPollingCount.value = 0;

  // 清除可能存在的之前的计时器
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
  }

  // 设置轮询计时器
  pollingTimer.value = setInterval(async () => {
    try {
      currentPollingCount.value++;
      console.log(`正在查询修改结果，第 ${currentPollingCount.value} 次`);

      // 超过最大轮询次数
      if (currentPollingCount.value > maxPollingCount.value) {
        stopPolling();
        throw new Error('图片生成超时，请重试');
      }

      // 查询修改结果
      const result = await getImageModifyResult(currentModifyCode.value);

      if (result) {
        // 状态：处理状态 (0:处理中, 1:成功, 2:失败)
        if (result.status === 1 && result.modifiedImageUrl) {
          // 生成成功
          stopPolling();
          // 清空提示语
          editPrompt.value = '';

          // 保存新生成图片的修改码映射关系
          if (result.modifiedImageUrl && currentModifyCode.value) {
            modifyCodeMap.value[result.modifiedImageUrl] = currentModifyCode.value;
          }

          // 添加到历史记录
          addToHistory(result.modifiedImageUrl, currentModifyCode.value);
          ElMessage.success('图片修改成功');
        } else if (result.status === 2) {
          // 生成失败
          stopPolling();
          throw new Error(result.failureReason || '图片修改失败');
        }
        // 状态为0时继续轮询
      }
    } catch (error) {
      stopPolling();
      console.error('查询修改结果失败:', error);
      ElMessage.error(error.message || '图片修改失败，请重试');
      loading.value = false;
    }
  }, pollingInterval.value);
};

// 停止轮询
const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
  }

  // 重置loading状态
  loading.value = false;
};

// 根据模式获取内容类型
const getContentTypeByMode = () => {
  switch (props.editorMode) {
    case 'scene':
      return 2; // 场景
    case 'character':
      return 3; // 角色
    case 'shot':
      return 4; // 分镜
    default:
      return 3; // 默认为角色
  }
};

// 获取主ID
const getPrimaryId = () => {
  // 根据编辑模式获取不同的主ID
  switch (props.editorMode) {
    case 'scene':
      // 场景模式：从场景数据中获取ID
      // if (props.scenes && props.scenes.length > 0) {
      //   return props.scenes[0]?.ID || '';
      // }
      return props.primaryId || '';
    // break;
    case 'character':
      // 角色模式：从角色数据中获取ID
      // if (props.characters && props.characters.length > 0) {
      //   return props.characters[0]?.charID || '';
      // }
      return props.primaryId || '';
    // break;
    case 'shot':
      // 分镜模式：需要从父组件传递相关数据
      // 这里需要根据实际情况实现
      return props.secondaryId || '';
  }
  return '';
};

// 获取二级ID (可选)
const getSecondaryId = () => {
  // 根据编辑模式获取不同的二级ID
  // 注意：并非所有场景都需要二级ID
  switch (props.editorMode) {
    case 'shot':
      // 分镜模式可能需要二级ID
      // 这里需要根据实际情况实现
      break;
  }
  return '';
};

// 添加到历史记录
const addToHistory = (imgUrl, modifyCode = null, isUploaded = false) => {
  // 检查是否已存在相同的图片URL，避免重复添加
  const existingIndex = imageHistory.value.findIndex(item => item.modifiedImageUrl === imgUrl);
  if (existingIndex !== -1) {
    // 如果已存在，则直接切换到该图片
    currentImageIndex.value = existingIndex;
    return;
  }

  // 添加新图片到历史记录
  imageHistory.value.push({
    modifiedImageUrl: imgUrl,
    modifyCode: modifyCode,
    isUploaded: isUploaded // 标记是否为上传的图片
  });

  // 切换到最新图片
  currentImageIndex.value = imageHistory.value.length - 1;
  // 更新当前图片URL
  imageUrl.value = imageUrl;
};

// 应用参考图数量设置
const applySlotCountSetting = () => {
  const currentSlotCount = referenceSlots.value.length;
  const newSlotCount = slotCountSetting.value;

  if (newSlotCount > currentSlotCount) {
    // 增加插槽
    for (let i = currentSlotCount; i < newSlotCount; i++) {
      referenceSlots.value.push({
        title: `参考图${i + 1}`,
        image: ''
      });
    }
  } else if (newSlotCount < currentSlotCount) {
    // 减少插槽，保留前newSlotCount个
    referenceSlots.value = referenceSlots.value.slice(0, newSlotCount);
  }

  showSlotCountSettings.value = false;
  ElMessage.success(`参考图数量已设置为 ${newSlotCount}`);
};

// 确认编辑
const confirmEdit = async () => {
  if (loading.value) return;

  try {
    // 获取当前选中图片项
    const selectedImage = imageHistory.value[currentImageIndex.value];

    if (!selectedImage) {
      console.warn('未选择任何图片，无法确认');
      return;
    }

    // 获取修改码
    const modifyCode = selectedImage.modifyCode;

    if (!modifyCode) {
      console.warn('所选图片没有修改码，无法确认');
      ElMessage.warning('无法确认此图片');
      return;
    }

    console.log('确认修改，modifyCode:', modifyCode);
    const result = await confirmImageModify(modifyCode);

    if (result && result.success) {
      ElMessage.success('已成功保存修改');

      // 发送当前图片URL给父组件
      emit('update', selectedImage.modifiedImageUrl);

      // 关闭编辑器
      handleClose();
    } else {
      throw new Error('确认修改失败');
    }
  } catch (error) {
    console.error('确认修改失败:', error);
    ElMessage.error('保存修改失败');
  }
};

// 关闭编辑器
const handleClose = () => {
  if (loading.value) {
    ElMessage.warning('正在处理图片，请稍候...');
    return;
  }

  // 清理计时器
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
  }

  // 重置状态
  currentImageIndex.value = 0;

  // 重置修改码映射
  currentModifyCode.value = '';
  loading.value = false;
  emit('close');
};

// 应用快捷标签
const applyQuickTag = (tag) => {
  // 如果当前已有文本，则添加换行符
  if (editPrompt.value && !editPrompt.value.endsWith('\n') && !editPrompt.value.endsWith(' ')) {
    editPrompt.value += ' ';
  }

  // 添加标签提示语
  editPrompt.value += tag.prompt;
};

// 获取历史图片列表
const fetchSourceImageList = async () => {
  if (!props.conversationId || !props.primaryId) {
    console.log('缺少获取历史图片所需参数:', {
      conversationId: props.conversationId,
      primaryId: props.primaryId
    });
    return;
  }

  try {
    // 构建请求参数
    const params = {
      conversationId: props.conversationId,
      primaryId: props.primaryId
    };

    // 如果有二级ID，则添加到请求参数
    if (props.secondaryId) {
      // params.secondaryId = props.secondaryId;
      params.primaryId = props.secondaryId;
    }

    console.log('获取历史图片参数:', params);

    // 请求历史图片列表
    const response = await getSourceImageList(params);
    console.log('获取历史图片响应:', response);

    imageUrl.value = props.imageUrl;
    // 处理响应数据
    if (response.imageUrlInfoResList) {
      // 直接使用API返回的数据结构
      const historyItems = Array.isArray(response.imageUrlInfoResList) ? response.imageUrlInfoResList : [];

      if (historyItems.length > 0) {
        console.log('获取历史图片成功:', historyItems);

        // 直接使用API返回的数据结构
        imageHistory.value = historyItems.filter(item =>
          item && item.modifiedImageUrl && typeof item.modifiedImageUrl === 'string'
        );

        // 如果有历史图片，选择第一张
        if (imageHistory.value.length > 0) {
          currentImageIndex.value = 0;
          console.log(`已加载${imageHistory.value.length}张历史图片`);
          // ElMessage.success(`已加载${imageHistory.value.length}张历史图片`);
        }

        imageHistory.value.forEach(item => {
          if (item.modifiedImageUrl == props.imageUrl) {
            currentImageIndex.value = imageHistory.value.indexOf(item);
            imageUrl.value = item.modifiedImageUrl;
          }
        });

      } else {
        imageHistory.value = [{
          modifiedImageUrl: props.imageUrl,
          modifyCode: ''
        }];
        // imageUrl.value = props.imageUrl;
        console.log('未找到历史图片');
      }

      drawingPrompt.value = response.prompt;

    } else {
      console.log('API返回空响应');
      imageHistory.value = [{
        modifiedImageUrl: props.imageUrl,
        modifyCode: ''
      }];
      // imageUrl.value = props.imageUrl;
    }
  } catch (error) {
    console.error('获取历史图片失败:', error);
    // 不显示错误提示，避免干扰用户体验
  }
};

// 组件挂载时初始化
onMounted(() => {
  // 如果有会话ID，则获取角色和场景数据
  if (props.conversationId && props.visible) {
    fetchCharactersAndScenes();

    // 如果有primaryId，则获取历史图片
    if (props.primaryId) {
      fetchSourceImageList();
    }
  }

  // 检查是否有可用的角色和场景数据
  console.log('可用角色数量:', availableCharacters.value.length);
  console.log('可用场景数量:', availableScenes.value.length);
});

// 组件卸载时清理计时器
onBeforeUnmount(() => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
  }
});

// 取消裁剪
const cancelCrop = () => {
  cropperVisible.value = false;
  selectedFile.value = null;
  selectedFileUrl.value = '';
  // 重置绘画模式
  isBrushMode.value = false;
  // 清空绘画历史
  drawActions.value = [];
};

// 确认裁剪并上传图片
const confirmCrop = async () => {
  if (isBrushMode.value) {
    // 在涂抹模式下，直接使用画布内容
    try {
      // 将画布内容转换为Blob
      brushCanvas.value.toBlob(async (blob) => {
        if (!blob) {
          ElMessage.error('图片处理失败');
          return;
        }

        // 创建新的File对象
        const editedFile = new File([blob], selectedFile.value.name, {
          type: selectedFile.value.type,
          lastModified: new Date().getTime()
        });

        // 关闭裁剪对话框
        cropperVisible.value = false;

        // 上传处理后的图片
        uploadLoading.value = true;
        try {
          // 上传图片到OSS
          const result = await uploadToOSS(editedFile, props.conversationId);
          if (result && result.url) {
            // 添加到本地上传图片列表
            localUploadedImages.value.push(result.url);
          } else {
            throw new Error('上传失败');
          }
        } catch (error) {
          console.error('上传图片失败:', error);
          ElMessage.error('图片上传失败，请重试');
        } finally {
          uploadLoading.value = false;
          // 清空选择的文件
          selectedFile.value = null;
          selectedFileUrl.value = '';
          // 重置绘画模式
          isBrushMode.value = false;
        }
      }, selectedFile.value.type);
    } catch (error) {
      console.error('图片处理失败:', error);
      ElMessage.error('图片处理失败，请重试');
    }
  } else {
    // 在裁剪模式下，使用裁剪器的结果
    if (!cropperRef.value) {
      ElMessage.warning('裁剪器未加载完成');
      return;
    }

    try {
      // 获取裁剪结果
      const { canvas } = cropperRef.value.getResult();

      // 将canvas转换为Blob
      canvas.toBlob(async (blob) => {
        if (!blob) {
          ElMessage.error('图片裁剪失败');
          return;
        }

        // 创建新的File对象
        const croppedFile = new File([blob], selectedFile.value.name, {
          type: selectedFile.value.type,
          lastModified: new Date().getTime()
        });

        // 关闭裁剪对话框
        cropperVisible.value = false;

        // 上传裁剪后的图片
        uploadLoading.value = true;
        try {
          // 上传图片到OSS
          const result = await uploadToOSS(croppedFile, props.conversationId);
          if (result && result.url) {
            // 添加到本地上传图片列表
            localUploadedImages.value.push(result.url);
          } else {
            throw new Error('上传失败');
          }
        } catch (error) {
          console.error('上传图片失败:', error);
          ElMessage.error('图片上传失败，请重试');
        } finally {
          uploadLoading.value = false;
          // 清空选择的文件
          selectedFile.value = null;
          selectedFileUrl.value = '';
        }
      }, selectedFile.value.type);
    } catch (error) {
      console.error('裁剪失败:', error);
      ElMessage.error('图片裁剪失败，请重试');
    }
  }
};

// 处理历史图片上传
const handleHistoryImageUpload = () => {
  if (historyFileInput.value) {
    historyFileInput.value.click();
  }
};

// 处理历史文件选择
const onHistoryFileSelected = async (event) => {
  const file = event.target.files[0];
  if (!file) return;

  const isImage = file.type.indexOf('image/') !== -1;
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return;
  }
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过10MB!');
    return;
  }

  // 上传前显示加载状态
  loading.value = true;
  try {
    // 上传图片到OSS
    const result = await uploadToOSS(file, props.conversationId);
    if (result && result.url) {
      // 添加到历史记录
      const response = await uploadAiImageToOSS(getPrimaryId(), getContentTypeByMode(), props.conversationId, result.objectName, props.imageUrl);

      if (response && response.success) {
        ElMessage.success('图片上传成功');
        addToHistory(result.url, null, true);
      } else {
        throw new Error('上传失败');
      }
    } else {
      throw new Error('上传失败');
    }
  } catch (error) {
    console.error('上传图片失败:', error);
    ElMessage.error('图片上传失败，请重试');
  } finally {
    loading.value = false;
    // 重置文件输入
    event.target.value = '';
  }
};
</script>

<style scoped>
.ai-image-editor {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 15;
}

:deep(.el-loading-mask) {
  z-index: 2;
  background-color: rgba(52, 52, 52, 0.788);
  opacity: 1;
  backdrop-filter: blur(3px);
}

.editor-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.editor-container {
  position: relative;
  width: 90%;
  max-width: 1300px;
  max-height: 76vh;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.3s ease;
  transition: background-color 0.3s;
}

body.dark .editor-container {
  background-color: var(--bg-card);
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.4);
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f8fafc;
  transition: background-color 0.3s, border-color 0.3s;
}

body.dark .editor-header {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

.editor-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1e293b;
  transition: color 0.3s;
}

body.dark .editor-header h3 {
  color: var(--text-primary);
}


:deep(.el-textarea__inner) {
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  padding: 16px;
  font-size: 16px;
  background-color: #f9fafc;
  transition: all 0.3s;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03);
}

:deep(.el-textarea.is-disabled .el-textarea__inner) {
  background-color: #1f1f1f08;
}

/* 自定义滚动条样式，参考ShotGeneration.vue */
:deep(.el-textarea__inner::-webkit-scrollbar) {
  width: 0px;
}

:deep(.el-textarea__inner::-webkit-scrollbar-thumb) {
  background-color: #6365f139;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark :deep(.el-textarea__inner::-webkit-scrollbar-thumb) {
  background-color: rgba(99, 102, 241, 0.2);
}

:deep(.el-textarea__inner::-webkit-scrollbar-track) {
  background-color: #f1f5f9;
  border-radius: 4px;
  transition: background-color 0.3s;
}

body.dark :deep(.el-textarea__inner::-webkit-scrollbar-track) {
  background-color: var(--bg-tertiary);
}

body.dark :deep(.el-textarea__inner) {
  border-color: var(--border-color);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-textarea__inner:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
}

body.dark :deep(.el-textarea__inner:focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  background-color: var(--bg-tertiary);
}

:deep(.el-textarea__word-count) {
  color: #909399;
  font-size: 14px;
  padding: 8px 12px;
  transition: color 0.3s;
}

body.dark :deep(.el-textarea__word-count) {
  color: var(--text-secondary);
}


.close-icon {
  cursor: pointer;
  font-size: 20px;
  color: #64748b;
  transition: color 0.3s;
}

.close-icon:hover {
  color: #1e293b;
}

body.dark .close-icon {
  color: var(--text-tertiary);
}

body.dark .close-icon:hover {
  color: var(--text-primary);
}

.editor-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧面板样式 */
.editor-left-panel {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  border-right: 1px solid #e2e8f0;
  overflow-y: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.editor-left-panel::-webkit-scrollbar {
  display: none;
}

body.dark .editor-left-panel {
  border-color: var(--border-color);
}

.image-preview {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f8fafc;
  position: relative;
  transition: background-color 0.3s;
  border: 1px solid #e2e8f0;
}

body.dark .image-preview {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: filter 0.3s;
  cursor: zoom-in;
}

body.dark .image-preview img {
  filter: brightness(0.9);
}

.editor-history {
  display: flex;
  flex-direction: column;
  gap: 0px;
  padding: 12px 0 0 0;
}

.history-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 4px 8px 4px;
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
  margin-bottom: 4px;
  border-bottom: 1px solid #e2e8f0;
  transition: color 0.3s, border-color 0.3s;
}

body.dark .history-title {
  color: var(--text-primary);
  border-color: var(--border-color);
}

.history-count {
  color: #64748b;
  font-size: 12px;
  font-weight: normal;
  background-color: #f1f5f9;
  padding: 2px 8px;
  border-radius: 12px;
  transition: background-color 0.3s, color 0.3s;
}

body.dark .history-count {
  color: var(--text-tertiary);
  background-color: var(--bg-tertiary);
}

.history-items {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  padding: 8px 0 8px 0;
  overflow-y: hidden;
}

.history-items::-webkit-scrollbar {
  height: 8px;
}

.history-items::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
}

body.dark .history-items::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

.history-item {
  width: 70px;
  height: 70px;
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  opacity: 0.7;
  flex-shrink: 0;
}

.history-item:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

.history-item.active {
  border-color: #6366f1;
  opacity: 1;
  box-shadow: 0 2px 12px #6365f182;
}

.history-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.history-item.upload-item {
  background-color: #f1f5f9;
  border: 1px dashed #cbd5e1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.history-item.upload-item:hover {
  background-color: #e2e8f0;
  border-color: #64748b;
  transform: translateY(-2px);
}

body.dark .history-item.upload-item {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

body.dark .history-item.upload-item:hover {
  background-color: var(--bg-quaternary, #2d3748);
  border-color: var(--text-tertiary);
}

.history-item.upload-item .upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.history-item.upload-item .el-icon {
  font-size: 20px;
  color: #64748b;
}

body.dark .history-item.upload-item .el-icon {
  color: var(--text-tertiary);
}

.history-item.upload-item .upload-text {
  font-size: 12px;
  color: #64748b;
}

body.dark .history-item.upload-item .upload-text {
  color: var(--text-tertiary);
}

.history-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 4px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  color: white;
  font-size: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .editor-content {
    flex-direction: column;
    overflow: hidden;
    box-sizing: border-box;
  }

  .editor-left-panel,
  .editor-right-panel {
    width: 100%;
    padding: 12px;
  }

  .editor-left-panel {
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
  }

  body.dark .editor-left-panel {
    border-color: var(--border-color);
  }

  .image-preview {
    height: 200px;
  }

  .action-buttons {
    flex-wrap: wrap;
  }

  .reference-images-container {
    grid-template-columns: 1fr;
  }

  .reference-image-container {
    height: 50px;
  }
}

/* 上传图片区域样式 */
.upload-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
}

.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 10px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  background-color: #fafafa;
  padding: 30px;
  text-align: center;
}

.image-uploader:hover {
  border-color: #6366f1;
  background-color: #f8f8ff;
}

body.dark .image-uploader {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

body.dark .image-uploader:hover {
  border-color: #818cf8;
  background-color: var(--bg-quaternary, #2d3748);
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.upload-icon {
  font-size: 28px;
  color: #8c8c8c;
}

.upload-text {
  color: #8c8c8c;
  font-size: 14px;
}

body.dark .upload-icon,
body.dark .upload-text {
  color: var(--text-tertiary);
}

.upload-preview {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  background-color: white;
  padding: 16px;
  transition: background-color 0.3s;
}

body.dark .upload-preview {
  background-color: var(--bg-card);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
}

.upload-preview img {
  width: 100%;
  max-height: 300px;
  object-fit: contain;
  border-radius: 8px;
}

.upload-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  gap: 10px;
}

/* 修改参考图设置区域 */
.reference-settings {
  display: flex;
  align-items: center;
}

/* 本地上传模式样式 */
.local-upload-container {
  width: 100%;
  padding: 10px 0;
}

.upload-slots {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
}

.upload-slot-item {
  position: relative;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f8fafc;
  transition: all 0.3s ease;
}

body.dark .upload-slot-item {
  background-color: var(--bg-tertiary);
}

.upload-slot-image {
  width: 100%;
  height: 100%;
  position: relative;
}

.upload-slot-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-slot-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.upload-slot-image:hover .upload-slot-actions {
  opacity: 1;
}

.upload-add-button {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #64748b;
  /* border: 1px dashed #e2e8f0; */
  box-shadow: inset 0 0 0 1px #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-add-button:hover {
  background-color: #f1f5f9;
  color: #4f46e5;
  border-color: #4f46e5;
}

body.dark .upload-add-button {
  border-color: var(--border-color);
  color: var(--text-tertiary);
}

body.dark .upload-add-button:hover {
  background-color: var(--bg-quaternary, #2d3748);
  color: #818cf8;
  border-color: #818cf8;
}

.upload-add-button .el-icon {
  font-size: 24px;
}

/* 快捷标签样式 */
.quick-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.quick-tag {
  padding: 2px 8px;
  background-color: #f1f5f9;
  color: #475569;
  border-radius: 20px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.quick-tag:hover {
  background-color: #e2e8f0;
  transform: translateY(-2px);
}

body.dark .quick-tag {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border-color: var(--border-color);
}

body.dark .quick-tag:hover {
  background-color: var(--bg-quaternary, #2d3748);
}

/* 右侧面板样式 */
.editor-right-panel {
  width: 680px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 0px;
  overflow-y: auto;
}

/* 参考图区域样式 */
.reference-images-section {
  margin-bottom: 4px;
}

.section-title {
  margin-bottom: 4px;
  font-weight: 600;
  color: #1e293b;
  font-size: 16px;
  transition: color 0.3s;
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

body.dark .section-title {
  color: var(--text-primary);
}

.reference-images-container {
  display: flex;
  flex-direction: row;
  gap: 4px;
}

.reference-slot {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 8px;
}

.slot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 4px;
}

.slot-header span {
  font-size: 14px;
  color: #64748b;
  transition: color 0.3s;
}

body.dark .slot-header span {
  color: var(--text-tertiary);
}

.reference-image-container {
  height: 60px;
  max-width: 140px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f8fafc;
  border: 1px dashed #e2e8f0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.reference-image-container:hover {
  background-color: #f1f5f9;
  transform: translateY(-2px);
}

.reference-image-container.has-image {
  border-style: solid;
}

.reference-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

body.dark .reference-image-container {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

body.dark .reference-image-container:hover {
  background-color: var(--bg-quaternary, #2d3748);
}

.add-reference {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #64748b;
  transition: color 0.3s;
}

.add-reference .el-icon {
  font-size: 24px;
}

body.dark .add-reference {
  color: var(--text-tertiary);
}

body.dark .add-reference:hover {
  background-color: var(--bg-quaternary, #2d3748);
  color: #818cf8;
  border-color: #818cf8;
}

/* 参考图选择器样式 */
.reference-selector {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.selector-tabs {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
}

body.dark .selector-tabs {
  border-color: var(--border-color);
}

.selector-tab {
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 2px solid transparent;
  color: #64748b;
}

.selector-tab.active {
  color: #6366f1;
  border-bottom-color: #6366f1;
  font-weight: 600;
}

.selector-tab:hover:not(.active) {
  color: #334155;
  background-color: #f8fafc;
}

body.dark .selector-tab {
  color: var(--text-tertiary);
}

body.dark .selector-tab.active {
  color: #818cf8;
  border-bottom-color: #818cf8;
}

body.dark .selector-tab:hover:not(.active) {
  color: var(--text-secondary);
  background-color: var(--bg-tertiary);
}

.reference-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 12px;
  max-height: 70%;
  overflow-y: auto;
  padding: 8px;
}

.reference-grid::-webkit-scrollbar {
  width: 4px;
}

.reference-grid::-webkit-scrollbar-thumb {
  background-color: #6365f15d;
  border-radius: 4px;
}

body.dark .reference-grid::-webkit-scrollbar-thumb {
  background-color: rgba(99, 102, 241, 0.3);
}

.reference-option {
  height: 150px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.reference-option:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.reference-option img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}


.option-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 8px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  color: white;
  font-size: 12px;
  transition: opacity 0.3s ease;
}

.editor-update-controls {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 12px;
}

.editor-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: auto;
}

.prompt-input {
  width: 100%;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 6px;
}

/* 自定义按钮样式 */
.custom-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  background-color: #f8fafc;
  color: #1e293b;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.custom-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-color: #f1f5f9;
}

.custom-button:active {
  transform: translateY(0);
}

.primary-update-button {
  background-color: #6366f1;
  color: white;
  border-color: #6366f1;
}

.primary-update-button:hover {
  background-color: #4f46e5;
  border-color: #4f46e5;
}

body.dark .primary-update-button {
  background-color: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

body.dark .primary-update-button:hover {
  background-color: #4338ca;
  border-color: #4338ca;
}

.primary-button {
  background-color: #6e0090;
  color: white;
  border-color: rgb(204, 0, 204);
}

.primary-button:hover {
  background-color: #4f46e5;
  border-color: #4f46e5;
}

body.dark .primary-button {
  background-color: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

body.dark .primary-button:hover {
  background-color: #4338ca;
  border-color: #4338ca;
}


.success-button {
  background-color: #10b981;
  color: white;
  border-color: #10b981;
  margin-top: 10px;
}

.success-button:hover {
  background-color: #059669;
  border-color: #059669;
}

.custom-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.custom-button .el-icon {
  font-size: 16px;
}

/* 暗色模式适配 */
body.dark .custom-button {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-primary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

body.dark .custom-button:hover {
  background-color: var(--bg-quaternary, #2d3748);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

body.dark .success-button {
  background-color: #059669;
  color: white;
  border-color: #059669;
}

body.dark .success-button:hover {
  background-color: #047857;
  border-color: #047857;
}

/* 图片裁剪对话框样式 */
.image-cropper-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.cropper-area {
  height: 100%;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

body.dark .cropper-area {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

.cropper {
  height: 100%;
  width: 100%;
}

.cropper-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.crop-dimensions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.dimension-selector {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dimension-label {
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
}

body.dark .dimension-label {
  color: var(--text-primary);
}

.dimension-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.dimension-option {
  padding: 6px 12px;
  border-radius: 20px;
  background-color: #f1f5f9;
  color: #64748b;
  font-size: 14px;
  cursor: pointer;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.dimension-option:hover {
  background-color: #e2e8f0;
  color: #334155;
}

.dimension-option.active {
  background-color: #6366f1;
  color: white;
  border-color: #6366f1;
}

body.dark .dimension-option {
  background-color: var(--bg-tertiary);
  color: var(--text-tertiary);
  border-color: var(--border-color);
}

body.dark .dimension-option:hover {
  background-color: var(--bg-quaternary, #2d3748);
  color: var(--text-secondary);
}

body.dark .dimension-option.active {
  background-color: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.custom-dimension {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.dimension-divider {
  font-size: 16px;
  color: #64748b;
}

body.dark .dimension-divider {
  color: var(--text-tertiary);
}

.cropper-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 8px;
}

@media (min-width: 768px) {
  .image-cropper-container {
    flex-direction: row;
  }

  .cropper-area {
    flex: 1;
    height: 500px;
  }

  .cropper-controls {
    width: 250px;
  }
}

/* 模式切换样式 */
.edit-mode-switch {
  margin-bottom: 16px;
}

.mode-label {
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
  margin-bottom: 8px;
}

body.dark .mode-label {
  color: var(--text-primary);
}

.mode-options {
  display: flex;
  gap: 8px;
}

.mode-option {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 20px;
  background-color: #f1f5f9;
  color: #64748b;
  font-size: 14px;
  cursor: pointer;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.mode-option:hover {
  background-color: #e2e8f0;
  color: #334155;
}

.mode-option.active {
  background-color: #6366f1;
  color: white;
  border-color: #6366f1;
}

body.dark .mode-option {
  background-color: var(--bg-tertiary);
  color: var(--text-tertiary);
  border-color: var(--border-color);
}

body.dark .mode-option:hover {
  background-color: var(--bg-quaternary, #2d3748);
  color: var(--text-secondary);
}

body.dark .mode-option.active {
  background-color: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

/* 画布容器样式 */
.brush-canvas-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.brush-canvas {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: crosshair;
}

/* 画笔控制样式 */
.brush-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.brush-size,
.brush-opacity {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-label {
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
}

body.dark .control-label {
  color: var(--text-primary);
}

.brush-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

/* 修改画布样式以保持原始比例 */
.brush-canvas {
  display: block;
  margin: 0 auto;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: crosshair;
}

.history-item:hover .history-info {
  opacity: 1;
}

.history-index {
  font-size: 10px;
  padding: 2px 4px;
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: 4px;
}

.history-tag {
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #64748b8d;
  font-size: 16px;
  gap: 12px;
}

body.dark .no-image-placeholder {
  color: var(--text-tertiary);
}

.no-image-placeholder .el-icon {
  font-size: 36px;
  opacity: 0.6;
}
</style>