<template>
  <transition name="viewer-fade" appear>
    <div class="shot-image-viewer" v-if="visible" @click="closeViewer">
      <div class="viewer-backdrop">
        <img :src="currentShot.image" :alt="title" />
      </div>
      <div class="viewer-content-container">
        <div class="viewer-content">
          <!-- 图片区域 -->
          <div class="viewer-image-container">
              <img 
                :key="currentShot.id" 
                :src="currentShot.image" 
                :alt="title" 
                @click.stop 
                :class="{
                  'camera-zoom-in': isPlaying && cameraEffect === 'zoom-in',
                  'camera-zoom-out': isPlaying && cameraEffect === 'zoom-out',
                  'camera-pan-right': isPlaying && cameraEffect === 'pan-right',
                  'camera-pan-left': isPlaying && cameraEffect === 'pan-left',
                  'camera-tilt-up': isPlaying && cameraEffect === 'tilt-up',
                  'camera-tilt-down': isPlaying && cameraEffect === 'tilt-down',
                  'camera-rotate-cw': isPlaying && cameraEffect === 'rotate-cw',
                  // 'camera-rotate-ccw': isPlaying && cameraEffect === 'rotate-ccw',
                  // 'camera-diagonal-up-right': isPlaying && cameraEffect === 'diagonal-up-right',
                  // 'camera-diagonal-up-left': isPlaying && cameraEffect === 'diagonal-up-left',
                  // 'camera-diagonal-down-right': isPlaying && cameraEffect === 'diagonal-down-right',
                  // 'camera-diagonal-down-left': isPlaying && cameraEffect === 'diagonal-down-left',
                  // 'camera-perspective-in': isPlaying && cameraEffect === 'perspective-in',
                  // 'camera-perspective-out': isPlaying && cameraEffect === 'perspective-out'
                }"
                v-if="currentShot.image"
              />
          </div>

          <!-- 旁白和操作区域 -->
          <div class="viewer-narration-container" @click.stop>
            <div class="narration-header">
              <div class="shot-info">
                <span class="scene-name">{{ currentShot.sceneName }}</span>
                <span class="shot-position" v-if="props.shots && props.shots.length > 0">{{ currentShotPosition }}/{{
                  props.shots.length }}</span>
                <span class="shot-type">{{ currentShot.type }}</span>
                <span class="shot-id">分镜 {{ currentShot.id }}</span>
                <span class="shot-id">动画 {{ currentShot.movement }}</span>
                <span class="shot-duration" v-if="currentShot.lineList && currentShot.lineList.length > 0">
                  <!-- duration 总和 -->
                  {{formatDuration(currentShot.lineList.reduce((acc, line) => acc + line.duration, 0)) }}</span>
              </div>
            </div>

            <div class="narration-content">
              <!-- 旁白内容 -->
              <div class="narration-text">
                <!-- <span v-if="currentShot.narration">{{ currentShot.narration }}</span> -->
                <span v-if="currentShot.lineList && currentShot.lineList.length > 0">
                  <span v-for="lineObj in currentShot.lineList" :key="lineObj.id" class="dialogue-item">
                    <span class="dialogue-character">{{ lineObj.name }}</span>
                    <span class="dialogue-content" v-if="lineObj.voiceType != 2">{{ lineObj.line }}</span>
                  </span>
                </span>
                <span class="no-narration" v-else>暂无旁白内容</span>
              </div>

              <!-- 音频播放控制 -->
              <div class="audio-controls" v-if="currentShot.lineList && currentShot.lineList.length > 0">
                <div class="voice-play" :class="{ 'playing': isPlaying }" @click="toggleVoice">
                  <VideoPause v-if="isPlaying" class="play-icon" />
                  <VideoPlay v-else class="play-icon" />
                  <span class="wave-animation" v-if="isPlaying">
                    <span class="wave-bar"></span>
                    <span class="wave-bar"></span>
                    <span class="wave-bar"></span>
                    <span class="wave-bar"></span>
                  </span>
                </div>
              </div>
            </div>
            <!-- 音频进度条 -->
            <div class="audio-progress-container" v-if="isPlaying || (currentShot.voice && duration > 0)">
              <div class="audio-progress-bar">
                <div class="audio-progress-inner" :style="{ width: progressPercent }"></div>
              </div>
            </div>
          </div>

          <!-- 添加导航按钮 -->
          <div class="navigation-controls" @click.stop>
            <div class="nav-button prev" :class="{ 'disabled': !hasPreviousShot }" @click.stop="goToPreviousShot">
              <el-icon>
                <ArrowLeft />
              </el-icon>
            </div>
            <div class="nav-button next" :class="{ 'disabled': !hasNextShot }" @click.stop="goToNextShot">
              <el-icon>
                <ArrowRight />
              </el-icon>
            </div>
          </div>
        </div>
        <div class="viewer-shots">
          <div class="shots-gallery" v-if="props.shots && props.shots.length > 0">
            <div 
              v-for="(shot, index) in props.shots" 
              :key="'thumb-' + shot.id"
              class="shot-thumbnail"
              :class="{ 'active': shot.id === currentShot.id }"
              @click.stop="selectShot(shot.id)"
            >
              <img :src="shot.image" :alt="`分镜 ${shot.id}`" v-if="shot.image"/>
              <div class="shot-number">{{ index + 1 }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="close-button" @click="closeViewer">
        <el-icon>
          <Close />
        </el-icon>
      </div>
    </div>
  </transition>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount, computed } from 'vue'
import { Close, VideoPlay, VideoPause, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  shot: {
    type: Object,
    default: () => ({})
  },
  shots: {
    type: Array,
    default: () => []
  },
  currentShotId: {
    type: [String, Number],
    default: null
  },
  title: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['close', 'update:currentShotId'])

// 音频播放相关状态
const isPlaying = ref(false)
const audioElement = ref(null)
const currentTime = ref(0)       // 当前播放时间（秒）
const duration = ref(0)          // 音频总时长（秒）
const progress = ref(0)          // 播放进度（0-1之间的值）

// 当前显示的分镜
const currentShot = computed(() => {
  // 优先使用 currentShotId 查找分镜
  if (props.currentShotId && props.shots && props.shots.length > 0) {
    const found = props.shots.find(s => s.id === props.currentShotId)
    if (found) {
      console.log('ShotImageViewer - currentShot:', found, 'duration:', found.duration)
      return found
    }
  }

  // 如果找不到或未提供 currentShotId，则使用直接传入的 shot
  console.log('ShotImageViewer - using direct shot:', props.shot, 'duration:', props.shot.duration)
  return props.shot || {}
})

// 获取当前分镜的相机动画效果
const cameraEffect = computed(() => {
  return currentShot.value.movement || currentShot.value.camera_effect || ''
})

// 获取当前分镜在场景中的位置（1-based索引）
const currentShotPosition = computed(() => {
  if (!props.shots || props.shots.length === 0 || !props.currentShotId) return 1

  const index = props.shots.findIndex(s => s.id === props.currentShotId)
  return index >= 0 ? index + 1 : 1
})

// 是否有上一个分镜
const hasPreviousShot = computed(() => {
  if (!props.shots || props.shots.length <= 1) return false

  const currentIndex = props.shots.findIndex(s => s.id === props.currentShotId)
  return currentIndex > 0
})

// 是否有下一个分镜
const hasNextShot = computed(() => {
  if (!props.shots || props.shots.length <= 1) return false

  const currentIndex = props.shots.findIndex(s => s.id === props.currentShotId)
  return currentIndex >= 0 && currentIndex < props.shots.length - 1
})

// 切换到上一个分镜
const goToPreviousShot = () => {
  if (!hasPreviousShot.value) return

  const currentIndex = props.shots.findIndex(s => s.id === props.currentShotId)
  if (currentIndex > 0) {
    const previousShot = props.shots[currentIndex - 1]

    // 如果正在播放音频，停止
    stopAudio()

    // 更新当前分镜ID
    emit('update:currentShotId', previousShot.id)
  }
}

// 切换到下一个分镜
const goToNextShot = () => {
  if (!hasNextShot.value) return

  const currentIndex = props.shots.findIndex(s => s.id === props.currentShotId)
  if (currentIndex < props.shots.length - 1) {
    const nextShot = props.shots[currentIndex + 1]

    // 如果正在播放音频，停止
    stopAudio()

    // 更新当前分镜ID
    emit('update:currentShotId', nextShot.id)
  }
}

// 选择指定的分镜
const selectShot = (shotId) => {
  if (shotId === props.currentShotId) return
  
  // 如果正在播放音频，停止
  stopAudio()
  
  // 更新当前分镜ID
  emit('update:currentShotId', shotId)
}

// 停止音频
const stopAudio = () => {
  if (isPlaying.value && audioElement.value) {
    audioElement.value.pause()
    audioElement.value = null
    isPlaying.value = false
    // 重置进度相关状态
    currentTime.value = 0
    progress.value = 0
  }
}

// 关闭预览
const closeViewer = () => {
  // 如果正在播放音频，先停止
  stopAudio()
  emit('close')
}

// 切换音频播放
const toggleVoice = () => {
  if (isPlaying.value && audioElement.value) {
    // 暂停播放
    audioElement.value.pause()
    audioElement.value = null
    isPlaying.value = false
  } else if (currentShot.value.lineList && currentShot.value.lineList.length > 0) {
    // 开始播放
    playVoice()
  }
}

// 播放音频
const playVoice = (index = 0) => {
  if (!currentShot.value.lineList || currentShot.value.lineList.length === 0) return

  if (audioElement.value) {
    audioElement.value.pause()
    audioElement.value = null
  }

  // 重置进度相关状态
  currentTime.value = 0
  duration.value = 0
  progress.value = 0

  const audio = new Audio(currentShot.value.lineList[index].voice)

  // 设置事件监听
  audio.addEventListener('play', () => {
    isPlaying.value = true

    // 设置动画持续时间
    const animationDuration = currentShot.value.duration ? (currentShot.value.duration / 1000) : 10
    document.documentElement.style.setProperty('--shot-duration', `${animationDuration}s`)

    // 记录相机效果
    console.log('ShotImageViewer - 开始播放, 相机效果:', cameraEffect.value, '持续时间:', animationDuration + 's')
  })

  audio.addEventListener('ended', () => {
    audioElement.value = null
    isPlaying.value = false
    currentTime.value = 0
    progress.value = 0
  })

  audio.addEventListener('pause', () => {
    isPlaying.value = false
  })

  audio.addEventListener('loadedmetadata', () => {
    duration.value = audio.duration
  })

  audio.addEventListener('timeupdate', () => {
    currentTime.value = audio.currentTime
    if (duration.value > 0) {
      progress.value = audio.currentTime / duration.value
    }
  })

  // 音频播放结束
  audio.addEventListener('ended', () => {
    isPlaying.value = false
    currentTime.value = 0
    progress.value = 0

    // 顺序播放 lineList 中的语音
    if (index < currentShot.value.lineList.length - 1) {
      playVoice(index + 1)
    }
  })

  // 播放
  audio.play().catch(error => {
    console.error('播放失败:', error)
    audioElement.value = null
    isPlaying.value = false
    currentTime.value = 0
    duration.value = 0
    progress.value = 0

    // 顺序播放 lineList 中的语音
    if (index < currentShot.value.lineList.length - 1) {
      playVoice(index + 1)
    }
  })

  audioElement.value = audio
}

// 添加键盘导航
const handleKeyDown = (event) => {
  if (!props.visible) return

  if (event.key === 'ArrowLeft') {
    goToPreviousShot()
  } else if (event.key === 'ArrowRight') {
    goToNextShot()
  } else if (event.key === 'Escape') {
    closeViewer()
  }
}

// 监听 currentShot 变化自动播放音频
watch(() => currentShot.value, (newShot, oldShot) => {
  if (props.visible && newShot && newShot.lineList && newShot.lineList.length > 0 && newShot.id !== (oldShot && oldShot.id)) {
    // 短暂延迟以确保 DOM 已更新
    setTimeout(() => {
      playVoice()
    }, 100)
  }
  
  // 当分镜变化时，滚动到对应的缩略图
  setTimeout(() => {
    scrollToActiveThumbnail()
  }, 300)
}, { deep: true })

// 滚动到当前激活的缩略图
const scrollToActiveThumbnail = () => {
  if (!props.currentShotId) return
  
  // 获取缩略图元素
  const thumbnailEl = document.querySelector(`.shot-thumbnail.active`)
  if (!thumbnailEl) return
  
  // 获取画廊元素
  const galleryEl = document.querySelector('.shots-gallery')
  if (!galleryEl) return
  
  // 计算滚动位置，使活动的缩略图居中
  const thumbnailRect = thumbnailEl.getBoundingClientRect()
  const galleryRect = galleryEl.getBoundingClientRect()
  
  const scrollLeft = thumbnailEl.offsetLeft - (galleryRect.width / 2) + (thumbnailRect.width / 2)
  
  // 平滑滚动到指定位置
  galleryEl.scrollTo({
    left: Math.max(0, scrollLeft),
    behavior: 'smooth'
  })
}

// 在组件挂载时添加键盘事件监听
onMounted(() => {
  window.addEventListener('keydown', handleKeyDown)
  
  // 初始化时，如果组件可见且当前分镜有语音，则自动播放
  if (props.visible && currentShot.value && currentShot.value.lineList && currentShot.value.lineList.length > 0) {
    // 延迟一段时间后播放，确保组件已完全渲染
    setTimeout(() => {
      playVoice()
    }, 500)
  }
  
  // 初始化滚动位置
  setTimeout(() => {
    scrollToActiveThumbnail()
  }, 300)
})

// 在组件卸载前清理音频资源和事件监听
onBeforeUnmount(() => {
  stopAudio()
  window.removeEventListener('keydown', handleKeyDown)
})

// 监听visible变化
watch(() => props.visible, (newValue) => {
  if (!newValue) {
    stopAudio()
  } else if (newValue && currentShot.value && currentShot.value.lineList && currentShot.value.lineList.length > 0) {
    // 当预览器变为可见状态，并且当前分镜有语音，自动播放
    setTimeout(() => {
      playVoice()
    }, 300)
  }
  
  // 初始化滚动位置
  if (newValue) {
    setTimeout(() => {
      scrollToActiveThumbnail()
    }, 300)
  }
})

// 格式化毫秒为秒，保留一位小数
const formatDuration = (milliseconds) => {
  console.log('ShotImageViewer - formatDuration called with:', milliseconds)
  if (milliseconds === undefined || milliseconds === null || isNaN(milliseconds)) return ''
  return (milliseconds / 1000).toFixed(1) + ' 秒'
}

// 格式化时间为 mm:ss 格式
const formatTime = (seconds) => {
  if (!seconds) return '00:00'
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 播放进度百分比
const progressPercent = computed(() => {
  return `${Math.round(progress.value * 100)}%`
})
</script>

<style scoped>
/* 添加查看器过渡动画 */
.viewer-fade-enter-active,
.viewer-fade-leave-active {
  transition: all 0.35s cubic-bezier(0.25, 0.8, 0.5, 1);
  will-change: opacity, transform;
}

.dialogue-character{
  color: #6366f1;
  font-weight: 600;
  font-size: 12px;
  background-color: #eef2ff;
  padding: 1px 6px;
  border-radius: 4px;
  border: 1px solid #6366f1;
  margin-right: 4px;
}

.viewer-fade-enter-from,
.viewer-fade-leave-to {
  opacity: 0;
}

.viewer-fade-enter-from .viewer-content {
  transform: scale(0.85);
}

.viewer-fade-leave-to .viewer-content {
  transform: scale(0.85);
}

/* 添加图片切换动画 */
.image-switch-enter-active,
.image-switch-leave-active {
  transition: all 0.4s ease;
}

.image-switch-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.image-switch-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

.viewer-content {
  transition: transform 0.35s cubic-bezier(0.25, 0.8, 0.5, 1);
}

.shot-image-viewer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.viewer-content-container{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  z-index: 1;
  gap: 4px;
}

.viewer-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgb(0, 0, 0);
  backdrop-filter: blur(3px);
  animation: backdropEnhance 0.6s ease-out forwards;
  overflow: hidden;
}

.viewer-backdrop img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(8px);
  opacity: 0.4;
  transform-origin: center;
  animation: slowZoom 30s infinite alternate ease-in-out;
}

@keyframes backdropEnhance {
  from {
    backdrop-filter: blur(0px);
    background-color: rgba(0, 0, 0, 0.8);
  }
  to {
    backdrop-filter: blur(3px);
    background-color: rgba(0, 0, 0, 0.9);
  }
}

@keyframes slowZoom {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}

body.dark .viewer-backdrop {
  background-color: rgb(0, 0, 0);
}

.viewer-content {
  position: relative;
  display: flex;
  flex-direction: column;
  max-width: 90%;
  min-width: 60%;
  max-height: 90vh;
  border-radius: 12px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.3);
}

body.dark .viewer-content {
  background-color: var(--bg-card);
}

.viewer-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70vh;
  overflow: hidden;
  background-color: #000;
  position: relative;
}

.viewer-image-container img {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.viewer-narration-container {
  padding: 16px;
  width: 100%;
  box-sizing: border-box;
}

.narration-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.shot-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.scene-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 16px;
}

.shot-type {
  color: #6366f1;
  font-size: 14px;
  background-color: #eef2ff;
  padding: 2px 8px;
  border-radius: 4px;
}

.shot-id {
  color: #64748b;
  font-size: 12px;
  background-color: #f1f5f9;
  padding: 2px 8px;
  border-radius: 4px;
}

.shot-position {
  color: #ffffff;
  font-size: 12px;
  background-color: #6366f1;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.shot-duration {
  color: #0369a1;
  font-size: 12px;
  background-color: #e0f2fe;
  padding: 2px 8px;
  border-radius: 4px;
}

body.dark .scene-name {
  color: var(--text-primary);
}

body.dark .shot-type {
  background-color: rgba(99, 102, 241, 0.2);
  color: #818cf8;
}

body.dark .shot-id {
  background-color: var(--bg-tertiary);
  color: var(--text-tertiary);
}

body.dark .shot-position {
  background-color: rgba(99, 101, 241, 0.59);
  color: #ffffff;
}

body.dark .shot-duration {
  background-color: rgba(14, 165, 233, 0.2);
  color: #0ea5e9;
}

.close-button {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 36px;
  height: 36px;
  background-color: #f1f5f9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 10;
  animation: pulse 2s infinite;
  animation-delay: 1s;
}

.close-button:hover {
  background-color: #ef4444;
  color: white;
  transform: rotate(90deg) scale(1.1);
  animation: none;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(241, 245, 249, 0.7);
    transform: scale(1);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(241, 245, 249, 0);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(241, 245, 249, 0);
    transform: scale(1);
  }
}

body.dark .close-button {
  background-color: var(--bg-tertiary);
  color: var(--text-tertiary);
}

body.dark .close-button:hover {
  background-color: #ef4444;
  color: white;
}

.narration-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.narration-text {
  flex: 1;
  font-size: 16px;
  line-height: 1.6;
  color: #334155;
  background-color: #ebf2f8;
  padding: 12px;
  border-radius: 8px;
  border-left: 3px solid #6366f1;
}

.no-narration {
  color: #94a3b8;
  font-style: italic;
}

body.dark .narration-text {
  color: var(--text-secondary);
  background-color: var(--bg-tertiary);
}

body.dark .no-narration {
  color: var(--text-tertiary);
}

/* 音频播放相关样式 */
.audio-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.voice-play {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: rgba(64, 160, 255, 0.715);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  position: relative;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
  cursor: pointer;
}

.voice-play:hover {
  transform: scale(1.1);
  background-color: #409eff;
  box-shadow: 0 4px 10px rgba(64, 158, 255, 0.4);
}

.play-icon {
  width: 24px;
  height: 24px;
}

/* 播放状态 */
.voice-play.playing {
  background-color: #67c23a;
  box-shadow: 0 2px 6px rgba(103, 194, 58, 0.3);
}

.voice-play.playing:hover {
  background-color: #85ce61;
  box-shadow: 0 4px 10px rgba(103, 194, 58, 0.4);
}

/* 波形动画 */
.wave-animation {
  position: absolute;
  left: 50%;
  top: -18px;
  transform: translateX(-50%);
  display: flex;
  align-items: flex-end;
  height: 16px;
  width: 20px;
  gap: 2px;
}

.wave-bar {
  width: 3px;
  background-color: #67c23a;
  border-radius: 1px;
  animation: waveAnimation 0.8s infinite ease-in-out;
}

.wave-bar:nth-child(1) {
  height: 7px;
  animation-delay: 0s;
}

.wave-bar:nth-child(2) {
  height: 10px;
  animation-delay: 0.2s;
}

.wave-bar:nth-child(3) {
  height: 8px;
  animation-delay: 0.4s;
}

.wave-bar:nth-child(4) {
  height: 9px;
  animation-delay: 0.6s;
}

@keyframes waveAnimation {

  0%,
  100% {
    transform: scaleY(0.6);
  }

  50% {
    transform: scaleY(1);
  }
}

body.dark .voice-play {
  background-color: rgba(64, 160, 255, 0.3);
}

body.dark .voice-play:hover {
  background-color: rgba(64, 160, 255, 0.8);
}

body.dark .voice-play.playing {
  background-color: rgba(103, 194, 58, 0.6);
}

body.dark .voice-play.playing:hover {
  background-color: rgba(103, 194, 58, 0.8);
}

/* 导航按钮样式 */
.navigation-controls {
  position: absolute;
  top: 50%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  box-sizing: border-box;
  transform: translateY(-50%);
  pointer-events: none;
  /* 允许点击穿透到底层 */
}

.nav-button {
  width: 44px;
  height: 44px;
  background-color: rgb(255, 255, 255);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1e293b;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  pointer-events: auto;
  /* 恢复按钮的点击能力 */
  opacity: 0.8;
  transform: translateX(0);
}

.nav-button.prev:hover {
  background-color: rgba(99, 102, 241, 0.9);
  color: white;
  transform: translateX(-5px) scale(1.1);
  opacity: 1;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

.nav-button.next:hover {
  background-color: rgba(99, 102, 241, 0.9);
  color: white;
  transform: translateX(5px) scale(1.1);
  opacity: 1;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
}

.nav-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-button.disabled:hover {
  transform: none;
  background-color: rgb(255, 255, 255);
  color: #1e293b;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

body.dark .nav-button {
  background-color: rgba(34, 60, 101, 0.983);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  color: #ffffff;
}

body.dark .nav-button.prev:hover,
body.dark .nav-button.next:hover {
  background-color: rgba(99, 102, 241, 0.9);
}

body.dark .nav-button.disabled:hover {
  background-color: rgba(34, 60, 101, 0.983);
  color: #ffffff;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .viewer-content {
    max-width: 95%;
    max-height: 95vh;
  }

  .narration-content {
    flex-direction: column;
  }

  .audio-controls {
    margin-top: 16px;
    flex-direction: row;
    justify-content: center;
    width: 100%;
  }

  .navigation-controls {
    padding: 0 10px;
  }

  .nav-button {
    width: 36px;
    height: 36px;
  }
}

/* 音频进度条样式 */
.audio-progress-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  /* background-color: rgba(0, 0, 0, 0.5); */
  box-sizing: border-box;
}

.audio-progress-bar {
  width: 100%;
  height: 2px;
  background-color: rgba(255, 255, 255, 0);
  border-radius: 2px;
  overflow: hidden;
}

.audio-progress-inner {
  height: 100%;
  background-color: #0b81f7;
  border-radius: 2px;
  transition: width 0.25s linear;
}

.audio-time {
  display: flex;
  justify-content: space-between;
  color: white;
  font-size: 12px;
  opacity: 0.8;
}

.current-time,
.duration-time {
  font-family: monospace;
}

body.dark .audio-progress-inner {
  background-color: rgba(64, 158, 255, 0.8);
}

/* 相机效果动画优化 */
.camera-zoom-in,
.camera-zoom-out,
.camera-pan-right,
.camera-pan-left,
.camera-tilt-up,
.camera-tilt-down,
.camera-rotate-cw,
.camera-rotate-ccw,
.camera-diagonal-up-right,
.camera-diagonal-up-left,
.camera-diagonal-down-right,
.camera-diagonal-down-left,
.camera-perspective-in,
.camera-perspective-out {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 相机效果动画 */
.camera-zoom-in {
  animation: zoomIn var(--shot-duration, 10s) linear forwards;
}

.camera-zoom-out {
  animation: zoomOut var(--shot-duration, 10s) linear forwards;
}

.camera-pan-right {
  animation: panRight var(--shot-duration, 10s) linear forwards;
}

.camera-pan-left {
  animation: panLeft var(--shot-duration, 10s) linear forwards;
}

.camera-tilt-up {
  animation: tiltUp var(--shot-duration, 10s) linear forwards;
}

.camera-tilt-down {
  animation: tiltDown var(--shot-duration, 10s) linear forwards;
}

/* 新增相机动画效果 */
.camera-rotate-cw {
  animation: rotateCW var(--shot-duration, 10s) ease-in-out forwards;
}

.camera-rotate-ccw {
  animation: rotateCCW var(--shot-duration, 10s) ease-in-out forwards;
}

.camera-diagonal-up-right {
  animation: diagonalUpRight var(--shot-duration, 10s) linear forwards;
}

.camera-diagonal-up-left {
  animation: diagonalUpLeft var(--shot-duration, 10s) linear forwards;
}

.camera-diagonal-down-right {
  animation: diagonalDownRight var(--shot-duration, 10s) linear forwards;
}

.camera-diagonal-down-left {
  animation: diagonalDownLeft var(--shot-duration, 10s) linear forwards;
}

.camera-perspective-in {
  animation: perspectiveIn var(--shot-duration, 10s) ease-out forwards;
}

.camera-perspective-out {
  animation: perspectiveOut var(--shot-duration, 10s) ease-in forwards;
}

/* 动画关键帧定义 */
@keyframes zoomIn {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(1.2);
  }
}

@keyframes zoomOut {
  0% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes panRight {
  0% {
    transform: translateX(0) scale(1);
  }

  100% {
    transform: translateX(-10%) scale(1.2);
  }
}

@keyframes panLeft {
  0% {
    transform: translateX(0) scale(1);
  }

  100% {
    transform: translateX(10%) scale(1.2);
  }
}

@keyframes tiltUp {
  0% {
    transform: translateY(0) scale(1);
  }

  100% {
    transform: translateY(10%) scale(1.2);
  }
}

@keyframes tiltDown {
  0% {
    transform: translateY(0) scale(1);
  }

  100% {
    transform: translateY(-10%) scale(1.2);
  }
}

/* 新增动画关键帧定义 */
@keyframes rotateCW {
  0% {
    transform: rotate(0deg) scale(1);
  }
  100% {
    transform: rotate(5deg) scale(1.15);
  }
}

@keyframes rotateCCW {
  0% {
    transform: rotate(0deg) scale(1);
  }
  100% {
    transform: rotate(-5deg) scale(1.15);
  }
}

@keyframes diagonalUpRight {
  0% {
    transform: translate(0, 0) scale(1);
  }
  100% {
    transform: translate(5%, -5%) scale(1.15);
  }
}

@keyframes diagonalUpLeft {
  0% {
    transform: translate(0, 0) scale(1);
  }
  100% {
    transform: translate(-5%, -5%) scale(1.15);
  }
}

@keyframes diagonalDownRight {
  0% {
    transform: translate(0, 0) scale(1);
  }
  100% {
    transform: translate(5%, 5%) scale(1.15);
  }
}

@keyframes diagonalDownLeft {
  0% {
    transform: translate(0, 0) scale(1);
  }
  100% {
    transform: translate(-5%, 5%) scale(1.15);
  }
}

@keyframes perspectiveIn {
  0% {
    transform: perspective(1000px) rotateY(0deg) scale(1);
  }
  100% {
    transform: perspective(1000px) rotateY(2deg) scale(1.15);
  }
}

@keyframes perspectiveOut {
  0% {
    transform: perspective(1000px) rotateY(0deg) scale(1.15);
  }
  100% {
    transform: perspective(1000px) rotateY(-2deg) scale(1);
  }
}

/* 分镜选择器样式 */
.viewer-shots {
  max-width: 90%;
  min-width: 60%;
  /* padding: 8px 16px; */
  background-color: #f8fafc;
  border-radius: 12px;
  overflow-y: auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

body.dark .viewer-shots {
  background-color: var(--bg-tertiary);
}

.shots-gallery {
  display: flex;
  overflow-x: auto;
  gap: 0px;
  margin: 4px 8px;
  padding: 0px 10px;
  -ms-overflow-style: none;  /* IE and Edge */
  /* scrollbar-width: thin; */
  overflow-y: hidden;
}

.shots-gallery::-webkit-scrollbar {
  height: 8px;
}

.shots-gallery::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.shots-gallery::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

body.dark .shots-gallery::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

body.dark .shots-gallery::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

.shot-thumbnail {
  position: relative;
  width: 130px;
  height: 72px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  flex-shrink: 0;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  /* border: 1px solid transparent; */
  box-sizing: border-box;
  transform: scale(0.9);
  opacity: 0.6;
  /* filter: grayscale(30%); */
  border-color: #ffffff72;
}

.shot-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease-out;
}

.shot-thumbnail.active {
  border: 2px solid transparent;
  border-color: #a3a4ff;
  transform: scale(0.95);
  opacity: 1;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.5);
  filter: grayscale(0%);
  animation: activeThumbnailPulse 2s infinite;
}

.shot-thumbnail:hover img {
  transform: scale(1.05);
}

.shot-thumbnail.active img {
  transform: scale(1);
}

.shot-number {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 6px;
  padding: 2px 4px;
  border-top-left-radius: 6px;
  transition: all 0.2s ease;
}

.shot-thumbnail:hover {
  transform: scale(1);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  opacity: 0.9;
  filter: grayscale(0%);
}

.shot-thumbnail.active:hover {
  transform: scale(1);
  box-shadow: 0 6px 12px rgba(99, 102, 241, 0.4);
}

@keyframes activeThumbnailPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(99, 102, 241, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  }
}

body.dark .shot-thumbnail {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

body.dark .shot-thumbnail.active {
  border-color: rgba(99, 102, 241, 0.8);
  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.4);
}

body.dark .shot-thumbnail:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

body.dark .shot-thumbnail.active:hover {
  box-shadow: 0 6px 12px rgba(99, 102, 241, 0.5);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .viewer-shots {
    padding: 10px;
    max-height: 120px;
  }
  
  .shot-thumbnail {
    width: 80px;
    height: 60px;
  }
  
  .shots-gallery {
    gap: 8px;
  }
}
</style>