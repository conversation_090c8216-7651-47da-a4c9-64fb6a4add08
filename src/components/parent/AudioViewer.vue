<template>
  <div class="audio-viewer" v-if="visible" @click="closeViewer">
    <div class="viewer-backdrop"></div>
    <div class="viewer-container">
      <div class="viewer-content">
        <!-- 音频播放区域 -->
        <div class="audio-section">
          <div class="audio-player-container">
            <div class="audio-waveform">
              <!-- 音频波形图或封面图 -->
              <div class="audio-cover" v-if="coverUrl">
                <img :src="coverUrl" alt="音频封面" class="cover-image" />
              </div>
              <div class="waveform-placeholder" v-else>
                <el-icon class="audio-icon"><Headset /></el-icon>
              </div>
            </div>
            
            <div class="audio-controls">
              <!-- 播放/暂停按钮 -->
              <div class="play-button" @click.stop="togglePlay">
                <el-icon v-if="isPlaying"><VideoPause /></el-icon>
                <el-icon v-else><VideoPlay /></el-icon>
              </div>
              
              <!-- 进度条 -->
              <div class="progress-container">
                <div class="time current-time">{{ formatTime(currentTime) }}</div>
                <div class="progress-bar" @click.stop="seekAudio">
                  <div class="progress-background"></div>
                  <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
                  <div class="progress-handle" :style="{ left: progressPercentage + '%' }"></div>
                </div>
                <div class="time total-time">{{ formatTime(duration) }}</div>
              </div>
              
              <!-- 音量控制 -->
              <div class="volume-control">
                <el-icon class="volume-icon" @click.stop="toggleMute">
                  <Mute v-if="isMuted" />
                  <Headset v-else />
                </el-icon>
                <el-slider
                  v-model="volume"
                  :min="0"
                  :max="100"
                  @input="updateVolume"
                  @click.stop
                  class="volume-slider"
                />
              </div>
            </div>
          </div>
        </div>
        
        <!-- 音频信息区域 -->
        <div class="info-section" @click.stop>
          <div class="info-content">
            <h3 class="audio-title" v-if="title">{{ title }}</h3>
            
            <div class="audio-description" v-if="description">
              <p class="description-content">{{ description }}</p>
            </div>
            
            <div class="audio-metadata">
              <div class="metadata-item" v-if="artist">
                <span class="metadata-label">艺术家:</span>
                <span class="metadata-value">{{ artist }}</span>
              </div>
              <div class="metadata-item" v-if="album">
                <span class="metadata-label">专辑:</span>
                <span class="metadata-value">{{ album }}</span>
              </div>
              <div class="metadata-item" v-if="fileFormat">
                <span class="metadata-label">格式:</span>
                <span class="metadata-value">{{ fileFormat }}</span>
              </div>
              <div class="metadata-item" v-if="fileSize">
                <span class="metadata-label">大小:</span>
                <span class="metadata-value">{{ formatFileSize(fileSize) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="action-buttons">
        <div class="download-button" @click="downloadAudio" title="下载音频">
          <el-icon><Download /></el-icon>
        </div>
        <div class="close-button" @click="closeViewer">
          <el-icon><Close /></el-icon>
        </div>
      </div>
    </div>
    
    <!-- 隐藏的音频元素 -->
    <audio 
      ref="audioRef" 
      :src="audioUrl" 
      @timeupdate="onTimeUpdate" 
      @loadedmetadata="onAudioLoaded"
      @ended="onAudioEnded"
      @play="isPlaying = true"
      @pause="isPlaying = false"
    ></audio>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, computed } from 'vue';
import { Close, VideoPlay, VideoPause, Headset, Mute, Download } from '@element-plus/icons-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  audioUrl: {
    type: String,
    default: ''
  },
  coverUrl: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  artist: {
    type: String,
    default: ''
  },
  album: {
    type: String,
    default: ''
  },
  fileFormat: {
    type: String,
    default: ''
  },
  fileSize: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['close']);

// 音频播放相关状态
const audioRef = ref(null);
const isPlaying = ref(false);
const currentTime = ref(0);
const duration = ref(0);
const volume = ref(80); // 默认音量80%
const isMuted = ref(false);
const previousVolume = ref(80); // 存储静音前的音量

// 计算进度百分比
const progressPercentage = computed(() => {
  if (!duration.value) return 0;
  return (currentTime.value / duration.value) * 100;
});

// 关闭预览
const closeViewer = () => {
  if (audioRef.value) {
    audioRef.value.pause();
  }
  emit('close');
};

// 播放/暂停切换
const togglePlay = () => {
  if (!audioRef.value) return;
  
  if (isPlaying.value) {
    audioRef.value.pause();
  } else {
    audioRef.value.play().catch(err => {
      console.warn('音频播放失败:', err);
    });
  }
};

// 静音切换
const toggleMute = () => {
  if (!audioRef.value) return;
  
  if (isMuted.value) {
    // 取消静音，恢复之前的音量
    volume.value = previousVolume.value;
    audioRef.value.volume = previousVolume.value / 100;
    isMuted.value = false;
  } else {
    // 静音，保存当前音量
    previousVolume.value = volume.value;
    volume.value = 0;
    audioRef.value.volume = 0;
    isMuted.value = true;
  }
};

// 更新音量
const updateVolume = (val) => {
  if (!audioRef.value) return;
  audioRef.value.volume = val / 100;
  
  // 根据音量值更新静音状态
  isMuted.value = val === 0;
};

// 跳转到指定位置
const seekAudio = (event) => {
  if (!audioRef.value || !duration.value) return;
  
  const progressBar = event.currentTarget;
  const rect = progressBar.getBoundingClientRect();
  const offsetX = event.clientX - rect.left;
  const percentage = offsetX / rect.width;
  
  // 设置新的播放位置
  audioRef.value.currentTime = percentage * duration.value;
};

// 音频时间更新事件
const onTimeUpdate = () => {
  if (!audioRef.value) return;
  currentTime.value = audioRef.value.currentTime;
};

// 音频加载完成事件
const onAudioLoaded = () => {
  if (!audioRef.value) return;
  duration.value = audioRef.value.duration;
  
  // 设置初始音量
  audioRef.value.volume = volume.value / 100;
};

// 音频播放结束事件
const onAudioEnded = () => {
  isPlaying.value = false;
};

// 格式化时间显示 (mm:ss)
const formatTime = (seconds) => {
  if (!seconds) return '00:00';
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B';
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`;
};

// 下载音频
const downloadAudio = async () => {
  if (!props.audioUrl) {
    console.error('音频链接不存在');
    return;
  }

  try {
    // 使用fetch API获取音频文件
    const response = await fetch(props.audioUrl);

    // 检查响应状态
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`);
    }

    // 将响应转换为blob
    const blob = await response.blob();

    // 创建一个blob URL
    const blobUrl = URL.createObjectURL(blob);

    // 创建一个临时的a标签用于下载
    const link = document.createElement('a');
    link.href = blobUrl;

    // 获取文件名，优先使用title，其次使用artist，最后使用默认名称
    const fileName = props.title || props.artist || 'audio';
    const fileExtension = props.fileFormat || props.audioUrl.split('.').pop() || 'mp3';
    link.download = `${fileName}.${fileExtension}`;
    link.style.display = 'none';
    document.body.appendChild(link);

    // 触发点击下载
    link.click();

    // 清理
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl); // 释放blob URL
    }, 100);

    console.log('音频下载已开始');
  } catch (error) {
    console.error('下载音频失败:', error);

    // 如果fetch失败，尝试直接在新标签页中打开音频
    window.open(props.audioUrl, '_blank');
  }
};

// 监听ESC键关闭预览
const handleKeyDown = (e) => {
  if (e.key === 'Escape' && props.visible) {
    closeViewer();
  }
};

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal && audioRef.value) {
    // 当组件显示时，尝试播放音频
    setTimeout(() => {
      audioRef.value.play().catch(err => {
        console.warn('自动播放失败:', err);
      });
    }, 100);
  }
});

onMounted(() => {
  document.addEventListener('keydown', handleKeyDown);
  togglePlay()
});

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleKeyDown);
});
</script>

<style scoped>
.audio-viewer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.viewer-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(4px);
}

.viewer-container {
  position: relative;
  width: 90%;
  max-width: 1200px;
  height: auto;
  max-height: 600px;
  z-index: 1;
  display: flex;
  flex-direction: column;
}

.viewer-content {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 16px;
  overflow: hidden;
  background-color: rgb(30, 30, 30);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.3);
}

.audio-section {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px;
  background-color: rgba(0, 0, 0, 0.2);
}

.audio-player-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.audio-waveform {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  overflow: hidden;
}

.audio-cover {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cover-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.waveform-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.audio-icon {
  font-size: 60px;
  color: rgba(255, 255, 255, 0.3);
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.play-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.play-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.play-button .el-icon {
  font-size: 24px;
  color: white;
}

.progress-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.time {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  width: 45px;
  text-align: center;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  position: relative;
  cursor: pointer;
}

.progress-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 3px;
}

.progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #409EFF;
  border-radius: 3px;
}

.progress-handle {
  position: absolute;
  top: 50%;
  width: 12px;
  height: 12px;
  background-color: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.progress-bar:hover .progress-handle {
  opacity: 1;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 120px;
}

.volume-icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.volume-slider {
  width: 80px;
}

.info-section {
  flex: 1;
  min-width: 320px;
  max-width: 400px;
  overflow-y: auto;
  background-color: rgba(40, 40, 40, 0.092);
  color: #fff;
  padding: 0;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  animation: slideIn 0.3s ease-out;
}

.info-content {
  padding: 20px;
}

.audio-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: #fff;
}

.description-content {
  font-size: 14px;
  line-height: 1.6;
  margin: 0 0 20px 0;
  color: rgba(255, 255, 255, 0.8);
  white-space: pre-wrap;
  word-break: break-word;
  text-align: left;
}

.audio-metadata {
  margin-top: 20px;
  padding-top: 15px;
  /* border-top: 1px solid rgba(255, 255, 255, 0.1); */
}

.metadata-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.metadata-label {
  color: rgba(255, 255, 255, 0.6);
}

.metadata-value {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.action-buttons {
  position: absolute;
  top: -20px;
  right: -20px;
  display: flex;
  gap: 10px;
  z-index: 2;
}

.download-button,
.close-button {
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.download-button:hover,
.close-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.download-button {
  background-color: rgba(64, 158, 255, 0.8);
}

.download-button:hover {
  background-color: rgba(64, 158, 255, 1);
}

@keyframes slideIn {
  from {
    transform: translateX(30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .viewer-container {
    width: 95%;
    max-height: 80vh;
  }
  
  .viewer-content {
    flex-direction: column;
  }
  
  .audio-section {
    flex: none;
    padding: 20px;
  }
  
  .audio-waveform {
    height: 160px;
  }
  
  .audio-controls {
    flex-wrap: wrap;
  }
  
  .volume-control {
    width: 100%;
    margin-top: 10px;
  }
  
  .volume-slider {
    width: calc(100% - 30px);
  }
  
  .info-section {
    flex: 1;
    max-width: 100%;
    width: 100%;
    min-width: unset;
    border-left: none;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .action-buttons {
    top: 10px;
    right: 10px;
  }

  .download-button,
  .close-button {
    width: 36px;
    height: 36px;
  }
}
</style> 