<template>
  <div class="video-viewer" v-if="visible" @click="closeViewer">
    <div class="viewer-backdrop"></div>
    <div class="viewer-container">
      <div class="viewer-content">
        <!-- 视频播放区域 -->
        <div class="video-section">
          <video 
            ref="videoRef" 
            :src="videoUrl" 
            controls 
            autoplay 
            @click.stop
            class="video-element"
          ></video>
        </div>
        
        <!-- 视频信息区域 -->
        <div class="info-section" @click.stop>
          <div class="info-content">
            <h3 class="video-title" v-if="title">{{ title }}</h3>
            
            <div class="video-prompt" v-if="prompt">
              <!-- <h4 class="prompt-title">视频描述</h4> -->
              <p class="prompt-content">{{ prompt }}</p>
            </div>
            
            <div class="video-metadata" v-if="resolution || duration">
              <div class="metadata-item" v-if="resolution">
                <span class="metadata-label">分辨率:</span>
                <span class="metadata-value">{{ resolution }}</span>
              </div>
              <div class="metadata-item" v-if="duration">
                <span class="metadata-label">时长:</span>
                <span class="metadata-value">{{ formatDuration(duration) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="action-buttons">
        <div class="download-button" @click="downloadVideo" title="下载视频">
          <el-icon><Download /></el-icon>
        </div>
        <div class="close-button" @click="closeViewer">
          <el-icon><Close /></el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { Close, Download } from '@element-plus/icons-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  videoUrl: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  prompt: {
    type: String,
    default: ''
  },
  resolution: {
    type: String,
    default: ''
  },
  duration: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['close']);
const videoRef = ref(null);

// 关闭预览
const closeViewer = () => {
  if (videoRef.value) {
    videoRef.value.pause();
  }
  emit('close');
};

// 监听ESC键关闭预览
const handleKeyDown = (e) => {
  if (e.key === 'Escape' && props.visible) {
    closeViewer();
  }
};

// 格式化视频时长
const formatDuration = (seconds) => {
  if (!seconds) return '00:00';
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
};

// 下载视频
const downloadVideo = async () => {
  if (!props.videoUrl) {
    console.error('视频链接不存在');
    return;
  }

  try {
    // 使用fetch API获取视频文件
    const response = await fetch(props.videoUrl);

    // 检查响应状态
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`);
    }

    // 将响应转换为blob
    const blob = await response.blob();

    // 创建一个blob URL
    const blobUrl = URL.createObjectURL(blob);

    // 创建一个临时的a标签用于下载
    const link = document.createElement('a');
    link.href = blobUrl;

    // 获取文件名，优先使用title，最后使用默认名称
    const fileName = props.title || 'video';
    const fileExtension = props.videoUrl.split('.').pop() || 'mp4';
    link.download = `${fileName}.${fileExtension}`;
    link.style.display = 'none';
    document.body.appendChild(link);

    // 触发点击下载
    link.click();

    // 清理
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl); // 释放blob URL
    }, 100);

    console.log('视频下载已开始');
  } catch (error) {
    console.error('下载视频失败:', error);

    // 如果fetch失败，尝试直接在新标签页中打开视频
    window.open(props.videoUrl, '_blank');
  }
};

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal && videoRef.value) {
    // 当组件显示时，尝试播放视频
    setTimeout(() => {
      videoRef.value.play().catch(err => {
        console.warn('自动播放失败:', err);
      });
    }, 100);
  }
});

onMounted(() => {
  document.addEventListener('keydown', handleKeyDown);
});

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleKeyDown);
});
</script>

<style scoped>
.video-viewer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.viewer-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(4px);
}

.viewer-container {
  position: relative;
  width: 90%;
  max-width: 2000px;
  height: 90vh;
  max-height: 1000px;
  z-index: 1;
  display: flex;
  flex-direction: column;
}

.viewer-content {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 16px;
  overflow: hidden;
  background-color: rgb(30, 30, 30);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.3);
}

.video-section {
  flex: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  /* background-color: #000; */
  overflow: hidden;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.info-section {
  flex: 1;
  min-width: 420px;
  max-width: 500px;
  overflow-y: auto;
  background-color: rgba(40, 40, 40, 0.092);
  color: #fff;
  padding: 0;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  animation: slideIn 0.3s ease-out;
}

.info-content {
  padding: 20px;
}

.video-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: #fff;
}

.prompt-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 10px 0;
  color: rgba(255, 255, 255, 0.9);
}

.prompt-content {
  font-size: 14px;
  line-height: 1.6;
  margin: 0 0 20px 0;
  color: rgba(255, 255, 255, 0.8);
  white-space: pre-wrap;
  word-break: break-word;
  text-align: left;
}

.video-metadata {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.metadata-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.metadata-label {
  color: rgba(255, 255, 255, 0.6);
}

.metadata-value {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.action-buttons {
  position: absolute;
  top: -20px;
  right: -20px;
  display: flex;
  gap: 10px;
  z-index: 2;
}

.download-button,
.close-button {
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.download-button:hover,
.close-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.download-button {
  background-color: rgba(64, 158, 255, 0.8);
}

.download-button:hover {
  background-color: rgba(64, 158, 255, 1);
}

@keyframes slideIn {
  from {
    transform: translateX(30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .viewer-container {
    width: 95%;
    height: 95vh;
  }
  
  .viewer-content {
    flex-direction: column;
  }
  
  .video-section {
    flex: none;
    height: 40vh;
  }
  
  .info-section {
    flex: 1;
    max-width: 100%;
    width: 100%;
    border-left: none;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .action-buttons {
    top: 10px;
    right: 10px;
  }

  .download-button,
  .close-button {
    width: 36px;
    height: 36px;
  }
}
</style> 