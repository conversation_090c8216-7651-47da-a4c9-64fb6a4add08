<template>
  <div class="dropdown-panel-container" ref="containerRef">
    <!-- 触发器 -->
    <div class="dropdown-trigger" @click="toggleDropdown" ref="triggerRef">
      <slot name="trigger"></slot>
    </div>
    
    <!-- 下拉面板 - 传送门模式 -->
    <teleport :to="portalTarget" v-if="portal">
      <transition name="dropdown">
        <div
          v-show="isVisible"
          class="dropdown-content dropdown-portal"
          :class="[
            `position-${position}`,
            `align-${align}`,
            { 'dark-mode': isDarkMode }
          ]"
          :style="portalContentStyle"
          ref="contentRef"
        >
          <slot></slot>
        </div>
      </transition>
    </teleport>

    <!-- 下拉面板 - 普通模式 -->
    <transition name="dropdown" v-else>
      <div
        v-show="isVisible"
        class="dropdown-content"
        :class="[
          `position-${position}`,
          `align-${align}`,
          { 'dark-mode': isDarkMode }
        ]"
        :style="contentStyle"
        ref="contentRef"
      >
        <slot></slot>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue';
import { useDropdownManager } from '@/composables/useDropdownManager.js';

const props = defineProps({
  // 控制面板显示状态
  modelValue: {
    type: Boolean,
    default: false
  },
  // 弹出位置: 'bottom', 'left', 'right'
  position: {
    type: String,
    default: 'bottom',
    validator: (val) => ['bottom', 'left', 'right'].includes(val)
  },
  // 对齐方式: 'start', 'center', 'end'
  align: {
    type: String,
    default: 'start',
    validator: (val) => ['start', 'center', 'end'].includes(val)
  },
  // 点击外部区域是否关闭
  closeOnClickOutside: {
    type: Boolean,
    default: true
  },
  // 是否使用暗黑模式
  darkMode: {
    type: Boolean,
    default: false
  },
  // 内容宽度
  width: {
    type: [String, Number],
    default: null
  },
  // 最小宽度
  minWidth: {
    type: [String, Number],
    default: '100%'
  },
  // 最大宽度
  maxWidth: {
    type: [String, Number],
    default: null
  },
  // 最大高度
  maxHeight: {
    type: [String, Number],
    default: null
  },
  // z-index
  zIndex: {
    type: Number,
    default: 1000
  },
  // 下拉面板唯一标识（可选）
  dropdownId: {
    type: String,
    default: null
  },
  // 是否使用传送门模式（渲染到body，不受父容器限制）
  portal: {
    type: Boolean,
    default: true
  },
  // 传送门目标选择器
  portalTarget: {
    type: String,
    default: 'body'
  }
});

const emit = defineEmits(['update:modelValue', 'open', 'close']);

// 使用全局下拉面板管理器
const {
  dropdownId: managedDropdownId,
  register,
  unregister,
  activeDropdownId
} = useDropdownManager(props.dropdownId);

// 面板显示状态 - 基于全局管理器状态
const isVisible = computed(() => {
  return activeDropdownId.value === managedDropdownId;
});
// 容器引用
const containerRef = ref(null);
// 触发器引用
const triggerRef = ref(null);
// 内容引用
const contentRef = ref(null);
// 暗黑模式状态
const isDarkMode = computed(() => {
  return props.darkMode || document.body.classList.contains('dark');
});

// 计算内容样式 - 普通模式
const contentStyle = computed(() => {
  const style = {
    zIndex: props.zIndex
  };

  // 设置宽度
  if (props.width) {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width;
  }

  // 设置最小宽度
  if (props.minWidth) {
    style.minWidth = typeof props.minWidth === 'number' ? `${props.minWidth}px` : props.minWidth;
  }

  // 设置最大宽度
  if (props.maxWidth) {
    style.maxWidth = typeof props.maxWidth === 'number' ? `${props.maxWidth}px` : props.maxWidth;
  }

  // 设置最大高度
  if (props.maxHeight) {
    style.maxHeight = typeof props.maxHeight === 'number' ? `${props.maxHeight}px` : props.maxHeight;
    style.overflowY = 'auto';
  }

  return style;
});

// 传送门位置状态
const portalPosition = ref({ top: 0, left: 0 });

// 计算传送门内容样式
const portalContentStyle = computed(() => {
  const style = {
    position: 'fixed',
    zIndex: props.zIndex,
    top: `${portalPosition.value.top}px`,
    left: `${portalPosition.value.left}px`
  };

  // 设置宽度
  if (props.width) {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width;
  }

  // 设置最小宽度
  if (props.minWidth && props.minWidth !== '100%') {
    style.minWidth = typeof props.minWidth === 'number' ? `${props.minWidth}px` : props.minWidth;
  }

  // 设置最大宽度
  if (props.maxWidth) {
    style.maxWidth = typeof props.maxWidth === 'number' ? `${props.maxWidth}px` : props.maxWidth;
  }

  // 设置最大高度
  if (props.maxHeight) {
    style.maxHeight = typeof props.maxHeight === 'number' ? `${props.maxHeight}px` : props.maxHeight;
    style.overflowY = 'auto';
  }

  return style;
});

// 内部显示/隐藏方法
const showDropdown = () => {
  emit('update:modelValue', true);
  emit('open');
  nextTick(() => {
    adjustPosition();
  });
};

const hideDropdown = () => {
  emit('update:modelValue', false);
  emit('close');
};

// 监听 modelValue 变化，同步到全局管理器
watch(() => props.modelValue, (newVal) => {
  if (newVal && !isVisible.value) {
    // 如果父组件要求显示，通过全局管理器显示
    activeDropdownId.value = managedDropdownId;
  } else if (!newVal && isVisible.value) {
    // 如果父组件要求隐藏，通过全局管理器隐藏
    activeDropdownId.value = null;
  }
});

// 监听全局状态变化，同步到父组件
watch(isVisible, (newVal) => {
  if (newVal) {
    showDropdown();
  } else {
    hideDropdown();
  }
});

// 切换下拉面板显示状态
const toggleDropdown = (event) => {
  // 阻止事件冒泡，防止立即触发外部点击事件
  if (event) {
    event.stopPropagation();
  }

  if (isVisible.value) {
    // 当前显示，隐藏
    activeDropdownId.value = null;
  } else {
    // 当前隐藏，显示（会自动关闭其他面板）
    activeDropdownId.value = managedDropdownId;
  }
};

// 计算传送门位置
const calculatePortalPosition = () => {
  if (!triggerRef.value) return;

  const triggerRect = triggerRef.value.getBoundingClientRect();
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  let top = 0;
  let left = 0;

  // 根据位置计算基础坐标
  switch (props.position) {
    case 'bottom':
      top = triggerRect.bottom + 8;
      break;
    case 'left':
      top = triggerRect.top;
      left = triggerRect.left - 8;
      break;
    case 'right':
      top = triggerRect.top;
      left = triggerRect.right + 8;
      break;
  }

  // 根据对齐方式调整位置
  if (props.position === 'bottom') {
    switch (props.align) {
      case 'start':
        left = triggerRect.left;
        break;
      case 'center':
        left = triggerRect.left + (triggerRect.width / 2);
        break;
      case 'end':
        left = triggerRect.right;
        break;
    }
  } else {
    // left 或 right 位置的对齐
    switch (props.align) {
      case 'start':
        top = triggerRect.top;
        break;
      case 'center':
        top = triggerRect.top + (triggerRect.height / 2);
        break;
      case 'end':
        top = triggerRect.bottom;
        break;
    }
  }

  portalPosition.value = { top, left };
};

// 调整传送门位置以防止超出视口
const adjustPortalPosition = () => {
  if (!contentRef.value) return;

  const contentRect = contentRef.value.getBoundingClientRect();
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  let { top, left } = portalPosition.value;

  // 处理居中对齐的特殊情况
  if (props.position === 'bottom' && props.align === 'center') {
    left = left - (contentRect.width / 2);
  }

  // 检查右侧边界
  if (left + contentRect.width > viewportWidth) {
    left = viewportWidth - contentRect.width - 10;
  }

  // 检查左侧边界
  if (left < 10) {
    left = 10;
  }

  // 检查底部边界
  if (top + contentRect.height > viewportHeight && props.position === 'bottom') {
    const triggerRect = triggerRef.value.getBoundingClientRect();
    top = triggerRect.top - contentRect.height - 8;
  }

  // 检查顶部边界
  if (top < 10) {
    top = 10;
  }

  portalPosition.value = { top, left };
};

// 调整下拉面板位置 - 普通模式
const adjustPosition = () => {
  if (!isVisible.value || !contentRef.value || !triggerRef.value) return;

  if (props.portal) {
    // 传送门模式
    calculatePortalPosition();
    nextTick(() => {
      adjustPortalPosition();
    });
  } else {
    // 普通模式
    const triggerRect = triggerRef.value.getBoundingClientRect();

    // 如果是居中对齐且在底部弹出，需要手动调整位置
    if (props.align === 'center' && props.position === 'bottom') {
      const contentRect = contentRef.value.getBoundingClientRect();
      const leftOffset = (triggerRect.width - contentRect.width) / 2;
      contentRef.value.style.left = `${leftOffset}px`;
    }

    // 检查是否超出视口边界并调整
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    if (contentRef.value) {
      const contentRect = contentRef.value.getBoundingClientRect();

      // 检查右侧边界
      if (contentRect.right > viewportWidth) {
        const overflow = contentRect.right - viewportWidth;
        const currentLeft = parseFloat(contentRef.value.style.left || '0');
        contentRef.value.style.left = `${currentLeft - overflow - 10}px`;
      }

      // 检查左侧边界
      if (contentRect.left < 0) {
        contentRef.value.style.left = '0px';
      }

      // 检查底部边界
      if (contentRect.bottom > viewportHeight && props.position === 'bottom') {
        contentRef.value.style.top = 'auto';
        contentRef.value.style.bottom = '100%';
        contentRef.value.style.marginTop = '0';
        contentRef.value.style.marginBottom = '8px';
      }
    }
  }
};

// 处理点击外部区域关闭面板
const handleOutsideClick = (event) => {
  if (!props.closeOnClickOutside) return;

  if (isVisible.value) {
    const isClickInside = containerRef.value && containerRef.value.contains(event.target);
    const isClickInContent = contentRef.value && contentRef.value.contains(event.target);

    if (!isClickInside && !isClickInContent) {
      activeDropdownId.value = null;
    }
  }
};

// 处理窗口大小变化
const handleResize = () => {
  if (isVisible.value) {
    adjustPosition();
  }
};

// 组件挂载时添加事件监听和注册到全局管理器
onMounted(() => {
  document.addEventListener('click', handleOutsideClick);
  window.addEventListener('resize', handleResize);

  // 注册到全局管理器
  register({
    show: showDropdown,
    hide: hideDropdown
  });
});

// 组件卸载时移除事件监听和从全局管理器注销
onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick);
  window.removeEventListener('resize', handleResize);

  // 从全局管理器注销
  unregister();
});
</script>

<style scoped>
.dropdown-panel-container {
  position: relative;
  display: inline-block;
}

.dropdown-trigger {
  cursor: pointer;
  padding: 0px;
  margin: 0px;
}

.dropdown-content {
  position: absolute;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 12px rgba(0, 0, 0, 0.433);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  box-sizing: border-box;
}

/* 传送门模式样式 */
.dropdown-content.dropdown-portal {
  position: fixed;
  transform-origin: top left;
}

/* 暗黑模式样式 */
body.dark .dropdown-content {
  background-color: var(--bg-card, #1e1e2d) !important;
  border-color: var(--border-color, #2d2d3f) !important;
  box-shadow: 0 1px 12px rgb(53, 102, 170);
}

/* 位置样式 */
.dropdown-content.position-bottom {
  top: 100%;
  margin-top: 8px;
  transform-origin: top center;
}

.dropdown-content.position-left {
  top: 0;
  right: 100%;
  margin-right: 8px;
  transform-origin: center right;
}

.dropdown-content.position-right {
  top: 0;
  left: 100%;
  margin-left: 8px;
  transform-origin: center left;
}

/* 对齐方式 */
.dropdown-content.position-bottom.align-start {
  left: 0;
}

.dropdown-content.position-bottom.align-center {
  /* 居中对齐由 JS 动态计算 */
  transform-origin: top center;
}

.dropdown-content.position-bottom.align-end {
  right: 0;
  left: auto;
}

.dropdown-content.position-left.align-start,
.dropdown-content.position-right.align-start {
  top: 0;
}

.dropdown-content.position-left.align-center,
.dropdown-content.position-right.align-center {
  top: 50%;
  transform: translateY(-50%);
}

.dropdown-content.position-left.align-end,
.dropdown-content.position-right.align-end {
  bottom: 0;
  top: auto;
}

/* 过渡动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
}

/* 根据位置设置不同的进入动画 */
.position-bottom.dropdown-enter-from,
.position-bottom.dropdown-leave-to {
  transform: translateY(-10px);
}

.position-left.dropdown-enter-from,
.position-left.dropdown-leave-to {
  transform: translateX(10px);
}

.position-right.dropdown-enter-from,
.position-right.dropdown-leave-to {
  transform: translateX(-10px);
}
</style> 