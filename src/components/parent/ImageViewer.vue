<template>
  <div class="image-viewer" v-if="visible" @click="closeViewer">
    <div class="viewer-backdrop"></div>
    <div class="viewer-container">
      <div class="viewer-content">
        <!-- 图片显示区域 -->
        <div class="image-section">
          <img :src="imageUrl" :alt="alt" @click.stop class="image-element" />
        </div>
        
        <!-- 图片信息区域，仅当有描述或标题时显示 -->
        <div v-if="hasDescription" class="info-section" @click.stop>
          <div class="info-content">
            <h3 class="image-title" v-if="displayTitle">{{ displayTitle }}</h3>
            
            <div class="image-description" v-if="description">
              <p class="description-content">{{ description }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="action-buttons">
        <div class="download-button" @click="downloadImage" title="下载图片">
          <el-icon><Download /></el-icon>
        </div>
        <div class="close-button" @click="closeViewer">
          <el-icon><Close /></el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { Close, Download } from '@element-plus/icons-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  imageUrl: {
    type: String,
    default: ''
  },
  alt: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  }
});

// 计算属性：是否有描述信息
const hasDescription = computed(() => {
  return props.description || props.title || props.alt;
});

// 计算属性：显示的标题
const displayTitle = computed(() => {
  return props.title || props.alt;
});

const emit = defineEmits(['close']);

const closeViewer = () => {
  emit('close');
};

// 下载图片
const downloadImage = async () => {
  if (!props.imageUrl) {
    console.error('图片链接不存在');
    return;
  }

  try {
    // 使用fetch API获取图片文件
    const response = await fetch(props.imageUrl);

    // 检查响应状态
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`);
    }

    // 将响应转换为blob
    const blob = await response.blob();

    // 创建一个blob URL
    const blobUrl = URL.createObjectURL(blob);

    // 创建一个临时的a标签用于下载
    const link = document.createElement('a');
    link.href = blobUrl;

    // 获取文件名，优先使用title，其次使用alt，最后使用默认名称
    const fileName = props.title || props.alt || 'image';
    const fileExtension = props.imageUrl.split('.').pop() || 'jpg';
    link.download = `${fileName}.${fileExtension}`;
    link.style.display = 'none';
    document.body.appendChild(link);

    // 触发点击下载
    link.click();

    // 清理
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl); // 释放blob URL
    }, 100);

    console.log('图片下载已开始');
  } catch (error) {
    console.error('下载图片失败:', error);

    // 如果fetch失败，尝试直接在新标签页中打开图片
    window.open(props.imageUrl, '_blank');
  }
};
</script>

<style scoped>
.image-viewer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.viewer-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(4px);
}

.viewer-container {
  position: relative;
  width: 90%;
  max-width: 1400px;
  height: 90vh;
  max-height: 800px;
  z-index: 1;
  display: flex;
  flex-direction: column;
}

.viewer-content {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 16px;
  overflow: hidden;
  background-color: rgb(30, 30, 30);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.3);
}

.image-section {
  flex: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.image-element {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.info-section {
  flex: 1;
  min-width: 420px;
  max-width: 500px;
  overflow-y: auto;
  background-color: rgba(40, 40, 40, 0.092);
  color: #fff;
  padding: 0;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  animation: slideIn 0.3s ease-out;
}

.info-content {
  padding: 20px;
}

.image-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 20px 0;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: #fff;
}

.description-content {
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
  color: rgba(255, 255, 255, 0.8);
  white-space: pre-wrap;
  word-break: break-word;
  text-align: left;
}

.action-buttons {
  position: absolute;
  top: -20px;
  right: -20px;
  display: flex;
  gap: 10px;
  z-index: 2;
}

.download-button,
.close-button {
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.download-button:hover,
.close-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.download-button {
  background-color: rgba(64, 158, 255, 0.8);
}

.download-button:hover {
  background-color: rgba(64, 158, 255, 1);
}

@keyframes slideIn {
  from {
    transform: translateX(30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .viewer-container {
    width: 95%;
    height: 95vh;
  }
  
  .viewer-content {
    flex-direction: column;
  }
  
  .image-section {
    flex: none;
    height: 60vh;
  }
  
  .info-section {
    flex: 1;
    max-width: 100%;
    width: 100%;
    min-width: unset;
    border-left: none;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .action-buttons {
    top: 10px;
    right: 10px;
  }

  .download-button,
  .close-button {
    width: 36px;
    height: 36px;
  }
}
</style> 