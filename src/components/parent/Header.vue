<template>
  <header ref="headerRef" class="app-header"
    :class="{ 'transparent': transparent, 'transparent-strong': transparentStrong, 'narrow-header': isNarrowHeader }">
    <!-- <div class="logo" @click="goToHome" v-if="!hideLogo">
      <img src="@/assets/logo.svg" alt="Logo" class="logo-img" />
      <span class="logo-text">{{ props.hideTitle }}</span>
    </div> -->

    <!-- 返回按钮 -->
    <div class="back-button" @click="goBack" v-if="backButton">
      <el-icon>
        <ArrowLeft />
      </el-icon>
      <span class="back-button-text">返回</span>
    </div>

    <div class="user-info" v-if="user && isUser">

      <div class="nav-buttons">
        <!-- <el-button class="nav-button" type="text" @click="goToStories">故事</el-button> -->
        <!-- <el-button class="nav-button" type="primary" @click="goToCreate">创建</el-button> -->
        <ThemeSwitch :is-transparent="transparent || transparentStrong" />
      </div>

      <div class="points-display" @click="goToProfile">
        <el-icon>
          <Coin />
        </el-icon>
        <span class="points-value">{{ formatPoints(userPoints) }}</span>
      </div>
      
      <div class="nav-menu">
        <!-- <el-button v-if="!isNarrowHeader" class="nav-menu-item" :class="{ 'active': isCurrentRoute('/home') }" type="text" @click="goToHome">首页</el-button> -->
        <!-- <el-button v-if="!isNarrowHeader" class="nav-menu-item" :class="{ 'active': isCurrentRoute('/featured-works') }" type="text" @click="goToFeaturedWorks">精选作品</el-button>
        <el-button v-if="!isNarrowHeader" class="nav-menu-item" :class="{ 'active': isCurrentRoute('/stories') }" type="text" @click="goToStories">我的故事</el-button>
        <el-button v-if="!isNarrowHeader" class="nav-menu-item" :class="{ 'active': isCurrentRoute('/inputSection') }" type="text" @click="goToCreate">创作故事</el-button> -->
        <el-dropdown class="user-dropdown">
          <span class="user-name" :class="{ 'narrow-user-name': isNarrowHeader }">
            <!-- {{ user.nickname }} -->
            <div class="avatar-container">
              <!-- 校验url -->
              <img v-if="user.avatar && user.avatar.startsWith('http')" :src="user.avatar" alt="用户头像" class="user-avatar" />
              <div v-else class="avatar-placeholder">
                {{ userInitials }}
              </div>
            </div>
            <!-- <el-icon><arrow-down /></el-icon> -->
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <template v-if="isNarrowHeader">
                <!-- <el-dropdown-item :class="{ 'active-dropdown-item': isCurrentRoute('/home') }" @click="goToHome">首页</el-dropdown-item> -->
                <!-- <el-dropdown-item :class="{ 'active-dropdown-item': isCurrentRoute('/featured-works') }" @click="goToFeaturedWorks">精选作品</el-dropdown-item>
                <el-dropdown-item :class="{ 'active-dropdown-item': isCurrentRoute('/inputSection') }" @click="goToCreate">创作故事</el-dropdown-item>
                <el-dropdown-item :class="{ 'active-dropdown-item': isCurrentRoute('/stories') }"divided  @click="goToStories">我的故事</el-dropdown-item> -->
              </template>
              <!-- divided  -->
              <el-dropdown-item :class="{ 'active-dropdown-item': isCurrentRoute('/profile') }"
                @click="goToProfile">个人中心</el-dropdown-item>
              <el-dropdown-item divided @click="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

    </div>
  </header>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, inject } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowDown, Coin, ArrowLeft } from '@element-plus/icons-vue'
import ThemeSwitch from '@/components/selector/ThemeSwitch.vue'
import { ElMessageBox } from 'element-plus'

const props = defineProps({
  hideTitle: {
    type: String,
    default: "幻流"
  },
  hideLogo: {
    type: Boolean,
    default: false
  },
  transparent: {
    type: Boolean,
    default: false
  },
  transparentStrong: {
    type: Boolean,
    default: false
  },
  isUser: {
    type: Boolean,
    default: true
  },
  backButton: {
    type: Boolean,
    default: false
  }
});

const router = useRouter()
const route = useRoute()
const goBack = () => {
  console.log('route.path', route.path)
  if (route.path.includes('/create')) {
    ElMessageBox.confirm('确定要退出创作吗？', '', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      router.back()
    })
  } else {
    router.back()
  }
}

const user = ref(null)
const headerRef = ref(null)
const isNarrowHeader = ref(false)
let resizeObserver = null

// 获取当前路由路径
const currentPath = computed(() => route.path)

// 判断是否为当前路由
const isCurrentRoute = (path) => {
  return currentPath.value === path
}

// 从App.vue注入积分相关的变量和函数
const userPoints = inject('userPoints')
const updateUserPoints = inject('updateUserPoints')
const formatNumber = inject('formatNumber')

// 格式化积分显示
const formatPoints = (points) => {
  return formatNumber(points)
}

// 监控header宽度变化
const observeHeaderWidth = () => {
  if (!headerRef.value) return

  resizeObserver = new ResizeObserver(entries => {
    for (const entry of entries) {
      const headerWidth = entry.contentRect.width
      isNarrowHeader.value = headerWidth < 700
    }
  })

  resizeObserver.observe(headerRef.value)
}

// 计算头像占位符
const userInitials = computed(() => {
  const name = user.value.nickname || ''
  return name.substring(0, 1).toUpperCase()
})

onMounted(() => {
  const userInfo = localStorage.getItem('userInfo')
  if (userInfo) {
    user.value = JSON.parse(userInfo)
    // 如果用户没有等级信息，默认设置为黄金等级
    if (!user.value.level) {
      user.value.level = 'gold' // 可选值: 'gold', 'diamond', 'platinum'
    }
  }

  // 监听积分更新事件
  window.addEventListener('user-points-updated', handlePointsUpdated)

  // 监听用户信息更新事件
  window.addEventListener('user-info-updated', handleUserInfoUpdated)

  // 初始化ResizeObserver
  observeHeaderWidth()
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('user-points-updated', handlePointsUpdated)
  window.removeEventListener('user-info-updated', handleUserInfoUpdated)

  // 清理ResizeObserver
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})

// 处理积分更新事件
const handlePointsUpdated = (event) => {
  // 可以在这里添加积分变化的动画或其他效果
  console.log('积分已更新:', event.detail.points)
}

// 处理用户信息更新事件
const handleUserInfoUpdated = (event) => {
  console.log('用户信息已更新:', event.detail)
  // 从localStorage重新获取用户信息
  const userInfo = localStorage.getItem('userInfo')
  if (userInfo) {
    user.value = JSON.parse(userInfo)
  }
}

const goToHome = () => {
  router.push('/home')
}

const goToFeaturedWorks = () => {
  router.push('/featured-works')
}

const goToCreate = () => {
  router.push('/inputSection')
}

const goToStories = () => {
  router.push('/stories')
}

const goToProfile = () => {
  router.push('/profile')
}

const logout = () => {
  localStorage.removeItem('token')
  localStorage.removeItem('userInfo')
  router.push('/login')
}
</script>

<style scoped>
.app-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* height: 60px; */
  padding: 0px 10px;
  background-color: #fff;
  /* box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1); */
  transition: background-color 0.3s, box-shadow 0.3s;
  box-sizing: border-box;
  height: 50px;
}


.avatar-container {
  position: relative;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid rgba(99, 102, 241, 0.2);
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.avatar-placeholder {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  border: 3px solid rgba(99, 102, 241, 0.2);
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}


.back-button {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  font-size: 14px;
  color: #ffffff;
  transition: color 0.3s;
  background-color: #706cefd7;
  padding: 5px 10px;
  border-radius: 5px;
  z-index: 2;
}

:root.dark .app-header,
body.dark .app-header {
  background-color: var(--bg-card);
  /* box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2); */
}

.app-header.transparent {
  background-color: rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(5px) !important;
  box-shadow: none !important;
}

body.dark .app-header.transparent {
  background-color: rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(5px) !important;
  box-shadow: none !important;
}

.app-header.transparent-strong {
  background-color: transparent !important;
  backdrop-filter: none !important;
  box-shadow: none !important;
}

body.dark .app-header.transparent-strong {
  background-color: transparent !important;
  backdrop-filter: none !important;
  box-shadow: none !important;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-img {
  height: 32px;
  margin-right: 10px;
}

.logo-text {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  transition: color 0.3s;
}

:root.dark .logo-text,
body.dark .logo-text {
  color: var(--text-primary);
}

.transparent .logo-text {
  color: #333;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

body.dark .transparent .logo-text {
  color: #fff;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.nav-buttons {
  display: flex;
  align-items: center;
  gap: 16px;
}

.nav-button.el-button--text {
  font-size: 15px;
  color: #606266;
  font-weight: 500;
}

.transparent .nav-button.el-button--text {
  color: #333;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

body.dark .transparent .nav-button.el-button--text {
  color: #fff;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.nav-button.el-button--text:hover {
  color: #409eff;
}

.nav-button.el-button--primary {
  border-radius: 4px;
  padding: 8px 16px;
  font-weight: 500;
}

.user-info {
  display: flex;
  align-items: center;
  /* background-color: rgba(255, 255, 255, 0.9); */
  padding: 8px;
  border-radius: 6px;
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
  margin-left: auto;
  margin-right: 0px;
  gap: 10px;
}

.points-display {
  display: flex;
  align-items: center;
  padding: 4px 10px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  border-radius: 20px;
  border: 1px solid rgba(99, 102, 241, 0.2);
  color: #333;
  transition: all 0.3s;
  cursor: pointer;
  z-index: 1;
}

.points-display:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.15), rgba(139, 92, 246, 0.15));
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.2);
}

body.dark .points-display:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.25), rgba(139, 92, 246, 0.25));
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.points-display .el-icon {
  color: #6366f1;
  margin-right: 6px;
  font-size: 14px;
}

.points-value {
  font-size: 14px;
  font-weight: 600;
}

body.dark .points-display {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(139, 92, 246, 0.2));
  border: 1px solid rgba(99, 102, 241, 0.3);
  color: var(--text-primary);
}

.transparent .points-display {
  background: rgba(255, 255, 255, 0.3);
  color: #333;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
  border-color: rgba(255, 255, 255, 0.3);
}

body.dark .transparent .points-display {
  background: rgba(0, 0, 0, 0.3);
  color: #fff;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  border-color: rgba(255, 255, 255, 0.1);
}

.transparent .points-display .el-icon {
  color: #333;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

body.dark .transparent .points-display .el-icon {
  color: #fff;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.user-name {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
  transition: color 0.3s, background-color 0.3s, padding 0.3s;
  font-weight: 600;
  cursor: pointer;
}

.narrow-user-name {
  padding: 4px 8px;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
}

.narrow-user-name:hover {
  background-color: rgba(64, 158, 255, 0.2);
}

body.dark .narrow-user-name {
  background-color: rgba(64, 158, 255, 0.2);
}

body.dark .narrow-user-name:hover {
  background-color: rgba(64, 158, 255, 0.3);
}

.crown-icon {
  display: inline-flex;
  align-items: center;
  margin-left: 4px;
  margin-right: 2px;
}

.crown-gold svg path:first-child {
  fill: #FFD700;
}

.crown-diamond svg path:first-child {
  fill: #B9F2FF;
}

.crown-platinum svg path:first-child {
  fill: #E5E4E2;
}

:root.dark .user-name,
body.dark .user-name {
  color: var(--text-primary);
}

.transparent .user-name {
  color: #333;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

body.dark .transparent .user-name {
  color: #fff;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.user-name .el-icon {
  margin-left: 5px;
  font-size: 12px;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 2;
}

.nav-menu-item.el-button--text {
  font-size: 14px;
  color: #606266;
  padding: 4px 8px;
  transition: all 0.3s;
}

.nav-menu-item.el-button--text:hover {
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
}

/* 选中状态样式 */
.nav-menu-item.active {
  color: #409eff !important;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
  font-weight: 500;
}

/* 下拉菜单选中状态样式 */
:deep(.active-dropdown-item) {
  color: #409eff !important;
  background-color: rgba(64, 158, 255, 0.1) !important;
}

.transparent .nav-menu-item.el-button--text {
  color: #333;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

/* 透明模式下的选中状态 */
.transparent .nav-menu-item.active {
  color: #409eff !important;
  background-color: rgba(255, 255, 255, 0.2);
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

body.dark .transparent .nav-menu-item.el-button--text {
  color: #fff;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

/* 暗色模式下的选中状态 */
body.dark .nav-menu-item.active {
  color: #409eff !important;
  background-color: rgba(64, 158, 255, 0.2);
}

body.dark .transparent .nav-menu-item.active {
  background-color: rgba(0, 0, 0, 0.3);
}

:root.dark .nav-menu-item.el-button--text,
body.dark .nav-menu-item.el-button--text {
  color: var(--text-primary);
}

.user-dropdown {
  margin-left: 8px;
}

.narrow-header .user-dropdown {
  margin-left: 0;
}

@media (max-width: 768px) {
  .nav-buttons {
    gap: 8px;
    margin-right: 10px;
  }

  .nav-button.el-button--primary {
    padding: 6px 12px;
    font-size: 14px;
  }

  .user-info {
    gap: 8px;
  }

  .points-display {
    padding: 3px 8px;
  }

  .points-value {
    font-size: 12px;
  }

  .nav-menu {
    gap: 4px;
  }

  .nav-menu-item.el-button--text {
    font-size: 12px;
    padding: 2px 6px;
  }
}
</style>