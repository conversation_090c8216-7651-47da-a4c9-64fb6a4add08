<template>
  <div class="demo-container">
    <div class="demo-header">
      <h1>分享弹框演示</h1>
      <p>点击下方按钮查看分享弹框效果</p>
    </div>

    <div class="demo-content">
      <div class="video-card-demo">
        <div class="video-preview">
          <img 
            :src="demoVideo.videoUrl + '?x-oss-process=video/snapshot,t_0,f_jpg'" 
            alt="演示视频封面"
            class="thumbnail"
          />
          <div class="duration">{{ formatDuration(demoVideo.videoDuration) }}</div>
          <div class="play-icon">
            <el-icon><VideoPlay /></el-icon>
          </div>
          <div class="share-icon" @click="showShareDialog">
            <el-icon><Share /></el-icon>
          </div>
        </div>
        <div class="video-info">
          <div class="video-meta">
            <span>演示视频</span>
            <span>{{ formatDate(demoVideo.createTime) }}</span>
          </div>
        </div>
      </div>

      <div class="demo-buttons">
        <button class="demo-btn primary" @click="showShareDialog">
          <el-icon><Share /></el-icon>
          显示分享弹框
        </button>
        <button class="demo-btn" @click="toggleShareStatus">
          {{ demoVideo.shareStatus === 1 ? '模拟取消分享' : '模拟已分享状态' }}
        </button>
      </div>
    </div>

    <!-- 分享弹框 -->
    <ShareDialog 
      v-if="shareDialogVisible" 
      :visible="shareDialogVisible"
      :video="demoVideo"
      @close="closeShareDialog"
      @share="handleShareAction"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { VideoPlay, Share } from '@element-plus/icons-vue';
import ShareDialog from './videoEdit/ShareDialog.vue';

// 演示数据
const demoVideo = ref({
  id: 'demo-123',
  videoUrl: 'https://example.com/demo-video.mp4',
  videoDuration: 125000, // 2分5秒
  createTime: Date.now() - 86400000, // 1天前
  resolution: '1080p',
  shareStatus: 0, // 0-未分享, 1-已分享
  shareUrl: ''
});

// 分享弹框状态
const shareDialogVisible = ref(false);

// 显示分享弹框
const showShareDialog = () => {
  shareDialogVisible.value = true;
};

// 关闭分享弹框
const closeShareDialog = () => {
  shareDialogVisible.value = false;
};

// 处理分享操作
const handleShareAction = (action) => {
  if (action === 'share') {
    // 模拟创建分享链接
    demoVideo.value.shareStatus = 1;
    demoVideo.value.shareUrl = 'https://example.com/share/demo-123';
    ElMessage.success('分享链接创建成功！');
  } else if (action === 'unshare') {
    // 模拟取消分享
    demoVideo.value.shareStatus = 0;
    demoVideo.value.shareUrl = '';
    ElMessage.success('已取消分享！');
  }
  closeShareDialog();
};

// 切换分享状态（用于演示）
const toggleShareStatus = () => {
  if (demoVideo.value.shareStatus === 1) {
    demoVideo.value.shareStatus = 0;
    demoVideo.value.shareUrl = '';
  } else {
    demoVideo.value.shareStatus = 1;
    demoVideo.value.shareUrl = 'https://example.com/share/demo-123';
  }
};

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  if (date.getFullYear() === new Date().getFullYear()) {
    return `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  } else {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  }
};

// 格式化视频时长
const formatDuration = (milliseconds) => {
  if (!milliseconds) return '00:00';
  const seconds = milliseconds / 1000;
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
};
</script>

<style scoped>
.demo-container {
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.demo-header h1 {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 16px 0;
}

.demo-header p {
  font-size: 18px;
  opacity: 0.9;
  margin: 0;
}

.demo-content {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 32px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.video-card-demo {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.video-preview {
  position: relative;
  aspect-ratio: 16/9;
  overflow: hidden;
  background-color: #000;
}

.thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.video-card-demo:hover .thumbnail {
  transform: scale(1.05);
}

.duration {
  position: absolute;
  top: 12px;
  left: 12px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  opacity: 0;
  transition: opacity 0.3s;
}

.share-icon {
  position: absolute;
  bottom: 12px;
  right: 12px;
  width: 40px;
  height: 40px;
  background-color: rgba(34, 197, 94, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s;
  opacity: 0;
}

.share-icon:hover {
  background-color: rgba(34, 197, 94, 1);
  transform: scale(1.1);
}

.video-card-demo:hover .play-icon,
.video-card-demo:hover .share-icon {
  opacity: 1;
}

.video-info {
  padding: 16px;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #666;
}

.demo-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.demo-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.demo-btn.primary {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  color: white;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.demo-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
}

.demo-btn:not(.primary) {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.demo-btn:not(.primary):hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-container {
    padding: 20px;
  }
  
  .demo-content {
    padding: 20px;
  }
  
  .demo-buttons {
    flex-direction: column;
  }
  
  .demo-btn {
    justify-content: center;
  }
}
</style>
