<template>
  <div class="dropdown-test-container">
    <h2>DropdownPanel 互斥显示测试</h2>
    
    <div class="test-section">
      <h3>基本测试 - 多个面板互斥显示</h3>
      <div class="panel-row">
        <!-- 面板1 -->
        <DropdownPanel 
          v-model="showPanel1" 
          dropdown-id="test-panel-1"
          position="bottom"
          align="start"
          :width="200"
        >
          <template #trigger>
            <button class="test-button" :class="{ active: showPanel1 }">
              面板1 {{ showPanel1 ? '(打开)' : '(关闭)' }}
            </button>
          </template>
          
          <div class="panel-content">
            <h4>面板1内容</h4>
            <p>这是第一个下拉面板的内容</p>
            <button @click="showPanel1 = false">关闭面板</button>
          </div>
        </DropdownPanel>

        <!-- 面板2 -->
        <DropdownPanel 
          v-model="showPanel2" 
          dropdown-id="test-panel-2"
          position="bottom"
          align="start"
          :width="200"
        >
          <template #trigger>
            <button class="test-button" :class="{ active: showPanel2 }">
              面板2 {{ showPanel2 ? '(打开)' : '(关闭)' }}
            </button>
          </template>
          
          <div class="panel-content">
            <h4>面板2内容</h4>
            <p>这是第二个下拉面板的内容</p>
            <button @click="showPanel2 = false">关闭面板</button>
          </div>
        </DropdownPanel>

        <!-- 面板3 -->
        <DropdownPanel 
          v-model="showPanel3" 
          dropdown-id="test-panel-3"
          position="bottom"
          align="start"
          :width="200"
        >
          <template #trigger>
            <button class="test-button" :class="{ active: showPanel3 }">
              面板3 {{ showPanel3 ? '(打开)' : '(关闭)' }}
            </button>
          </template>
          
          <div class="panel-content">
            <h4>面板3内容</h4>
            <p>这是第三个下拉面板的内容</p>
            <button @click="showPanel3 = false">关闭面板</button>
          </div>
        </DropdownPanel>
      </div>
    </div>

    <div class="test-section">
      <h3>程序化控制测试</h3>
      <div class="control-buttons">
        <button @click="openPanel1">打开面板1</button>
        <button @click="openPanel2">打开面板2</button>
        <button @click="openPanel3">打开面板3</button>
        <button @click="closeAllPanels" class="close-all">关闭所有面板</button>
      </div>
    </div>

    <div class="test-section">
      <h3>状态监控</h3>
      <div class="status-info">
        <p><strong>当前活跃面板ID:</strong> {{ activeDropdownId || '无' }}</p>
        <p><strong>面板1状态:</strong> {{ showPanel1 ? '显示' : '隐藏' }}</p>
        <p><strong>面板2状态:</strong> {{ showPanel2 ? '显示' : '隐藏' }}</p>
        <p><strong>面板3状态:</strong> {{ showPanel3 ? '显示' : '隐藏' }}</p>
      </div>
    </div>

    <div class="test-section">
      <h3>不同位置测试</h3>
      <div class="position-test">
        <!-- 左侧弹出 -->
        <DropdownPanel 
          v-model="showLeftPanel" 
          dropdown-id="test-left-panel"
          position="left"
          align="center"
          :width="150"
        >
          <template #trigger>
            <button class="test-button">左侧弹出</button>
          </template>
          <div class="panel-content">左侧面板内容</div>
        </DropdownPanel>

        <!-- 右侧弹出 -->
        <DropdownPanel 
          v-model="showRightPanel" 
          dropdown-id="test-right-panel"
          position="right"
          align="center"
          :width="150"
        >
          <template #trigger>
            <button class="test-button">右侧弹出</button>
          </template>
          <div class="panel-content">右侧面板内容</div>
        </DropdownPanel>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import DropdownPanel from '@/components/parent/DropdownPanel.vue';
import { useDropdownManager } from '@/composables/useDropdownManager.js';

// 面板状态
const showPanel1 = ref(false);
const showPanel2 = ref(false);
const showPanel3 = ref(false);
const showLeftPanel = ref(false);
const showRightPanel = ref(false);

// 获取全局管理器
const { activeDropdownId, hideAll } = useDropdownManager();

// 程序化控制方法
const openPanel1 = () => {
  showPanel1.value = true;
};

const openPanel2 = () => {
  showPanel2.value = true;
};

const openPanel3 = () => {
  showPanel3.value = true;
};

const closeAllPanels = () => {
  hideAll();
};

// 监听活跃面板变化
watch(activeDropdownId, (newId) => {
  console.log('活跃面板变化:', newId);
});
</script>

<style scoped>
.dropdown-test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #f9fafb;
}

.test-section h3 {
  margin-top: 0;
  color: #374151;
}

.panel-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.test-button {
  padding: 10px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s;
}

.test-button:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.test-button.active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.panel-content {
  padding: 16px;
  min-width: 180px;
}

.panel-content h4 {
  margin: 0 0 8px 0;
  color: #374151;
}

.panel-content p {
  margin: 0 0 12px 0;
  color: #6b7280;
  font-size: 14px;
}

.panel-content button {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  font-size: 12px;
}

.control-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.control-buttons button {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: #fff;
  cursor: pointer;
  transition: background-color 0.2s;
}

.control-buttons button:hover {
  background-color: #f3f4f6;
}

.control-buttons .close-all {
  background-color: #ef4444;
  color: white;
  border-color: #ef4444;
}

.control-buttons .close-all:hover {
  background-color: #dc2626;
}

.status-info {
  background-color: #fff;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.status-info p {
  margin: 8px 0;
  font-family: monospace;
}

.position-test {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
  min-height: 100px;
}
</style>
