<template>
  <div class="portal-test-container">
    <h2>DropdownPanel 传送门模式测试</h2>
    
    <div class="test-section">
      <h3>对比测试 - 传送门 vs 普通模式</h3>
      
      <!-- 有 overflow:hidden 限制的容器 -->
      <div class="overflow-container">
        <h4>容器有 overflow:hidden 限制</h4>
        
        <div class="test-row">
          <!-- 传送门模式 -->
          <DropdownPanel 
            v-model="showPortalPanel" 
            dropdown-id="portal-test"
            :portal="true"
            position="bottom"
            align="start"
            :width="250"
          >
            <template #trigger>
              <button class="test-button portal">
                传送门模式 {{ showPortalPanel ? '(打开)' : '(关闭)' }}
              </button>
            </template>
            
            <div class="panel-content large">
              <h4>传送门模式</h4>
              <p>这个面板使用传送门模式，不会被父容器的 overflow:hidden 裁剪</p>
              <div class="content-box">
                <p>内容区域1</p>
                <p>内容区域2</p>
                <p>内容区域3</p>
                <p>内容区域4</p>
              </div>
              <button @click="showPortalPanel = false">关闭面板</button>
            </div>
          </DropdownPanel>

          <!-- 普通模式 -->
          <DropdownPanel 
            v-model="showNormalPanel" 
            dropdown-id="normal-test"
            :portal="false"
            position="bottom"
            align="start"
            :width="250"
          >
            <template #trigger>
              <button class="test-button normal">
                普通模式 {{ showNormalPanel ? '(打开)' : '(关闭)' }}
              </button>
            </template>
            
            <div class="panel-content large">
              <h4>普通模式</h4>
              <p>这个面板使用普通模式，可能会被父容器裁剪</p>
              <div class="content-box">
                <p>内容区域1</p>
                <p>内容区域2</p>
                <p>内容区域3</p>
                <p>内容区域4</p>
              </div>
              <button @click="showNormalPanel = false">关闭面板</button>
            </div>
          </DropdownPanel>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>z-index 层级测试</h3>
      
      <div class="z-index-container">
        <div class="high-z-index-element">
          高 z-index 元素 (z-index: 999)
        </div>
        
        <DropdownPanel 
          v-model="showZIndexPanel" 
          dropdown-id="zindex-test"
          :portal="true"
          :z-index="1000"
          position="bottom"
          align="center"
        >
          <template #trigger>
            <button class="test-button zindex">
              z-index 测试 {{ showZIndexPanel ? '(打开)' : '(关闭)' }}
            </button>
          </template>
          
          <div class="panel-content">
            <h4>z-index 测试</h4>
            <p>这个面板应该显示在高 z-index 元素之上</p>
            <button @click="showZIndexPanel = false">关闭面板</button>
          </div>
        </DropdownPanel>
      </div>
    </div>

    <div class="test-section">
      <h3>自定义传送门目标</h3>
      
      <DropdownPanel 
        v-model="showCustomTargetPanel" 
        dropdown-id="custom-target-test"
        :portal="true"
        portal-target="#custom-portal-container"
        position="bottom"
        align="start"
      >
        <template #trigger>
          <button class="test-button custom">
            自定义目标 {{ showCustomTargetPanel ? '(打开)' : '(关闭)' }}
          </button>
        </template>
        
        <div class="panel-content">
          <h4>自定义传送门目标</h4>
          <p>这个面板渲染到自定义容器中</p>
          <button @click="showCustomTargetPanel = false">关闭面板</button>
        </div>
      </DropdownPanel>
    </div>

    <div class="control-section">
      <h3>程序化控制</h3>
      <div class="control-buttons">
        <button @click="openPortalPanel" class="control-btn">打开传送门面板</button>
        <button @click="openNormalPanel" class="control-btn">打开普通面板</button>
        <button @click="closeAllPanels" class="control-btn danger">关闭所有面板</button>
      </div>
    </div>

    <div class="info-section">
      <h3>当前状态</h3>
      <div class="status-info">
        <p>传送门面板: {{ showPortalPanel ? '打开' : '关闭' }}</p>
        <p>普通面板: {{ showNormalPanel ? '打开' : '关闭' }}</p>
        <p>z-index 面板: {{ showZIndexPanel ? '打开' : '关闭' }}</p>
        <p>自定义目标面板: {{ showCustomTargetPanel ? '打开' : '关闭' }}</p>
        <p>活跃面板ID: {{ activeDropdownId || '无' }}</p>
      </div>
    </div>
  </div>

  <!-- 自定义传送门容器 -->
  <div id="custom-portal-container" class="custom-portal-container"></div>
</template>

<script setup>
import { ref, watch } from 'vue';
import DropdownPanel from '@/components/parent/DropdownPanel.vue';
import { useDropdownManager } from '@/composables/useDropdownManager.js';

// 面板状态
const showPortalPanel = ref(false);
const showNormalPanel = ref(false);
const showZIndexPanel = ref(false);
const showCustomTargetPanel = ref(false);

// 获取全局管理器
const { activeDropdownId, hideAll } = useDropdownManager();

// 程序化控制方法
const openPortalPanel = () => {
  showPortalPanel.value = true;
};

const openNormalPanel = () => {
  showNormalPanel.value = true;
};

const closeAllPanels = () => {
  hideAll();
};

// 监听活跃面板变化
watch(activeDropdownId, (newId) => {
  console.log('活跃面板变化:', newId);
});
</script>

<style scoped>
.portal-test-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #fff;
}

body.dark .test-section {
  background-color: var(--bg-card);
  border-color: var(--border-color);
}

.overflow-container {
  height: 200px;
  overflow: hidden;
  border: 2px dashed #f59e0b;
  border-radius: 8px;
  padding: 20px;
  background-color: #fef3c7;
  position: relative;
}

body.dark .overflow-container {
  background-color: rgba(245, 158, 11, 0.1);
  border-color: #f59e0b;
}

.test-row {
  display: flex;
  gap: 20px;
  margin-top: 15px;
}

.test-button {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s;
}

.test-button.portal {
  background-color: #10b981;
  color: white;
}

.test-button.portal:hover {
  background-color: #059669;
}

.test-button.normal {
  background-color: #f59e0b;
  color: white;
}

.test-button.normal:hover {
  background-color: #d97706;
}

.test-button.zindex {
  background-color: #8b5cf6;
  color: white;
}

.test-button.zindex:hover {
  background-color: #7c3aed;
}

.test-button.custom {
  background-color: #06b6d4;
  color: white;
}

.test-button.custom:hover {
  background-color: #0891b2;
}

.test-button.active {
  transform: scale(0.95);
}

.panel-content {
  padding: 16px;
}

.panel-content.large {
  min-height: 180px;
}

.content-box {
  background-color: #f3f4f6;
  padding: 12px;
  border-radius: 4px;
  margin: 10px 0;
}

body.dark .content-box {
  background-color: var(--bg-secondary);
}

.z-index-container {
  position: relative;
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 8px;
}

body.dark .z-index-container {
  background-color: var(--bg-secondary);
}

.high-z-index-element {
  position: absolute;
  top: 50px;
  right: 20px;
  background-color: #ef4444;
  color: white;
  padding: 10px;
  border-radius: 4px;
  z-index: 999;
  font-size: 12px;
}

.control-section {
  margin-bottom: 30px;
}

.control-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.control-btn {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s;
}

.control-btn:hover {
  background-color: #f3f4f6;
}

.control-btn.danger {
  background-color: #ef4444;
  color: white;
  border-color: #ef4444;
}

.control-btn.danger:hover {
  background-color: #dc2626;
}

body.dark .control-btn {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

body.dark .control-btn:hover {
  background-color: var(--bg-tertiary);
}

.info-section {
  background-color: #f8fafc;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

body.dark .info-section {
  background-color: var(--bg-secondary);
  border-color: #3b82f6;
}

.status-info p {
  margin: 4px 0;
  font-family: monospace;
}

.custom-portal-container {
  position: fixed;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1001;
}

h2, h3, h4 {
  color: #1f2937;
  margin-top: 0;
}

body.dark h2,
body.dark h3,
body.dark h4 {
  color: var(--text-primary);
}
</style>
