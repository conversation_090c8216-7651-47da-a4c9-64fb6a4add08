<template>
  <div class="video-playback-test">
    <h2>视频播放测试</h2>
    
    <div class="test-controls">
      <el-button @click="createTestShots" type="primary">创建测试分镜</el-button>
      <el-button @click="startTest" :disabled="!testShots.length">开始测试</el-button>
      <el-button @click="stopTest">停止测试</el-button>
      <el-button @click="showDebugLogs">显示调试日志</el-button>
    </div>

    <div class="test-info" v-if="testShots.length">
      <p>测试分镜数量: {{ testShots.length }}</p>
      <p>当前分镜: {{ currentShotIndex + 1 }} / {{ testShots.length }}</p>
      <p>当前分镜类型: {{ currentShot?.type }}</p>
    </div>

    <div class="preview-container" v-if="testShots.length">
      <VideoEditorPreview 
        ref="previewComponent"
        :shots="testShots"
        :start-index="0"
        @time-update="onTimeUpdate"
        @shot-change="onShotChange"
        @play-state-change="onPlayStateChange"
        @playback-completed="onPlaybackCompleted"
      />
    </div>

    <!-- 调试日志对话框 -->
    <el-dialog v-model="showDebugDialog" title="调试日志" width="80%">
      <div class="debug-logs">
        <pre>{{ debugLogs }}</pre>
      </div>
      <template #footer>
        <el-button @click="clearDebugLogs">清除日志</el-button>
        <el-button @click="showDebugDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import VideoEditorPreview from '@/components/videoEdit/VideoEditorPreview.vue';
import { videoPlaybackDebugger } from '@/utils/videoPlaybackDebug.js';

// 测试数据
const testShots = ref([]);
const currentShotIndex = ref(0);
const previewComponent = ref(null);
const showDebugDialog = ref(false);
const debugLogs = ref('');

// 当前分镜
const currentShot = computed(() => {
  return testShots.value[currentShotIndex.value] || null;
});

// 创建测试分镜数据
const createTestShots = () => {
  testShots.value = [
    {
      id: 1,
      type: 'image',
      imageUrl: 'https://picsum.photos/800/600?random=1',
      movement: 'zoom-in',
      audios: [{
        audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
        audioDuration: 3000,
        text: '这是第一个图片分镜'
      }]
    },
    {
      id: 2,
      type: 'video',
      videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      videoDuration: 5000,
      audios: [{
        audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
        audioDuration: 2000,
        text: '这是第一个视频分镜'
      }]
    },
    {
      id: 3,
      type: 'video',
      videoUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4',
      videoDuration: 8000,
      audios: [{
        audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
        audioDuration: 3000,
        text: '这是第二个视频分镜'
      }]
    },
    {
      id: 4,
      type: 'image',
      imageUrl: 'https://picsum.photos/800/600?random=2',
      movement: 'pan-right',
      audios: [{
        audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
        audioDuration: 4000,
        text: '这是第二个图片分镜'
      }]
    }
  ];
  
  console.log('创建了测试分镜:', testShots.value);
};

// 开始测试
const startTest = () => {
  if (previewComponent.value) {
    videoPlaybackDebugger.log('开始视频播放测试');
    previewComponent.value.play();
  }
};

// 停止测试
const stopTest = () => {
  if (previewComponent.value) {
    videoPlaybackDebugger.log('停止视频播放测试');
    previewComponent.value.pause();
  }
};

// 显示调试日志
const showDebugLogs = () => {
  debugLogs.value = videoPlaybackDebugger.exportLogs();
  showDebugDialog.value = true;
};

// 清除调试日志
const clearDebugLogs = () => {
  videoPlaybackDebugger.clearLogs();
  debugLogs.value = '';
};

// 事件处理
const onTimeUpdate = (data) => {
  // console.log('时间更新:', data);
};

const onShotChange = (index) => {
  currentShotIndex.value = index;
  videoPlaybackDebugger.log('分镜切换', { newIndex: index, shotType: currentShot.value?.type });
};

const onPlayStateChange = (playing) => {
  videoPlaybackDebugger.log('播放状态变更', { playing });
};

const onPlaybackCompleted = () => {
  videoPlaybackDebugger.log('播放完成');
};
</script>

<style scoped>
.video-playback-test {
  padding: 20px;
}

.test-controls {
  margin-bottom: 20px;
}

.test-controls .el-button {
  margin-right: 10px;
}

.test-info {
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.preview-container {
  width: 100%;
  height: 400px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.debug-logs {
  max-height: 400px;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.debug-logs pre {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
}
</style>
