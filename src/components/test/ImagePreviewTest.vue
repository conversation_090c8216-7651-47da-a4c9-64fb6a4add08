<template>
  <div class="image-preview-test">
    <h2>ImagePreview Component Test</h2>
    
    <div class="test-section">
      <h3>Test 1: String Title (Original Format)</h3>
      <div class="test-container">
        <ImagePreview 
          type="character"
          :imageUrl="testImageUrl"
          title="Test Character Name"
          itemId="test-1"
          :conversationId="'test-conversation'"
        />
      </div>
    </div>

    <div class="test-section">
      <h3>Test 2: Object Title (New Format - Should Fix the Warning)</h3>
      <div class="test-container">
        <ImagePreview 
          type="character"
          :imageUrl="testImageUrl"
          :title="{ title: 'Test Character', description: 'A test character with detailed description' }"
          itemId="test-2"
          :conversationId="'test-conversation'"
        />
      </div>
    </div>

    <div class="test-section">
      <h3>Test 3: Scene Type with String Title</h3>
      <div class="test-container">
        <ImagePreview 
          type="scene"
          :imageUrl="testImageUrl"
          title="Test Scene"
          itemId="test-3"
          :conversationId="'test-conversation'"
        />
      </div>
    </div>

    <div class="test-section">
      <h3>Test 4: Empty Title</h3>
      <div class="test-container">
        <ImagePreview 
          type="character"
          :imageUrl="testImageUrl"
          title=""
          itemId="test-4"
          :conversationId="'test-conversation'"
        />
      </div>
    </div>

    <div class="test-info">
      <h3>Expected Behavior:</h3>
      <ul>
        <li>No Vue warnings in console about prop type mismatch</li>
        <li>All images should display correctly</li>
        <li>Clicking images should open preview with appropriate title information</li>
        <li>Alt text should be properly set for accessibility</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ImagePreview from '../create/ImagePreview.vue'

// Use a placeholder image for testing
const testImageUrl = ref('https://via.placeholder.com/300x400/4F46E5/FFFFFF?text=Test+Image')

// Log to console for debugging
console.log('ImagePreview Test Component Loaded')
</script>

<style scoped>
.image-preview-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.test-section h3 {
  margin-top: 0;
  color: #374151;
}

.test-container {
  width: 200px;
  height: 250px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-info {
  background-color: #f3f4f6;
  padding: 20px;
  border-radius: 8px;
  margin-top: 30px;
}

.test-info h3 {
  margin-top: 0;
  color: #059669;
}

.test-info ul {
  margin: 0;
  padding-left: 20px;
}

.test-info li {
  margin-bottom: 8px;
  color: #374151;
}
</style>
