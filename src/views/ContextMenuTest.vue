<template>
  <div class="context-menu-test">
    <div class="container">
      <h1>右键菜单测试</h1>
      
      <div class="test-area">
        <h2>测试区域 1 - 指令方式</h2>
        <div 
          v-context-menu="{ items: testMenuItems, callback: handleMenuClick }"
          class="test-box"
        >
          右键点击这里测试菜单
        </div>
      </div>

      <div class="test-area">
        <h2>测试区域 2 - Composable方式</h2>
        <div 
          class="test-box"
          @contextmenu="showTestMenu"
        >
          右键点击这里测试菜单（Composable）
        </div>
      </div>

      <div class="log-area">
        <h3>操作日志：</h3>
        <div class="logs">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            {{ log }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useContextMenu } from '../composables/useContextMenu.js'

const { showContextMenu, createMenuItem, createDivider } = useContextMenu()

const logs = ref([])

const testMenuItems = [
  createMenuItem('测试项目 1', { icon: 'el-icon-check', action: 'test1' }),
  createMenuItem('测试项目 2', { icon: 'el-icon-star-off', action: 'test2' }),
  createDivider(),
  createMenuItem('测试项目 3', { icon: 'el-icon-delete', action: 'test3' })
]

const addLog = (message) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift(`[${timestamp}] ${message}`)
  console.log(message)
}

const handleMenuClick = (item, index, target) => {
  addLog(`指令方式 - 点击了: ${item.label} (action: ${item.action})`)
}

const showTestMenu = (event) => {
  showContextMenu(event, testMenuItems, (item, index, target) => {
    addLog(`Composable方式 - 点击了: ${item.label} (action: ${item.action})`)
  })
}
</script>

<style scoped>
.context-menu-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-area {
  margin-bottom: 30px;
}

.test-box {
  background: #f0f9ff;
  border: 2px dashed #0ea5e9;
  border-radius: 8px;
  padding: 60px 20px;
  text-align: center;
  color: #0369a1;
  cursor: pointer;
  transition: all 0.3s;
  user-select: none;
}

.test-box:hover {
  background: #e0f2fe;
  border-color: #0284c7;
}

.log-area {
  margin-top: 40px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
}

.logs {
  max-height: 200px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.log-item {
  padding: 4px 0;
  border-bottom: 1px solid #e2e8f0;
  color: #475569;
}

.log-item:last-child {
  border-bottom: none;
}

h1 {
  color: #1e293b;
  text-align: center;
  margin-bottom: 30px;
}

h2 {
  color: #334155;
  margin-bottom: 15px;
}

h3 {
  color: #475569;
  margin-bottom: 15px;
}
</style>
