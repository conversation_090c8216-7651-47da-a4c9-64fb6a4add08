<template>
  <div class="stories-page">
    <div class="gradient-bg"></div>
    <div class="container">
      <MyStories />
    </div>
  </div>
</template>

<script setup>
import MyStories from '@/components/MyStories.vue'



</script>

<style scoped>
.stories-page {
  min-height: 100vh;
  position: relative;
}

.gradient-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
      rgba(99, 102, 241, 0.1) 0%,
      rgba(139, 92, 246, 0.1) 25%,
      rgba(59, 130, 246, 0.1) 50%,
      rgba(16, 185, 129, 0.1) 75%,
      rgba(245, 158, 11, 0.1) 100%);
  z-index: -2;
  animation: gradientShift 15s ease-in-out infinite;
}

body.dark .gradient-bg {
  background: linear-gradient(135deg,
      rgba(99, 102, 241, 0.05) 0%,
      rgba(139, 92, 246, 0.05) 25%,
      rgba(59, 130, 246, 0.05) 50%,
      rgba(16, 185, 129, 0.05) 75%,
      rgba(245, 158, 11, 0.05) 100%);
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.container {
  /* max-width: 1400px; */
  margin: 0 auto;
  /* padding: 40px 20px; */
  position: relative;
  z-index: 1;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .container {
    padding: 20px 10px;
  }
}
</style>
