<template>
  <div class="demo-container">
    <div class="demo-content">
      <h1>维护页面演示</h1>
      <p>点击下面的按钮查看不同的维护页面效果</p>
      
      <div class="demo-buttons">
        <button @click="showMaintenance" class="demo-btn primary">
          查看维护页面
        </button>
        
        <button @click="showTest502" class="demo-btn secondary">
          502错误测试
        </button>
        
        <button @click="goHome" class="demo-btn outline">
          返回首页
        </button>
      </div>
      
      <div class="demo-info">
        <h3>页面特性：</h3>
        <ul>
          <li>✨ 现代化设计风格</li>
          <li>🎨 渐变背景和浮动动画</li>
          <li>📱 完全响应式设计</li>
          <li>🔄 自动刷新倒计时</li>
          <li>💡 友好的用户提示</li>
          <li>🌙 深色模式支持</li>
          <li>⚡ 流畅的动画效果</li>
          <li>🛠️ 详细的技术信息</li>
        </ul>
      </div>
      
      <div class="demo-preview">
        <h3>页面预览：</h3>
        <div class="preview-grid">
          <div class="preview-item">
            <div class="preview-image">
              <div class="mock-maintenance">
                <div class="mock-icon">🔧</div>
                <div class="mock-title">服务器维护中</div>
                <div class="mock-desc">System Maintenance</div>
                <div class="mock-cards">
                  <div class="mock-card"></div>
                  <div class="mock-card"></div>
                  <div class="mock-card"></div>
                </div>
                <div class="mock-buttons">
                  <div class="mock-btn primary"></div>
                  <div class="mock-btn secondary"></div>
                </div>
              </div>
            </div>
            <p>桌面端效果</p>
          </div>
          
          <div class="preview-item">
            <div class="preview-image mobile">
              <div class="mock-maintenance mobile">
                <div class="mock-icon">🔧</div>
                <div class="mock-title">维护中</div>
                <div class="mock-cards vertical">
                  <div class="mock-card small"></div>
                  <div class="mock-card small"></div>
                </div>
                <div class="mock-buttons vertical">
                  <div class="mock-btn primary small"></div>
                  <div class="mock-btn secondary small"></div>
                </div>
              </div>
            </div>
            <p>移动端效果</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'

export default {
  name: 'MaintenanceDemo',
  setup() {
    const router = useRouter()
    
    const showMaintenance = () => {
      router.push('/error/502')
    }
    
    const showTest502 = () => {
      router.push('/test/502')
    }
    
    const goHome = () => {
      router.push('/home')
    }
    
    return {
      showMaintenance,
      showTest502,
      goHome
    }
  }
}
</script>

<style scoped>
.demo-container {
  min-height: 100vh;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.demo-content {
  max-width: 1000px;
  margin: 0 auto;
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

h1 {
  text-align: center;
  color: #2d3748;
  margin-bottom: 16px;
  font-size: 2.5rem;
  font-weight: 700;
}

.demo-content > p {
  text-align: center;
  color: #718096;
  margin-bottom: 40px;
  font-size: 1.1rem;
}

.demo-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 50px;
  flex-wrap: wrap;
}

.demo-btn {
  padding: 14px 28px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.demo-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.demo-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.demo-btn.secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
}

.demo-btn.secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
}

.demo-btn.outline {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.demo-btn.outline:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

.demo-info {
  margin-bottom: 50px;
  padding: 30px;
  background: #f7fafc;
  border-radius: 16px;
  border-left: 4px solid #667eea;
}

.demo-info h3 {
  margin: 0 0 20px 0;
  color: #2d3748;
  font-size: 1.3rem;
}

.demo-info ul {
  margin: 0;
  padding-left: 0;
  list-style: none;
}

.demo-info li {
  margin-bottom: 12px;
  color: #4a5568;
  font-size: 1rem;
  display: flex;
  align-items: center;
}

.demo-preview h3 {
  margin: 0 0 30px 0;
  color: #2d3748;
  font-size: 1.3rem;
}

.preview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

.preview-item {
  text-align: center;
}

.preview-image {
  background: #f7fafc;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  border: 2px solid #e2e8f0;
}

.preview-image.mobile {
  max-width: 200px;
  margin: 0 auto 16px;
}

.mock-maintenance {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 30px;
  color: white;
  text-align: center;
}

.mock-maintenance.mobile {
  padding: 20px;
  font-size: 0.8rem;
}

.mock-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.mock-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 8px;
}

.mock-maintenance.mobile .mock-title {
  font-size: 1.2rem;
}

.mock-desc {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-bottom: 20px;
}

.mock-cards {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  justify-content: center;
}

.mock-cards.vertical {
  flex-direction: column;
  gap: 8px;
}

.mock-card {
  background: rgba(255, 255, 255, 0.2);
  height: 40px;
  border-radius: 8px;
  flex: 1;
  max-width: 80px;
}

.mock-card.small {
  height: 30px;
  max-width: none;
}

.mock-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.mock-buttons.vertical {
  flex-direction: column;
  gap: 8px;
}

.mock-btn {
  height: 35px;
  border-radius: 8px;
  flex: 1;
  max-width: 80px;
}

.mock-btn.small {
  height: 30px;
  max-width: none;
}

.mock-btn.primary {
  background: rgba(255, 255, 255, 0.9);
}

.mock-btn.secondary {
  background: rgba(255, 255, 255, 0.6);
}

.preview-item p {
  color: #718096;
  font-weight: 500;
}

@media (max-width: 768px) {
  .demo-content {
    padding: 30px 20px;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  .demo-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .demo-btn {
    width: 100%;
    max-width: 300px;
  }
  
  .preview-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}
</style>
