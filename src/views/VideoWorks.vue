<template>
  <div class="video-works-component">
    <!-- 页面标题 -->
    <!-- <div class="page-header">
      <h1 class="page-title">视频作品精选</h1>
    </div> -->

    <!-- 使用封装的视频作品网格组件 -->
    <VideoWorksGrid
      @video-click="viewVideo"
      @share-video="shareVideo"
      @load-more="handleLoadMore"
      @make-similar="handleShareCode"
    />
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import VideoWorksGrid from '@/components/VideoWorksGrid.vue'
import { getVideoRenderSharedVideo } from '@/api/auth.js'

const router = useRouter()

// 查看视频
const viewVideo = (video) => {
  // 如果有分享码，跳转到视频分享页面
}

// 分享视频
const shareVideo = (video) => {
}

// 处理加载更多事件
const handleLoadMore = (data) => {
  console.log('加载更多:', data)
}

// 处理分享码，获取视频的 prompt 并跳转到创作页面
const handleShareCode = async (video) => {
  if (!video.shareCode) {
    ElMessage.warning('该视频暂无分享码')
    return
  }

  try {
    console.log('检测到分享码:', video.shareCode)
    const response = await getVideoRenderSharedVideo(video.shareCode)

    if (response && response.success && response.data) {
      const prompt = response.data.prompt
      if (prompt) {
        console.log('获取到 prompt:', prompt)
        // 跳转到创作页面，传递分享码参数
        router.push({
          path: '/inputSection',
          query: { shareCode: video.shareCode }
        })
      } else {
        console.log('分享视频中没有 prompt 数据')
        ElMessage.warning('该视频暂无创作提示词')
      }
    } else {
      console.error('获取分享视频失败:', response?.errMessage || '未知错误')
      ElMessage.error('获取分享视频信息失败')
    }
  } catch (error) {
    console.error('处理分享码异常:', error)
    ElMessage.error('获取分享视频信息失败')
  }
}
</script>

<style scoped>
.video-works-component {
  padding: 0 10px;
  width: 100%;
}

/* 页面标题样式 */
.page-header {
  text-align: center;
  padding: 10px 0;
  text-align: left;
}

.page-title {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  background: linear-gradient(135deg, #6365f1b0, #8a5cf6b7);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: title-glow 3s ease-in-out infinite alternate;
}

body.dark .page-title {
  color: #e5e7eb;
}

@keyframes title-glow {
  0% {
    filter: brightness(1);
  }
  100% {
    filter: brightness(1.1);
  }
}
</style>
