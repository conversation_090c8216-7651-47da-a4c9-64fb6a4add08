<template>
  <div class="create-page">
    <!-- <Header class="header-component" :transparent-strong="true" /> -->
    <ParticlesBackground />
    <div class="gradient-bg"></div>
    <div class="main-content">

      <!-- 右侧创意输入 -->
      <transition name="section-fade">
        <div class="creative-input-section" v-if="!showInputSection">

          <CreativeInput v-model="creativeText" :selected-style="selectedStyle" :selected-actors="selectedActors"
            :actor-names="selectedActors.map(id => actors.find(a => a.id === id))" :selected-voice="selectedVoice"
            :voice-name="selectedVoice && voices.length > 0 ? voices.find(v => v.id === selectedVoice)?.name : ''"
            :style-name="selectedStyle && styles.length > 0 ? styles.find(s => s.id === selectedStyle)?.name : ''"
            :selected-model="selectedModel"
            :model-name="selectedModel && imageModels.length > 0 ? imageModels.find(m => m.id === selectedModel)?.name : ''"
            :output-ratio="outputRatio" v-model:confirm-each-step="confirmEachStep" v-model:high-quality="highQuality"
            :is-creating="isCreating" :suggestions="tags" :is-loading-suggestions="isLoadingTags"
            :styles="styles" :isLoadingStyles="isLoadingStyles" :voices="voices" :isLoadingVoices="isLoadingVoices"
            :imageModels="imageModels" @refresh-suggestions="fetchTags" @clear-style="selectedStyle = null"
            @remove-actor="removeActor" @clear-voice="selectedVoice = null" @start-creation="startCreation"
            @open-setting="openSettingModule" @update:uploadedFiles="handleUploadedFiles"
            @update:selectedStyle="selectedStyle = $event" @update:selectedVoice="selectedVoice = $event"
            @update:selectedModel="selectedModel = $event" @update:outputRatio="outputRatio = $event"
            @refresh-voices="fetchVoices" />
        </div>
      </transition>

      <transition name="section-fade">
        <div class="my-stories-section" v-if="!showInputSection">
          <VideoWorksGrid @make-similar="handleMakeSimilar" />
        </div>
      </transition>
    </div>
  </div>

  <!-- 创作喜好配置对话框 -->
  <CreativePrefsDialog v-model:visible="creativePrefsDialogVisible" @saved="handleCreativePrefsSaved" />

  <!-- Prompt优化确认对话框 -->
  <PromptOptimizationDialog
    v-model:visible="promptOptimizationDialogVisible"
    :creative-text="creativeText"
    :selected-style-prompt="selectedStylePrompt"
    :image-urls="imageUrls"
    @confirm="handlePromptOptimizationConfirm"
    @cancel="handlePromptOptimizationCancel" />
</template>

<script setup>
import {nextTick, onMounted, ref, watch} from 'vue'
import {useRouter, useRoute} from 'vue-router'

import CreativeInput from '@/components/CreativeInput.vue'
import ParticlesBackground from '@/components/ParticlesBackground.vue'
import VideoWorksGrid from '@/components/VideoWorksGrid.vue'
import userSession from '@/utils/userSession.js'
import {ElMessage} from 'element-plus';
import {
  createConversation,
  getImageStyleList,
  getTagList,
  getVoiceList,
  getVideoRenderSharedVideo
} from '@/api/auth.js'
import CreativePrefsDialog from '@/components/CreativePrefsDialog.vue'
import PromptOptimizationDialog from '@/components/PromptOptimizationDialog.vue'

// 创作喜好配置对话框
const creativePrefsDialogVisible = ref(false)

// Prompt优化确认对话框
const promptOptimizationDialogVisible = ref(false)
const optimizedPromptData = ref(null)
const imageUrls = ref([])

// 使用 route 获取 URL 参数
const router = useRouter()
const route = useRoute()

// 画面风格数据
const styles = ref([])
const isLoadingStyles = ref(true)

// 快捷标签数据
const tags = ref([])
const isLoadingTags = ref(true)

// 音色数据
const voices = ref([])
const isLoadingVoices = ref(true)

// 表单状态
const selectedStylePrompt = ref(null)
const selectedStyle = ref(null)
const selectedActors = ref([])
const selectedVoice = ref(null)
const selectedModel = ref("DOUBAO") // 添加选中的模型状态
const creativeText = ref('')
const confirmEachStep = ref(true)
const highQuality = ref(false)
const isCreating = ref(false)
const outputRatio = ref('16:9') // 默认输出比例为16:9
const uploadedFiles = ref([]) // 存储上传的文件信息

// 关闭按钮闪烁状态
const isCloseButtonFlashing = ref(false)
// 存储闪烁效果计时器引用
let flashTimeoutRef = null

const imageModels = ref([
  {
    "name": "DOUBAO",
    "id": "DOUBAO",
    "advantages": "高分辨率",
    "price": "30积分/张",
    "image": "https://img.doubao.com/doubao.png"
  },
  {
    "name": "FLUX",
    "id": "FLUX",
    "advantages": "细节丰富，理解复杂提示",
    "price": "30积分/张",
    "image": "https://img.doubao.com/flux.png"
  }
])

// 控制输入区域显示/隐藏
const showInputSection = ref(false)
// 当前聚焦的设置模块
const focusModule = ref(null) // 'style', 'voice', 'ratio'

// 打字机效果状态
const isTyping = ref(false)

// 打字机效果函数
const typewriterEffect = (text, callback = null) => {
  if (isTyping.value) return; // 如果正在打字，则不执行

  isTyping.value = true;
  creativeText.value = ''; // 清空文本

  const fullText = text;
  const totalDuration = 1500; // 总时间固定为1.5秒（1500毫秒）
  const totalChars = fullText.length;

  // 处理空文本或极短文本的情况
  if (totalChars <= 0) {
    isTyping.value = false;
    if (callback && typeof callback === 'function') {
      callback();
    }
    return;
  }

  // 计算每个字符的时间间隔
  const intervalPerChar = totalDuration / totalChars;

  // 使用 requestAnimationFrame 实现更平滑的动画
  let startTime = null;

  const animate = (timestamp) => {
    if (!startTime) startTime = timestamp;

    // 计算已经过去的时间
    const elapsedTime = timestamp - startTime;

    // 根据已过时间计算应该显示的字符数
    const charsToShow = Math.min(Math.floor(elapsedTime / intervalPerChar), totalChars);

    // 更新文本
    creativeText.value = fullText.substring(0, charsToShow);

    // 如果还没显示完所有字符且未超时，继续动画
    if (charsToShow < totalChars && elapsedTime < totalDuration) {
      requestAnimationFrame(animate);
    } else {
      // 确保显示完整文本
      creativeText.value = fullText;
      isTyping.value = false;
      if (callback && typeof callback === 'function') {
        callback();
      }
    }
  };

  // 开始动画
  requestAnimationFrame(animate);
};



// 从演员列表中移除
const removeActor = (actorId) => {
  const index = selectedActors.value.indexOf(actorId)
  if (index > -1) {
    selectedActors.value.splice(index, 1)
  }
}

// 创作流程
const startCreation = async () => {
  try {
    isCreating.value = true

    if (creativeText.value == "") {
      ElMessage.error('请输入创意内容')
      isCreating.value = false
      return
    }

    // if (selectedVoice.value == null) {
    //   ElMessage.error('请选择音色')
    //   return
    // }
    // if (selectedStyle.value == null) {
    //   ElMessage.error('请选择画面风格')
    //   return
    // }

    if (selectedModel.value == null) {
      ElMessage.error('请选择创图模型')
      isCreating.value = false
      return
    }

    if (outputRatio.value == null) {
      ElMessage.error('请选择输出比例')
      isCreating.value = false
      return
    }

    if (selectedModel.value == null) {
      ElMessage.error('请选择创图模型')
      isCreating.value = false
      return
    }

    if (selectedStyle.value != null) {
      const prompt = styles.value.find(s => s.id === selectedStyle.value)?.prompt
      selectedStylePrompt.value = "#画面风格#\n" + prompt + "\n#内容#\n" + creativeText.value
    }

    // 提取上传文件的 objectName
    imageUrls.value = uploadedFiles.value.map(file => file.objectName)

    // 显示 Prompt 优化确认对话框
    isCreating.value = false
    promptOptimizationDialogVisible.value = true

  } catch (error) {
    console.error('启动创作流程出错:', error)
    ElMessage.error('' + error)
    isCreating.value = false
  }
}

// 实际创建会话的函数
const createConversationWithOptimizedPrompt = async (optimizedData = null) => {
  try {
    isCreating.value = true

    var creativeNewText = creativeText.value

    // 如果有优化数据，可以在这里使用优化后的内容
    if (optimizedData) {
      // 可以根据需要使用优化数据来增强 prompt
      console.log('使用优化数据:', optimizedData)
      creativeNewText = JSON.stringify(optimizedData)
    }

    // 创建一个新的会话
    userSession.createNewConversation()

    // 提取上传文件的 objectName
    imageUrls.value = uploadedFiles.value.map(file => file.objectName)

    // 调用接口创建AI创作会话
    const response = await createConversation(
      creativeNewText,
      selectedVoice.value,
      outputRatio.value,
      selectedStyle.value,
      selectedModel.value, // 添加模型参数
      imageUrls.value // 添加文件参数
    )

    if (response && response.success) {
      const sessionId = response.data.sessionId

      // 路由到创作页面，传递相关参数
      router.push({
        path: '/create',
        query: {
          conversationId: sessionId,
          // style: selectedStyle.value,
          // ratio: outputRatio.value
        }
      })

      localStorage.setItem('creativeText', "")

    } else {
      console.error('创建AI创作会话失败:', response)
      // 可以在这里添加错误提示
      ElMessage.error('会话失败:' + response.errMessage)
    }
  } catch (error) {
    console.error('会话出错:', error)
    // 可以在这里添加错误提示
    ElMessage.error('' + error)
  } finally {
    isCreating.value = false
  }
}

// 处理上传文件
const handleUploadedFiles = (files) => {
  uploadedFiles.value = files
  console.log('上传的文件:', files)
}

watch(() => creativeText.value, (newValue) => {
  localStorage.setItem('creativeText', newValue)
}, { immediate: false })

// 监听风格变化
watch(() => selectedStyle.value, (newValue, oldValue) => {
  if (oldValue !== null && newValue !== oldValue) {
    triggerCloseButtonFlash()
  }
})

// 监听音色变化
watch(() => selectedVoice.value, (newValue, oldValue) => {
  if (oldValue !== null && newValue !== oldValue) {
    triggerCloseButtonFlash()
  }
})

// 监听模型变化
watch(() => selectedModel.value, (newValue, oldValue) => {
  if (oldValue !== null && newValue !== oldValue) {
    triggerCloseButtonFlash()
  }
})

// 监听比例变化
watch(() => outputRatio.value, (newValue, oldValue) => {
  if (oldValue !== null && newValue !== oldValue) {
    triggerCloseButtonFlash()
  }
})

// 获取音色列表
const fetchVoices = async () => {
  try {
    isLoadingVoices.value = true
    const response = await getVoiceList()
    if (response.success) {
      // 处理音色数据，将外部URL转换为本地代理URL
      voices.value = response.data.map(voice => {
        // if (voice.audioUrl) {
        //   return { ...voice, audioUrl: toProxyUrl(voice.audioUrl) }
        // }
        return voice
      })

      // 如果没有选中音色，随机选择一个
      // if (voices.value.length > 0 && !selectedVoice.value) {
      //   const randomIndex = getRandomInt(0, voices.value.length - 1);
      //   selectedVoice.value = voices.value[randomIndex].id;
      //   console.log('自动随机选择了音色:', voices.value[randomIndex].name);
      // }
    } else {
      console.error('获取音色列表失败:', response.data.errMessage)
    }
  } catch (error) {
    console.error('获取音色列表异常:', error)
  } finally {
    isLoadingVoices.value = false
  }
}

// 获取画面风格列表
const fetchStyles = async () => {
  try {
    isLoadingStyles.value = true
    const response = await getImageStyleList()
    if (response.success) {
      // 转换API返回的风格数据为组件需要的格式
      styles.value = response.data.map(style => ({
        id: style.id,
        name: style.styleName,
        category: style.styleCode || '',
        preview: style.styleUrl || '',
        styleCategory: style.styleCategory || '',
        prompt:style.prompt || ''
      }))

      // 如果没有选中风格，随机选择一个
      // if (styles.value.length > 0 && !selectedStyle.value) {
      //   const randomIndex = getRandomInt(0, styles.value.length - 1);
      //   selectedStyle.value = styles.value[randomIndex].id;
      //   console.log('自动随机选择了画面风格:', styles.value[randomIndex].name);
      // }
    } else {
      console.error('获取画面风格列表失败:', response.errMessage)
    }
  } catch (error) {
    console.error('获取画面风格列表异常:', error)
  } finally {
    isLoadingStyles.value = false
  }
}

// 获取创意标签列表
const fetchTags = async () => {
  try {
    // isLoadingTags.value = true
    const response = await getTagList()
    if (response.success) {
      // 提取标签数据中的prompt字段作为标签显示内容
      tags.value = response.data
    } else {
      console.error('获取创意标签列表失败:', response.errMessage)
    }
  } catch (error) {
    console.error('获取创意标签列表异常:', error)
  } finally {
    isLoadingTags.value = false
  }
}

// 处理分享码，获取视频的 prompt
const handleShareCode = async () => {
  const shareCode = route.query.shareCode
  if (shareCode) {
    try {
      console.log('检测到分享码:', shareCode)
      const response = await getVideoRenderSharedVideo(shareCode)

      if (response && response.success && response.data) {
        const prompt = response.data.prompt
        if (prompt) {
          console.log('获取到 prompt:', prompt)
          // 使用打字机效果填充到输入框中
          typewriterEffect(prompt)
        } else {
          console.log('分享视频中没有 prompt 数据')
        }
      } else {
        console.error('获取分享视频失败:', response?.errMessage || '未知错误')
        ElMessage.error('获取分享视频信息失败')
      }
    } catch (error) {
      console.error('处理分享码异常:', error)
      ElMessage.error('获取分享视频信息失败')
    }
  }
}

// 处理做同款按钮点击
const handleMakeSimilar = async (video) => {
  if (!video.shareCode) {
    ElMessage.warning('该视频暂无分享码')
    return
  }

  try {
    console.log('做同款 - 检测到分享码:', video.shareCode)
    const response = await getVideoRenderSharedVideo(video.shareCode)

    if (response && response.success && response.data) {
      const prompt = response.data.prompt
      if (prompt) {
        console.log('做同款 - 获取到 prompt:', prompt)
        // 将两个换行字符替换成一个
        const processedPrompt = prompt.replace(/\n\n/g, '\n')
        // 页面滚动到顶部 - 滚动到 .create-page 容器的顶部
        const createPageElement = document.querySelector('.main-content')
        if (createPageElement) {
          createPageElement.scrollTo({ top: 0, behavior: 'smooth' })
        }
        // 使用打字机效果填充到输入框中
        typewriterEffect(processedPrompt)
      } else {
        console.log('分享视频中没有 prompt 数据')
        ElMessage.warning('该视频暂无创作提示词')
      }
    } else {
      console.error('获取分享视频失败:', response?.errMessage || '未知错误')
      ElMessage.error('获取分享视频信息失败')
    }
  } catch (error) {
    console.error('处理分享码异常:', error)
    ElMessage.error('获取分享视频信息失败')
  }
}

onMounted(async () => {

  // 获取音色列表
  fetchVoices()
  // 获取画面风格列表
  fetchStyles()
  // 获取创意标签列表
  fetchTags()

  // 处理分享码
  await handleShareCode()

  // 如果没有分享码，则从本地存储获取文本
  const shareCode = route.query.shareCode
  if (!shareCode) {
    const text = localStorage.getItem("creativeText")
    if (text) {
      // creativeText.value = text
      typewriterEffect(text)
    }
  }
})






// 打开设置模块
const openSettingModule = (module) => {
  if (module === 'prefs') {
    // 打开创作喜好对话框
    showCreativePrefsDialog()
    return
  }

  showInputSection.value = true
  focusModule.value = module // 'style', 'voice', 'ratio', 'model'

  // 使用nextTick确保DOM已经更新
  nextTick(() => {
    // 根据模块类型滚动到相应区域
    let targetSelector = ''

    // 根据模块名称确定目标选择器
    switch (module) {
      case 'style':
        targetSelector = '.style-selection-container'
        break
      case 'voice':
        targetSelector = '.voice-selector-container'
        break
      case 'model':
        targetSelector = '.model-selector'
        break
      case 'ratio':
        targetSelector = '.ratio-selector'
        break
    }

    if (targetSelector) {
      const targetElement = document.querySelector(targetSelector)
      if (targetElement) {
        targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    }
  })
}

// 显示创作喜好对话框
const showCreativePrefsDialog = () => {
  creativePrefsDialogVisible.value = true
}

// 处理创作喜好保存成功
const handleCreativePrefsSaved = () => {
  // 可以在这里处理保存后的逻辑，如果需要的话
  ElMessage.success('创作喜好已更新')
}

// 处理 Prompt 优化确认
const handlePromptOptimizationConfirm = (optimizedData) => {
  console.log('用户确认优化数据:', optimizedData)
  optimizedPromptData.value = optimizedData
  // 使用优化数据创建会话
  createConversationWithOptimizedPrompt(optimizedData)
}

// 处理 Prompt 优化取消
const handlePromptOptimizationCancel = () => {
  console.log('用户取消了 Prompt 优化')
  // 可以选择直接创建会话或者什么都不做
  // createConversationWithOptimizedPrompt()
}

// 触发关闭按钮闪烁效果
const triggerCloseButtonFlash = () => {
  if (showInputSection.value) {
    // 如果上一次的动画还在进行中，先清除计时器
    if (flashTimeoutRef) {
      clearTimeout(flashTimeoutRef)
      flashTimeoutRef = null
    }

    // 设置闪烁状态为 true
    isCloseButtonFlashing.value = true

    // 1.5秒后重置闪烁状态，并保存计时器引用
    flashTimeoutRef = setTimeout(() => {
      isCloseButtonFlashing.value = false
      flashTimeoutRef = null
    }, 1500)
  }
}

</script>

<style scoped>
.create-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  color: #333;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  /* 允许垂直滚动，但只在create-page级别 */
  scroll-behavior: smooth;
  /* 平滑滚动效果 */
  transition: background-color 0.3s, color 0.3s;
  display: flex;
  flex-direction: column;
}

body.dark .create-page {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.header-component {
  position: relative;
  z-index: 10;
  /* 确保Header显示在最上层 */
}

.gradient-bg {
  position: fixed;
  /* 改为fixed，使背景在滚动时保持在视口中 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* background: radial-gradient(circle at 10% 20%, rgba(204, 205, 240, 0.15) 0%, rgba(217, 209, 234, 0.1) 90%); */
  z-index: 0;
  transition: background 0.3s;
}

body.dark .gradient-bg {
  background: transparent;
  /* background: radial-gradient(circle at 10% 20%, rgba(99, 102, 241, 0.1) 0%, rgba(138, 92, 246, 0.05) 90%); */
}

.main-content {
  /* max-width: 1600px; */
  margin: 0 auto;
  /* padding: 0px 80px 80px 80px; */
  /* padding: 20px 20px 30px 20px; */
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  /* 改为顶部对齐 */
  align-items: center;
  /* align-items: flex-start; */
  /* 改为顶部对齐，避免内容过多时居中布局导致的问题 */
  /* min-height: calc(100vh - 200px); */
  margin-top: 52px;
  /* 调整内容区域高度 */
  position: relative;
  z-index: 2;
  width: 100%;
  box-sizing: border-box;
  flex: 1;
  /* 允许内容区域占据剩余空间 */
}

.input-section-container {
  /* height: calc(100vh - 200px); */
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-top: 50px;
  width: 100%;
  /* min-width: calc(100vw - 200px);; */
}

.input-section {
  /* height: calc(100vh - 200px); */
  width: 100%;
  max-width: 1400px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 32px;
  padding: 40px 60px;
  /* 调整内边距 */
  margin: 20px auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.8),
    inset 0 0 0 1px rgba(255, 255, 255, 0.4);
  transition: all 0.5s cubic-bezier(0.25, 1, 0.5, 1), background-color 0.3s, box-shadow 0.3s, opacity 0.3s, transform 0.3s;
  /* position: relative; */
  overflow: visible;
  /* 允许装饰效果溢出 */
  transform: translateZ(0);
  /* 添加关闭按钮的位置空间 */
  /* max-height: 90vh; 移除最大高度限制 */
  box-sizing: border-box;
}

body.dark .input-section {
  background: rgba(0, 0, 0, 0.226);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.05),
    inset 0 0 0 1px rgba(255, 255, 255, 0.05);
}



/* 动画相关样式 */
/* 进入动画 */
.section-fade2-enter-active {
  animation: section-in 0.2s cubic-bezier(0.14, 1, 0.84, 1);
  transition: opacity 0.2s ease-out;
}

/* 离开动画 - 取消动画效果 */
.section-fade2-leave-active {
  /* 移除动画效果 */
  transition: opacity 0s;
}

/* 动画相关样式 */
/* 进入动画 */
.section-fade-enter-active {
  animation: section-in 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  transition: opacity 0.5s ease-out;
}

/* 离开动画 - 取消动画效果 */
.section-fade-leave-active {
  /* 移除动画效果 */
  transition: opacity 0s;
}

/* 初始状态和结束状态 */
.section-fade-enter-from {
  opacity: 0;
}

/* 移除离开时的透明度变化 */
.section-fade-leave-to {
  opacity: 1;
}

/* 进入动画关键帧 */
@keyframes section-in {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }

  70% {
    opacity: 1;
    transform: translateY(-7px) scale(1.02);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 离开动画关键帧 - 取消变换效果 */
@keyframes section-out {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 卡片装饰元素的动画 */
.section-fade-enter-active .card-decoration {
  animation: decoration-in 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation-fill-mode: forwards;
  opacity: 0;
}

/* 取消离开时的装饰元素动画 */
.section-fade-leave-active .card-decoration {
  animation: none;
}

/* 为不同位置的装饰添加不同的延迟 */
.section-fade-enter-active .top-left {
  animation-delay: 0.1s;
}

.section-fade-enter-active .top-right {
  animation-delay: 0.2s;
}

.section-fade-enter-active .bottom-left {
  animation-delay: 0.25s;
}

.section-fade-enter-active .bottom-right {
  animation-delay: 0.3s;
}

/* 装饰元素进入动画 */
@keyframes decoration-in {
  0% {
    opacity: 0;
    transform: scale(0.5) translateY(20px);
    filter: blur(30px);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
    filter: blur(20px);
  }
}

/* 装饰元素离开动画 - 保持元素可见 */
@keyframes decoration-out {
  0% {
    opacity: 1;
    transform: scale(1);
    filter: blur(20px);
  }

  100% {
    opacity: 1;
    transform: scale(1);
    filter: blur(20px);
  }
}

/* 内容元素的动画 */
/* .section-fade-enter-active .content-layout {
  animation: content-in 0.1s;
  animation-delay: 0.2s;
  animation-fill-mode: both;
} */

/* 取消内容元素的离开动画 */
.section-fade-leave-active .content-layout {
  animation: none;
}

@keyframes content-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 内容元素离开动画 - 保持元素可见 */
@keyframes content-out {
  0% {
    opacity: 1;
    transform: translateY(0);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 隐藏时的样式 - 移除，因为现在使用 transition 组件 */
/* .input-section[style*="display: none"] {
  opacity: 0;
  transform: translateY(20px);
} */

.close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(24, 28, 248, 0.53);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-button:hover {
  background-color: rgba(131, 43, 246, 0.503);
  transform: rotate(-45deg);
}

body.dark .close-button {
  background-color: rgba(255, 255, 255, 0.1);
}

body.dark .close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.close-button .el-icon {
  font-size: 18px;
  color: #ddd;
}

body.dark .close-button .el-icon {
  color: #ddd;
}

.content-layout {
  display: flex;
  flex-direction: column;
  /* width: 600px; */
  gap: 30px;
  box-sizing: border-box;
  position: relative;
  /* 确保内容在装饰效果上方 */
  z-index: 1;
}

.select-panel {
  /* flex: 1.3; */
  /* 占比 1/3 */
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  box-sizing: border-box;
}

.creative-input-section {
  /* 300px */
  min-height: calc(100vh - 300px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  /* padding: 120px 0; */
  min-width: 800px;
  /* flex: 2; */
  /* 占比 2/3 */
}

.input-section:hover {
  box-shadow: 0 20px 40px rgba(99, 102, 241, 0.15),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.9),
    inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  transform: translateY(-5px);
}

body.dark .input-section:hover {
  box-shadow: 0 20px 40px rgba(99, 102, 241, 0.2),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.card-decoration {
  position: absolute;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(138, 92, 246, 0.1));
  filter: blur(20px);
  transition: all 0.6s ease, background 0.3s;
  pointer-events: none;
  /* 确保装饰不会干扰鼠标事件 */
  z-index: 0;
  /* 确保装饰位于内容下层 */
}

body.dark .card-decoration {
  opacity: 0.6;
}

.top-left {
  top: -20px;
  left: -20px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(99, 102, 241, 0.1));
}

body.dark .top-left {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(99, 102, 241, 0.08));
}

.top-right {
  top: -30px;
  right: -30px;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(168, 85, 247, 0.1));
}

body.dark .top-right {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(168, 85, 247, 0.08));
}

.bottom-left {
  bottom: -40px;
  left: -20px;
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.3), rgba(20, 184, 166, 0.1));
}

body.dark .bottom-left {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.2), rgba(20, 184, 166, 0.08));
}

.bottom-right {
  bottom: -30px;
  right: -30px;
  background: linear-gradient(135deg, rgba(20, 184, 166, 0.3), rgba(6, 182, 212, 0.1));
}

body.dark .bottom-right {
  background: linear-gradient(135deg, rgba(20, 184, 166, 0.2), rgba(6, 182, 212, 0.08));
}

.input-section:hover .card-decoration {
  transform: scale(1.2);
}



/* 在大屏幕设备上的样式 */
/* @media (min-width: 1440px) {
  .input-section {
    max-width: 1300px;
    padding: 60px 80px;
  }
} */

/* 平板设备样式 */
@media (max-width: 1024px) {
  .creative-input-section {
    min-width: 100%;
  }
}

/* 移动设备样式 */
@media (max-width: 768px) {
  .content-layout {
    flex-direction: column;
    gap: 25px;
  }

  .select-panel,
  .creative-input-section {
    flex: none;
    width: 100%;
  }

  .main-content {
    padding: 30px 15px 30px;
    min-height: auto;
    width: 100%;
    box-sizing: border-box;
  }

  .input-section {
    padding: 25px 15px;
    border-radius: 16px;
    max-width: 95%;
    margin: 10px auto;
  }

  .card-decoration {
    width: 50px;
    height: 50px;
  }

  .top-left,
  .top-right,
  .bottom-left,
  .bottom-right {
    width: 50px;
    height: 50px;
  }

  .ratio-options {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
  }

  .ratio-option {
    padding: 2px 10px 2px 8px;
  }

  .ratio-label {
    gap: 10px;
  }
}

/* 小型移动设备样式 */
@media (max-width: 480px) {
  .main-content {
    padding: 0px 10px 20px;
    /* 调整小型设备的顶部padding */
  }

  .input-section {
    padding: 20px 12px;
    border-radius: 12px;
    max-width: 100%;
    margin: 5px auto;
  }



  .card-decoration {
    display: none;
    /* 在小屏幕上隐藏装饰效果以提高性能 */
  }

  .ratio-options {
    grid-template-columns: 1fr;
  }

  .ratio-option {
    padding: 2px 8px;
  }

  .ratio-text {
    font-size: 14px;
    width: 40px;
  }

  .ratio-desc {
    font-size: 12px;
  }
}

.section-title {
  font-weight: 600;
  color: #334155;
  margin-bottom: 8px;
  font-size: 16px;
  transition: color 0.3s;
}

body.dark .section-title {
  color: var(--text-primary);
}

.sub-title {
  color: #64748b;
  font-size: 14px;
  margin-bottom: 20px;
  transition: color 0.3s;
}

body.dark .sub-title {
  color: var(--text-secondary);
}

.form-control {
  width: 100%;
  background-color: #fff;
  border: 1px solid #e2e8f0;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.5;
  color: #334155;
  transition: all 0.3s ease;
  margin-bottom: 24px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.02);
}

body.dark .form-control {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.form-control:focus {
  outline: none;
  border-color: #a5b4fc;
  box-shadow: 0 0 0 3px rgba(165, 180, 252, 0.25);
}

body.dark .form-control:focus {
  border-color: #818cf8;
  box-shadow: 0 0 0 3px rgba(129, 140, 248, 0.25);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  border-radius: 12px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  box-shadow: 0 4px 14px rgba(99, 102, 241, 0.2);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.3);
}

.btn-primary:active {
  transform: translateY(1px);
}

body.dark .btn-primary {
  box-shadow: 0 4px 14px rgba(99, 102, 241, 0.3);
}

body.dark .btn-primary:hover {
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
}

.btn-secondary {
  background-color: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

body.dark .btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
  background-color: #f1f5f9;
  color: #334155;
  transform: translateY(-2px);
}

body.dark .btn-secondary:hover {
  background-color: #27272a;
  color: var(--text-primary);
}

.image-uploader {
  background-color: #f8fafc;
  border: 2px dashed #e2e8f0;
  border-radius: 16px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

body.dark .image-uploader {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

.image-uploader:hover {
  border-color: #a5b4fc;
  background-color: #f1f5f9;
}

body.dark .image-uploader:hover {
  border-color: #818cf8;
  background-color: #27272a;
}

.color-option {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  margin-right: 10px;
  position: relative;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.color-option.selected {
  transform: scale(1.2);
  border-color: #fff;
  box-shadow: 0 0 0 2px #6366f1;
}

body.dark .color-option.selected {
  border-color: #27272a;
}

.toggle-switch {
  display: inline-block;
  height: 34px;
  position: relative;
  width: 60px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e2e8f0;
  transition: 0.4s;
  border-radius: 34px;
}

body.dark .slider {
  background-color: #3f3f46;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

input:checked+.slider {
  background-color: #6366f1;
}

input:checked+.slider:before {
  transform: translateX(26px);
}

.model-selector-container {
  padding: 0;
  gap: 12px;
  display: flex;
  flex-direction: column;
}

/* 比例选择器优化样式 */
.ratio-selector {
  padding: 0;
  gap: 12px;
  display: flex;
  flex-direction: column;
}

.section-desc {
  font-size: 13px;
  color: #94a3b8;
  margin-left: 8px;
  font-weight: normal;
}

body.dark .section-desc {
  color: #64748b;
}

.ratio-options {
  margin-top: 15px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 12px;
}

.ratio-option {
  display: flex;
  align-items: center;
  padding: 2px 18px 2px 10px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;

}

body.dark .ratio-option {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

.ratio-option:hover {
  border-color: #a5b4fc;
  background-color: #f9faff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

body.dark .ratio-option:hover {
  border-color: #818cf8;
  background-color: var(--bg-hover);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ratio-option.active {
  border-color: #4f46e5;
  background-color: #f1f5ff;
  box-shadow: 0 4px 10px rgba(79, 70, 229, 0.15);
}

body.dark .ratio-option.active {
  border-color: #6366f1;
  background-color: rgba(99, 102, 241, 0.1);
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.2);
}

.ratio-preview {
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* background-color: #f3f4f6; */
  border-radius: 6px;
  margin-right: 14px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

body.dark .ratio-preview {
  background-color: transparent;
  /* background-color: var(--bg-tertiary); */
}

.ratio-inner {
  background-color: #6366f1;
  border-radius: 3px;
  transition: all 0.2s ease;
}

.ratio-preview-16-9 .ratio-inner {
  width: 48px;
  height: 27px;
}

.ratio-preview-9-16 .ratio-inner {
  width: 27px;
  height: 48px;
}

.ratio-preview-1-1 .ratio-inner {
  width: 36px;
  height: 36px;
}

.ratio-option.active .ratio-preview {
  background-color: #eff6ff;
}

body.dark .ratio-option.active .ratio-preview {
  background-color: rgba(99, 101, 241, 0);
}

.ratio-option.active .ratio-inner {
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(99, 102, 241, 0.3);
}

.ratio-label {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20px;
  flex: 1;
  min-width: 0;
  /* 允许子元素缩小 */
}

.ratio-text {
  font-size: 16px;
  font-weight: 500;
  width: 50px;
  color: #1e293b;
  margin-bottom: 2px;
  flex-shrink: 0;
  /* 防止宽度缩小 */
}

body.dark .ratio-text {
  color: var(--text-primary);
}

.ratio-desc {
  font-size: 13px;
  color: #64748b;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

body.dark .ratio-desc {
  color: var(--text-secondary);
}

.ratio-option.active .ratio-text {
  color: #4f46e5;
}

body.dark .ratio-option.active .ratio-text {
  color: #818cf8;
}

/* 风格选择器容器样式优化 */
.style-selection-container {
  margin-bottom: 25px;
}

.style-selection-container .section-header {
  margin-bottom: 12px;
}

.voice-selector-content {
  /* height: 142px; */
  margin-top: 10px;
  padding: 2px;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.style-selector-content {
  /* overflow-y: auto;
  height: 160px; */
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.style-selector-container .section-header {
  margin-bottom: 12px;
}

.voice-selector-container {
  margin-bottom: 25px;
}

.section-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  position: relative;
  padding-bottom: 10px;
  transition: color 0.3s;
  text-align: left;
}

body.dark .section-header h2 {
  color: var(--text-primary);
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  transform: none;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  border-radius: 3px;
}

/* 响应式调整 */
/* @media (max-width: 1024px) {
  .ratio-preview {
    width: 60px;
    height: 60px;
  }

  .ratio-preview-16-9 .ratio-inner {
    width: 40px;
    height: 23px;
  }

  .ratio-preview-9-16 .ratio-inner {
    width: 23px;
    height: 40px;
  }

  .ratio-preview-1-1 .ratio-inner {
    width: 30px;
    height: 30px;
  }
} */

@media (max-width: 768px) {
  .content-layout {
    gap: 25px;
  }

  .ratio-options {
    gap: 10px;
  }

  .ratio-option {
    padding: 8px 10px;
  }

  .ratio-preview {
    width: 50px;
    height: 50px;
    margin-right: 10px;
  }

  .ratio-preview-16-9 .ratio-inner {
    width: 32px;
    height: 18px;
  }

  .ratio-preview-9-16 .ratio-inner {
    width: 18px;
    height: 32px;
  }

  .ratio-preview-1-1 .ratio-inner {
    width: 24px;
    height: 24px;
  }

  .ratio-text {
    font-size: 14px;
  }

  .ratio-desc {
    font-size: 12px;
  }
}

/* 开始创作按钮样式 */
.submit-button {
  margin-top: 24px;
  text-align: center;
}

.submit-button .el-button {
  width: 200px;
  height: 50px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 25px;
  background: linear-gradient(135deg, #409eff, #3a8ee6);
  border: none;
  box-shadow: 0 5px 15px rgba(58, 142, 230, 0.3);
  transition: all 0.3s ease;
}

.submit-button .el-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(58, 142, 230, 0.4);
  background: linear-gradient(135deg, #66b1ff, #409eff);
}

body.dark .submit-button .el-button {
  box-shadow: 0 5px 15px rgba(58, 142, 230, 0.2);
}

body.dark .submit-button .el-button:hover:not(:disabled) {
  box-shadow: 0 8px 20px rgba(58, 142, 230, 0.3);
}

.submit-button .el-button:disabled {
  background: #a0cfff;
  box-shadow: none;
  opacity: 0.7;
  cursor: not-allowed;
}

body.dark .submit-button .el-button:disabled {
  background: #4a6c94;
  opacity: 0.5;
}

/* 我的故事区域样式 */
.my-stories-section {
  box-sizing: border-box;
  width: 100%;
  padding: 20px;
  margin-top: 80px;
}



/* 单个模块显示时的样式 */
.select-panel>div:only-child:not([style*="display: none"]) {
  padding: 20px 0;
  margin: 0 auto;
  max-width: 800px;
}

.select-panel>div:only-child:not([style*="display: none"]) .section-header h2 {
  font-size: 24px;
  text-align: center;
}

.select-panel>div:only-child:not([style*="display: none"]) .section-header h2::after {
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
}

/* 设置标题样式 */
.setting-title {
  text-align: center;
  margin-bottom: 30px;
}

.setting-title h1 {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  background: linear-gradient(135deg, #409eff, #67c23a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

body.dark .setting-title h1 {
  color: var(--text-primary);
  background: linear-gradient(135deg, #67a9ff, #95d475);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 关闭按钮闪烁动画 */
@keyframes button-flash {

  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(99, 102, 241, 0);
    background-color: rgba(0, 0, 0, 0.1);
  }

  20% {
    transform: scale(1.2);
    box-shadow: 0 0 15px rgba(99, 102, 241, 0.8);
    background-color: rgba(99, 102, 241, 0.7);
  }

  40% {
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
    background-color: rgba(99, 102, 241, 0.5);
  }

  60% {
    transform: scale(1.15);
    box-shadow: 0 0 12px rgba(99, 102, 241, 0.7);
    background-color: rgba(99, 102, 241, 0.6);
  }

  80% {
    transform: scale(1.05);
    box-shadow: 0 0 8px rgba(99, 102, 241, 0.4);
    background-color: rgba(99, 102, 241, 0.4);
  }
}

/* 暗黑模式下的闪烁动画 */
@keyframes button-flash-dark {

  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(99, 102, 241, 0);
    background-color: rgba(255, 255, 255, 0.1);
  }

  20% {
    transform: scale(1.2);
    box-shadow: 0 0 15px rgba(99, 102, 241, 0.8);
    background-color: rgba(99, 102, 241, 0.7);
  }

  40% {
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
    background-color: rgba(99, 102, 241, 0.5);
  }

  60% {
    transform: scale(1.15);
    box-shadow: 0 0 12px rgba(99, 102, 241, 0.7);
    background-color: rgba(99, 102, 241, 0.6);
  }

  80% {
    transform: scale(1.05);
    box-shadow: 0 0 8px rgba(99, 102, 241, 0.4);
    background-color: rgba(99, 102, 241, 0.4);
  }
}

.close-button-flash {
  animation: button-flash 1.5s ease;
  z-index: 20;
}

body.dark .close-button-flash {
  animation: button-flash-dark 1.5s ease;
}

.close-button-flash .el-icon {
  color: white;
}

body.dark .close-button-flash .el-icon {
  color: white;
}
</style>