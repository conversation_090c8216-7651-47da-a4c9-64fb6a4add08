import axios from 'axios';

const DIFY_API_BASE_URL = 'https://agent.weilitech.cn/v1';
const DIFY_API_KEY = 'Bearer app-E3cZ1taqXoDR7xYE1pMLmrLw';

const difyService = {
  /**
   * 发送流式聊天消息
   * @param {Object} params 请求参数
   * @param {Object} params.inputs 输入参数
   * @param {string} params.query 查询文本
   * @param {string} params.conversation_id 会话ID
   * @param {string} params.user 用户ID
   * @param {Array} params.files 文件列表
   * @param {Function} onChunk 处理响应块的回调函数
   * @param {Function} onComplete 处理完成的回调函数
   * @param {Function} onError 处理错误的回调函数
   */
  sendStreamingChatMessage: async (params, onChunk, onComplete, onError) => {
    try {
      const token = localStorage.getItem('token')
      // const response = await fetch(`${DIFY_API_BASE_URL}/v1/chat-messages`, {
        const response = await fetch(`${DIFY_API_BASE_URL}/agent/chat/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // 'Authorization': DIFY_API_KEY,
          'accessToken': token,
        },
        body: JSON.stringify({
          ...params,
          response_mode: 'streaming'
        }),
      });

      if (!response.ok) {
        throw new Error(`Dify API error: ${response.status}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let fullResponse = '';
      let currentMessage = '';

      const processStream = async () => {
        while (true) {
          const { done, value } = await reader.read();
          
          if (done) {
            // 处理可能在缓冲区中的最后一条数据
            let buffersss = buffer.trim()
            console.log('/chat/messages:buffer:', buffersss);
            if (buffer.trim()) {
              try {
                // 处理最后可能的数据块
                if (buffer.startsWith('data:')) {
                  const jsonStr = buffer.slice(5).trim();
                  if (jsonStr && jsonStr !== '[DONE]') {
                    try {
                      const jsonData = JSON.parse(jsonStr);
                      onChunk(jsonData);
                    } catch (error) {
                      console.error('解析最终JSON数据出错:', error);
                    }
                  }
                }else{
                  console.error('错误消息 /chat/messages:buffer:', buffersss);
                  onError(buffersss);
                }
              } catch (error) {
                console.error('处理最终缓冲区时出错:', error);
              }
            }
            onComplete(fullResponse);
            break;
          }

          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;
          
          // 处理buffer中的完整JSON对象
          let newlineIndex;
          while ((newlineIndex = buffer.indexOf('\n')) !== -1) {
            const line = buffer.slice(0, newlineIndex).trim();
            buffer = buffer.slice(newlineIndex + 1);
            
            if (line.startsWith('data:')) {
              const jsonStr = line.slice(5).trim();
              console.log('jsonStr:', jsonStr);
              if (jsonStr === '[DONE]') {
                // 流结束
                continue;
              }
              
              try {
                const jsonData = JSON.parse(jsonStr);
                
                // 添加调试信息
                if (jsonData.event === 'message_end') {
                  console.log('API返回消息结束事件:', jsonStr);
                  console.log('消息结束事件metadata:', jsonData.metadata);
                }
                
                onChunk(jsonData);
                // 判断事件类型
                // if (jsonData.event === 'agent_message') {
                //   // 获取消息内容
                //   if (jsonData.answer !== undefined) {
                //     // 直接使用服务器返回的完整响应
                //     fullResponse = jsonData.answer;
                //   }
                  
                //   // 将完整响应传递给回调函数
                //   onChunk({
                //     answer: fullResponse,
                //     event: 'agent_message',
                //     conversation_id: jsonData.conversation_id
                //   });
                // } else if (jsonData.event === 'agent_thought') {
                //   // 思考过程可以不处理或者单独处理
                //   console.log('Agent思考:', jsonData.thought);
                  
                //   // 可选：将思考过程也传递给回调函数
                //   onChunk({
                //     thought: jsonData.thought,
                //     event: 'agent_thought',
                //     conversation_id: jsonData.conversation_id
                //   });
                // } else if (jsonData.event === 'message_end') {
                //   // 消息结束事件，传递最终完整响应
                //   onChunk({
                //     answer: fullResponse,
                //     event: 'message_end',
                //     conversation_id: jsonData.conversation_id
                //   });
                // } else {
                //   // 其他类型事件的处理
                //   console.log('未知事件类型:', jsonData.event);
                // }
              } catch (error) {
                console.error('解析JSON出错:', error, jsonStr);
              }
            }
          }
        }
      };

      await processStream();
    } catch (error) {
      console.error('Error in sendStreamingChatMessage:', error);
      onError(error);
    }
  },

  /**
   * 获取会话历史
   * @param {string} conversationId 会话ID
   * @returns {Promise<Array>} 历史消息列表
   */
  getConversationHistory: async (conversationId) => {
    try {
      const response = await axios.get(`/dify/v1/conversations/${conversationId}/messages`, {
        headers: {
          'Authorization': DIFY_API_KEY,
        }
      });
      
      return response.data.data;
    } catch (error) {
      console.error('Error fetching conversation history:', error);
      throw error;
    }
  },

  /**
   * 创建新会话
   * @returns {Promise<string>} 新会话ID
   */
  createConversation: async () => {
    try {
      const response = await axios.post(`/dify/v1/conversations`, {}, {
        headers: {
          'Authorization': DIFY_API_KEY,
          'Content-Type': 'application/json'
        }
      });
      
      return response.data.conversation_id;
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }
  },

  /**
   * 获取消息的建议问题
   * @param {string} messageId 消息ID
   * @param {string} userId 用户ID
   * @returns {Promise<Array>} 建议问题列表
   */
  getSuggestedQuestions: async (messageId, userId) => {
    try {
      const response = await axios.get(`/dify/v1/messages/${messageId}/suggested`, {
        headers: {
          'Authorization': DIFY_API_KEY,
          'Content-Type': 'application/json'
        },
        params: {
          user: userId
        }
      });
      
      return response.data;
    } catch (error) {
      console.error('Error fetching suggested questions:', error);
      throw error;
    }
  },

  /**
   * 获取会话列表
   * @param {string} userId 用户ID
   * @param {string} lastId 上一页最后一个会话ID（用于分页）
   * @param {number} limit 每页数量
   * @returns {Promise<Object>} 会话列表数据
   */
  getConversationsList: async (userId, lastId = '', limit = 20) => {
    try {
      const response = await axios.get(`/dify/v1/conversations`, {
        headers: {
          'Authorization': DIFY_API_KEY,
        },
        params: {
          user: userId,
          last_id: lastId,
          limit: limit
        }
      });
      
      return response.data;
    } catch (error) {
      console.error('Error fetching conversations list:', error);
      throw error;
    }
  },

  /**
   * 删除指定会话
   * @param {string} conversationId 会话ID
   * @param {string} userId 用户ID
   * @returns {Promise<Object>} 响应数据
   */
  deleteConversation: async (conversationId, userId) => {
    try {
      const response = await axios.delete(`/dify/v1/conversations/${conversationId}`, {
        headers: {
          'Authorization': DIFY_API_KEY,
          'Content-Type': 'application/json'
        },
        data: {
          user: userId
        }
      });
      
      return response.data;
    } catch (error) {
      console.error('Error deleting conversation:', error);
      throw error;
    }
  },

  /**
   * 获取会话消息历史记录
   * @param {string} userId 用户ID
   * @param {string} conversationId 会话ID
   * @param {number} limit 每页数量
   * @returns {Promise<Object>} 会话消息列表数据
   */
  getConversationMessages: async (userId, conversationId, limit = 20) => {
    try {
      const response = await axios.get(`/dify/v1/messages`, {
        headers: {
          'Authorization': DIFY_API_KEY,
        },
        params: {
          user: userId,
          conversation_id: conversationId,
          limit: limit
        }
      });
      
      return response.data;
    } catch (error) {
      console.error('Error fetching conversation messages:', error);
      throw error;
    }
  }
};

export default difyService; 