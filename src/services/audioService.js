/**
 * 音频处理服务
 * 提供音频相关的通用功能，包括获取音频时长、检查音频碰撞、查找可用时间点等
 */

/**
 * 获取音频文件时长
 * @param {string|File} audioSource - 音频URL或File对象
 * @returns {Promise<number>} - 音频时长（毫秒）
 */
export const getAudioDuration = async (audioSource) => {
    try {
        const audioEl = new Audio();
        let audioUrl;

        // 处理不同类型的音频源
        if (typeof audioSource === 'string') {
            audioUrl = audioSource;
        } else if (audioSource instanceof File) {
            audioUrl = URL.createObjectURL(audioSource);
        } else {
            throw new Error('无效的音频源');
        }

        // 等待音频加载完成
        await new Promise((resolve, reject) => {
            audioEl.onloadedmetadata = resolve;
            audioEl.onerror = (e) => {
                console.error('音频加载错误:', e);
                reject(e);
            };
            audioEl.src = audioUrl;
        });

        // 获取音频时长（毫秒）
        const audioDuration = Math.round(audioEl.duration * 1000);

        // 如果是临时URL，释放它
        if (audioSource instanceof File) {
            URL.revokeObjectURL(audioUrl);
        }

        return audioDuration;
    } catch (error) {
        console.error('获取音频时长失败:', error);
        return 5000; // 默认5秒
    }
};

/**
 * 检查音频是否与现有音频重叠
 * @param {Array} existingClips - 现有的音频片段数组
 * @param {Object} newClip - 新的音频片段
 * @param {number} clipIndex - 当前片段索引（用于排除自身，-1表示新添加的片段）
 * @returns {boolean} - 是否存在重叠
 */
export const checkAudioCollision = (existingClips, newClip, clipIndex = -1) => {
    if (!existingClips || existingClips.length === 0) {
        return false; // 没有现有片段，不存在碰撞
    }

    if (!newClip) {
        return false; // 新片段无效
    }

    // 确保时间值有效
    let newStartTime = newClip.startTrackTime;
    if (isNaN(newStartTime)) {
        newStartTime = 0;
    }

    let newEndTime = newStartTime;
    if (!isNaN(newClip.audioDuration)) {
        newEndTime = newStartTime + newClip.audioDuration;
    } else if (!isNaN(newClip.endTime) && !isNaN(newClip.startTime)) {
        newEndTime = newStartTime + (newClip.endTime - newClip.startTime);
    } else {
        newEndTime = newStartTime + 5000; // 默认5秒
    }

    // 检查每个现有片段是否与新片段重叠
    for (let i = 0; i < existingClips.length; i++) {
        // 跳过自己
        if (i === clipIndex) continue;

        const clip = existingClips[i];
        if (!clip) continue;

        // 确保时间值有效，但只在局部变量中使用有效值
        let clipStartTime = clip.startTrackTime;
        if (isNaN(clipStartTime)) {
            clipStartTime = 0;
        }

        let clipEndTime = clipStartTime;
        if (!isNaN(clip.endTime) && !isNaN(clip.startTime)) {
            clipEndTime = clipStartTime + (clip.endTime - clip.startTime);
        } else if (!isNaN(clip.audioDuration)) {
            clipEndTime = clipStartTime + clip.audioDuration;
        } else {
            clipEndTime = clipStartTime + 5000; // 默认5秒
        }

        // 检查是否重叠
        if (!(newEndTime <= clipStartTime || newStartTime >= clipEndTime)) {
            return true; // 有重叠
        }
    }

    return false; // 没有重叠
};

/**
 * 查找下一个可用的时间点
 * @param {Array} existingClips - 现有的音频片段数组
 * @param {number} duration - 需要放置的音频时长（毫秒）
 * @returns {number} - 可用的起始时间点（毫秒）
 */
export const findNextAvailableTime = (existingClips, duration) => {
    if (!existingClips || existingClips.length === 0) {
        return 0; // 没有现有片段，从0开始
    }

    if (duration === undefined || duration === null || isNaN(duration)) {
        duration = 5000; // 默认使用5秒
    }

    // 过滤掉无效的片段
    const validClips = existingClips.filter(clip => clip !== null && clip !== undefined);
    if (validClips.length === 0) {
        return 0;
    }

    // 按开始时间排序
    const sortedClips = [...validClips].sort((a, b) => {
        const aTime = isNaN(a.startTrackTime) ? 0 : a.startTrackTime;
        const bTime = isNaN(b.startTrackTime) ? 0 : b.startTrackTime;
        return aTime - bTime;
    });

    // 检查第一个片段前面是否有足够空间
    let firstClipStartTime = isNaN(sortedClips[0].startTrackTime) ? 0 : sortedClips[0].startTrackTime;
    if (firstClipStartTime >= duration) {
        return 0;
    }

    // 查找片段之间的空隙
    for (let i = 0; i < sortedClips.length - 1; i++) {
        // 确保时间值有效
        let clipStartTime = sortedClips[i].startTrackTime;
        if (isNaN(clipStartTime)) {
            clipStartTime = 0;
        }

        let clipEndTime = clipStartTime;
        if (!isNaN(sortedClips[i].endTime) && !isNaN(sortedClips[i].startTime)) {
            clipEndTime = clipStartTime + (sortedClips[i].endTime - sortedClips[i].startTime);
        } else if (!isNaN(sortedClips[i].audioDuration)) {
            clipEndTime = clipStartTime + sortedClips[i].audioDuration;
        } else {
            clipEndTime = clipStartTime + 5000; // 默认5秒
        }

        // 确保下一个片段的时间值有效
        let nextClipStartTime = sortedClips[i + 1].startTrackTime;
        if (isNaN(nextClipStartTime)) {
            nextClipStartTime = clipEndTime + 1000; // 放在当前片段后面1秒
        }

        if (nextClipStartTime - clipEndTime >= duration) {
            return clipEndTime;
        }
    }

    // 如果没有找到合适的空隙，放在最后一个片段后面
    const lastClip = sortedClips[sortedClips.length - 1];
    if (!lastClip) {
        return 0;
    }

    // 使用局部变量计算
    const lastClipStartTime = isNaN(lastClip.startTrackTime) ? 0 : lastClip.startTrackTime;
    let lastClipDuration;

    if (!isNaN(lastClip.endTime) && !isNaN(lastClip.startTime)) {
        lastClipDuration = lastClip.endTime - lastClip.startTime;
    } else if (!isNaN(lastClip.audioDuration)) {
        lastClipDuration = lastClip.audioDuration;
    } else {
        lastClipDuration = 5000; // 默认5秒
    }

    return lastClipStartTime + lastClipDuration;
};

/**
 * 创建音频片段对象
 * @param {Object} options - 音频片段选项
 * @param {string} options.id - 音频ID（可选，默认生成）
 * @param {string} options.name - 音频名称
 * @param {string} options.audioUrl - 音频URL
 * @param {number} options.audioDuration - 音频时长（毫秒）
 * @param {number} options.startTrackTime - 在音轨上的起始位置（毫秒）
 * @param {File} options.file - 音频文件对象（可选）
 * @returns {Object} - 音频片段对象
 */
export const createAudioClip = (options) => {
    const {
        id = `clip-${Date.now() + Math.random()}`,
        name = "音频片段",
        audioUrl,
        audioDuration = 5000,
        startTrackTime = 0,
        file = null
    } = options;

    return {
        id,
        name,
        audioUrl,
        audioDuration,
        startTime: 0, // 音频内部的起始时间
        endTime: audioDuration, // 音频内部的结束时间
        startTrackTime, // 音频在音轨上的位置
        volume: 100,
        fadeIn: 0,
        fadeOut: 0,
        file // 保存文件引用，以便后续处理
    };
};

/**
 * 格式化时间，将秒转换为 mm:ss 格式
 * @param {number} seconds - 秒数
 * @returns {string} - 格式化后的时间字符串 (mm:ss)
 */
export const formatTime = (seconds) => {
    try {
        // 确保seconds是有效的数字
        if (seconds === undefined || seconds === null || isNaN(seconds)) {
            return '00:00'; // 返回默认值
        }

        // 转换为数字类型（如果是字符串）
        const secs = Number(seconds);

        // 额外检查，确保是有限数
        if (!isFinite(secs)) {
            return '00:00'; // 返回默认值
        }

        // 计算分钟和秒
        const minutes = Math.floor(secs / 60);
        const remainingSecs = Math.floor(secs % 60);

        // 格式化为 mm:ss
        return `${String(minutes).padStart(2, '0')}:${String(remainingSecs).padStart(2, '0')}`;
    } catch (error) {
        console.error('formatTime发生异常:', error);
        return '00:00'; // 出错时返回默认值
    }
};

/**
 * 创建默认音轨
 * @param {number} index - 音轨索引（用于生成ID）
 * @returns {Object} - 默认音轨对象
 */
export const createDefaultTrack = (index = Date.now()) => {
    return {
        id: `track-${index}`,
        name: "背景音乐",
        type: "music",
        color: "#409eff33",
        muted: false,
        volume: 80,
        clips: []
    };
};

export default {
    getAudioDuration,
    checkAudioCollision,
    findNextAvailableTime,
    createAudioClip,
    formatTime,
    createDefaultTrack
};
