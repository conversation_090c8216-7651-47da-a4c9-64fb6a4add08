// Home页面的创作流程数据
export const creationProcessData = [
  {
    id: 1,
    title: '基本智能体',
    status: 'completed',
    progress: 100
  },
  {
    id: 2,
    title: '知识智能体',
    status: 'completed',
    progress: 100
  },
  {
    id: 3,
    title: '对话智能体',
    status: 'completed',
    progress: 100
  },
  {
    id: 4,
    title: '功能智能体',
    status: 'completed',
    progress: 100
  },
]

// 创作类型数据
export const creationTypes = [
  { id: 1, name: '图片', icon: 'Picture' },
  { id: 2, name: '对话', icon: 'ChatDotRound' },
  { id: 3, name: '文档导入', icon: 'Document' },
  { id: 4, name: '知识库', icon: 'Collection' },
  { id: 5, name: '插件', icon: 'Connection' },
  { id: 6, name: '数据集', icon: 'DataAnalysis' },
  { id: 7, name: '视频', icon: 'VideoCamera' },
  { id: 8, name: '其他功能', icon: 'More' }
]

// Create页面的智能体类型数据
export const intelligentAgentTypes = [
  {
    id: 1,
    title: '基本智能体',
    description: '快速创建一个基础功能的AI助手',
    icon: 'cpu',
    status: 'active'
  },
  {
    id: 2,
    title: '知识智能体',
    description: '创建一个拥有知识库能力的AI助手',
    icon: 'book',
    status: 'active'
  },
  {
    id: 3,
    title: '对话智能体',
    description: '创建一个专注于对话交流的AI助手',
    icon: 'message',
    status: 'active'
  },
  {
    id: 4,
    title: '动画智能体',
    description: '创建一个能够生成动画内容的AI助手',
    icon: 'film',
    status: 'active'
  }
] 