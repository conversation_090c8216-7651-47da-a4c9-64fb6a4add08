/**
 * API配置文件
 * 根据环境变量配置不同的API基础URL
 */

// 定义环境
const mode = import.meta.env.MODE
const isDev = mode === 'development'
const isDevServer = mode === 'development' && import.meta.env.COMMAND === 'serve'
const isBuildMode = mode === 'production' || mode === 'test' || mode === 'development-build'

// 获取API URL，构建环境使用定义的全局变量，开发服务器环境使用代理
const getBaseUrl = () => {
  if (isDevServer) {
    // 开发服务器环境使用代理
    return '/api'
  } else {
    // 构建环境直接使用完整URL
    const baseUrl = import.meta.env.VITE_API_URL || ''
    return baseUrl || '/api' // 如果环境变量为空，默认使用'/api'
  }
}

// 获取应用的基础路径，与vite.config.js中的base配置一致
const getBasePath = () => {
  if (isBuildMode) {
    // return '/agent'
    return ''
  } else {
    return ''
  }
}

// 导出API基础URL
export const API_BASE_URL = getBaseUrl()

// 导出应用基础路径
export const BASE_PATH = getBasePath()

// 导出当前环境模式
export const API_MODE = import.meta.env.VITE_API_MODE || 'development'

// 导出环境判断
export const IS_DEV = isDev
export const IS_DEV_SERVER = isDevServer
export const IS_BUILD_MODE = isBuildMode 