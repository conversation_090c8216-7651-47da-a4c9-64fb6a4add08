import { API_BASE_URL } from './config'

const DIFY_API_BASE_URL = API_BASE_URL;

// 添加全局 AbortController 变量
let currentController = null;

const chatService = {
  /**
   * 发送流式聊天消息
   * @param {Object} params 请求参数
   * @param {Object} params.inputs 输入参数
   * @param {string} params.query 查询文本
   * @param {string} params.conversation_id 会话ID
   * @param {string} params.user 用户ID
   * @param {Array} params.files 文件列表
   * @param {Function} onChunk 处理响应块的回调函数
   * @param {Function} onComplete 处理完成的回调函数
   * @param {Function} onError 处理错误的回调函数
   */
  sendStreamingChatMessage: async (params, onChunk, onComplete, onError) => {
    try {
      // 如果有正在进行的请求，先取消它
      if (currentController) {
        currentController.abort();
      }
      
      // 创建新的 AbortController
      currentController = new AbortController();
      const signal = currentController.signal;
      console.log('创建新的 AbortController', currentController);

      const token = localStorage.getItem('token')
      // const response = await fetch(`${DIFY_API_BASE_URL}/v1/chat-messages`, {
        const response = await fetch(`${DIFY_API_BASE_URL}/agent/chat/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // 'Authorization': DIFY_API_KEY,
          'accessToken': token,
        },
        body: JSON.stringify({
          ...params,
          response_mode: 'streaming'
        }),
        signal, // 添加 signal 以支持取消
      });

      if (!response.ok) {
        throw new Error(`Dify API error: ${response.status}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let fullResponse = '';
      let currentMessage = '';

      const processStream = async () => {
        try {
          while (true) {
            const { done, value } = await reader.read();
            
            if (done) {
              // 处理可能在缓冲区中的最后一条数据
              let buffersss = buffer.trim()
              console.log('/chat/messages:buffer:', buffersss);
              if (buffer.trim()) {
                try {
                  // 处理最后可能的数据块
                  if (buffer.startsWith('data:')) {
                    const jsonStr = buffer.slice(5).trim();
                    if (jsonStr && jsonStr !== '[DONE]') {
                      try {
                        const jsonData = JSON.parse(jsonStr);
                        onChunk(jsonData);
                      } catch (error) {
                        console.error('解析最终JSON数据出错:', error);
                      }
                    }
                  }else{
                    console.error('错误消息 /chat/messages:buffer:', buffersss);
                    onError(buffersss);
                  }
                } catch (error) {
                  console.error('处理最终缓冲区时出错:', error);
                }
              }
              onComplete(fullResponse);
              break;
            }

            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;
            
            // 处理buffer中的完整JSON对象
            let newlineIndex;
            while ((newlineIndex = buffer.indexOf('\n')) !== -1) {
              const line = buffer.slice(0, newlineIndex).trim();
              buffer = buffer.slice(newlineIndex + 1);
              
              if (line.startsWith('data:')) {
                const jsonStr = line.slice(5).trim();
                if (jsonStr === '[DONE]') {
                  // 流结束
                  continue;
                }
                
                try {
                  const jsonData = JSON.parse(jsonStr);
                  console.log('jsonStr:', jsonData);
                  
                  // 添加调试信息
                  if (jsonData.event === 'message_end') {
                    console.log('API返回消息结束事件:', jsonStr);
                    console.log('消息结束事件metadata:', jsonData.metadata);
                  }
                  
                  onChunk(jsonData);
                  // 判断事件类型
                  // if (jsonData.event === 'agent_message') {
                  //   // 获取消息内容
                  //   if (jsonData.answer !== undefined) {
                  //     // 直接使用服务器返回的完整响应
                  //     fullResponse = jsonData.answer;
                  //   }
                    
                  //   // 将完整响应传递给回调函数
                  //   onChunk({
                  //     answer: fullResponse,
                  //     event: 'agent_message',
                  //     conversation_id: jsonData.conversation_id
                  //   });
                  // } else if (jsonData.event === 'agent_thought') {
                  //   // 思考过程可以不处理或者单独处理
                  //   console.log('Agent思考:', jsonData.thought);
                    
                  //   // 可选：将思考过程也传递给回调函数
                  //   onChunk({
                  //     thought: jsonData.thought,
                  //     event: 'agent_thought',
                  //     conversation_id: jsonData.conversation_id
                  //   });
                  // } else if (jsonData.event === 'message_end') {
                  //   // 消息结束事件，传递最终完整响应
                  //   onChunk({
                  //     answer: fullResponse,
                  //     event: 'message_end',
                  //     conversation_id: jsonData.conversation_id
                  //   });
                  // } else {
                  //   // 其他类型事件的处理
                  //   console.log('未知事件类型:', jsonData.event);
                  // }
                } catch (error) {
                  console.error('解析JSON出错:', error, jsonStr);
                }
              }
            }
          }
        } catch (error) {
          if (error.name === 'AbortError') {
            console.log('请求被取消');
            // 调用错误回调，但标记为被取消
            onError({ isCancelled: true, message: '请求已取消' });
          } else {
            throw error; // 重新抛出其他错误
          }
        }
      };

      await processStream();
    } catch (error) {
      if (error.name === 'AbortError') {
        console.log('请求被取消');
        onError({ isCancelled: true, message: '请求已取消' });
      } else {
        console.error('Error in sendStreamingChatMessage:', error);
        onError(error);
      }
    } 
    // finally {
    //   // 请求完成后清除 controller 引用
    //   if (currentController) {
    //     currentController = null;
    //     console.log('请求完成后清除 controller 引用', currentController);
    //   }
    // }
  },

  /**
   * 取消当前正在进行的流式请求
   * @returns {boolean} 是否成功取消了请求
   */
  cancelStreamingRequest: () => {
    if (currentController) {
      console.log('取消流式请求');
      currentController.abort();
      currentController = null;
      return true;
    }else{
      console.log('没有流式请求', currentController);
    }
    return false;
  }
};

export default chatService; 