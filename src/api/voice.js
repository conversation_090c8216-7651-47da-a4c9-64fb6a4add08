import axios from 'axios'
import { createHttpInterceptors } from '../utils/httpErrorHandler'

// 创建API请求实例
const apiClient = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Accept': '*/*',
    'Accept-Language': 'zh-CN',
    'User-Agent': 'VueApp'
  }
})

// 添加请求拦截器，在每个请求中添加token和时间戳
apiClient.interceptors.request.use(
  config => {
    // 从localStorage或其他存储中获取token
    const accessToken = localStorage.getItem('token') || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************.tWNu81MtJXy9hsoryDH_GOB4ndIsNnYACGPVkLy1jX0'

    // 生成当前时间戳
    const timestamp = Date.now()

    config.headers.accessToken = accessToken
    config.headers.timestamp = timestamp

    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 添加响应拦截器处理HTTP错误
apiClient.interceptors.response.use(...createHttpInterceptors())

/**
 * 获取音色列表
 * @param {Object} params - 请求参数
 * @param {String} params.language - 语言筛选
 * @param {Number} params.sex - 性别筛选 (1为男, 2为女)
 * @param {Number} params.type - 类型筛选 (默认为1)
 * @returns {Promise} - 返回音色列表数据
 */
export function getVoiceList(params = {}) {
  return apiClient.get('/agent/v1/sound/list', {
    params: {
      language: params.language || '',
      sex: params.sex || '',
      type: params.type || 1
    }
  })
}

/**
 * 根据ID获取音色详情
 * @param {Number} id - 音色ID
 * @returns {Promise} - 返回音色详情数据
 */
export function getVoiceById(id) {
  return apiClient.get(`/agent/v1/sound/${id}`)
}

/**
 * 将外部URL转换为本地代理URL
 * @param {String} url - 原始URL
 * @returns {String} - 转换后的代理URL
 */
export function toProxyUrl(url) {
  if (!url) return '';
  if (url.startsWith('/api')) return url;
  
  try {
    // 如果是完整URL，则提取路径部分并添加代理前缀
    if (url.startsWith('http')) {
      return `/api${new URL(url).pathname}`;
    }
    // 否则直接加上代理前缀
    return `/api/${url.replace(/^\//, '')}`;
  } catch (error) {
    console.error('URL处理错误:', error);
    return url;
  }
}

export default {
  getVoiceList,
  getVoiceById,
  toProxyUrl
} 