import axios from 'axios';
import { API_BASE_URL } from './config';
import { createHttpInterceptors } from '../utils/httpErrorHandler';

// 创建API请求实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept-Language': 'zh-CN'
  }
});

// 添加请求拦截器
apiClient.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    const timestamp = new Date().getTime();
    config.headers.timestamp = timestamp.toString();
    if (token) {
      config.headers.accessToken = token;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 添加响应拦截器处理HTTP错误
apiClient.interceptors.response.use(...createHttpInterceptors());

/**
 * 提交图片修改请求
 * @param {Object} data - 修改图片的参数
 * @param {string} data.sourceImageUrl - 需要修改的图片源URL
 * @param {string} data.prompt - 图片修改的提示词
 * @param {string} data.conversationId - 会话ID
 * @param {number} data.contentType - 内容类型 (2-场景,3-角色,4-分镜)
 * @param {string} data.primaryId - 一级ID
 * @param {string} [data.secondaryId] - 二级ID (可选)
 * @param {string} [data.characterImageUrl1] - 参考角色图片1 (可选)
 * @param {string} [data.characterImageUrl2] - 参考角色图片2 (可选)
 * @param {string} [data.sceneImageUrl1] - 参考场景图片1 (可选)
 * @returns {Promise<{modifyCode: string}>} - 返回修改码
 */
export function submitImageModify(data) {
  return apiClient.post('/agent/ai-image/modify', data)
    .then(response => {
      if (response.data && response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data?.errMessage || '提交图片修改请求失败');
      }
    });
}

/**
 * 轮询查询修改结果
 * @param {string} modifyCode - 修改码
 * @returns {Promise<Object>} - 返回修改结果
 */
export function getImageModifyResult(modifyCode) {
  return apiClient.get(`/agent/ai-image/modify/result/${modifyCode}`)
    .then(response => {
      if (response.data && response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data?.errMessage || '获取图片修改结果失败');
      }
    });
}

/**
 * 查询历史图片URL列表
 * @param {Object} params - 查询参数
 * @param {string} params.primaryId - 一级ID
 * @param {string} [params.secondaryId] - 二级ID (可选)
 * @param {string} params.conversationId - 会话ID
 * @returns {Promise<Object>} - 返回历史图片URL列表
 */
export function getSourceImageList(params) {
  return apiClient.get('/agent/ai-image/modify/source-images', { params })
    .then(response => {
      if (response.data && response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data?.errMessage || '获取历史图片列表失败');
      }
    });
}

/**
 * 确认图片修改（保存修改结果）
 * @param {string} modifyCode - 修改码
 * @returns {Promise<Object>} - 返回确认结果
 */
export function confirmImageModify(modifyCode) {
  return apiClient.post('/agent/ai-image/confirm', { modifyCode })
    .then(response => {
      if (response.data && response.data.success) {
        return response.data;
      } else {
        throw new Error(response.data?.errMessage || '确认图片修改失败');
      }
    });
}

/**
 * 提交图片局部重绘请求
 * @param {Object} data - 局部重绘参数
 * @param {string} data.originalImageUrl - 需要重绘的图片源URL
 * @param {string} data.maskImageUrl - 遮罩图片URL，标记需要重绘的区域
 * @param {string} data.prompt - 重绘提示词
 * @param {string} data.shotId - 分镜ID
 * @returns {Promise<{inpaintingCode: string}>} - 返回处理码
 */
export function submitImageInpainting(data) {
  return apiClient.post('/agent/shot-image-edit/submit', data)
    .then(response => {
      if (response.data && response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data?.errMessage || '提交图片局部重绘请求失败');
      }
    });
}

/**
 * 查询局部重绘结果
 * @param {string} inpaintingCode - 处理码
 * @returns {Promise<Object>} - 返回处理结果
 */
export function getImageInpaintingResult(inpaintingCode) {
  return apiClient.get(`/agent/ai-image/inpainting/result/${inpaintingCode}`)
    .then(response => {
      if (response.data && response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data?.errMessage || '获取图片局部重绘结果失败');
      }
    });
} 