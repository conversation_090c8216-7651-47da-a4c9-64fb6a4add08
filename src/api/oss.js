import OSS from 'ali-oss';
import axios from 'axios';
import { ElMessage } from 'element-plus';
import { API_BASE_URL } from './config';

// 使用新的上传方法有以下优势：
// 自动管理STS Token，包括过期刷新
// 更好的错误处理和异常提示
// 文件名生成更加安全，防止冲突
// 可以按不同的业务场景设置不同的存储目录
// 支持上传进度显示和取消上传等高级功能
// 需要后端支持：
// 后端需要提供STS Token获取接口（/agent/sts/oss/token）
// Token数据格式需包含：accessKeyId、accessKeySecret、securityToken和expiration
// 使用时，可以根据实际情况修改oss.js中的默认配置，比如OSS的region和bucket名称。
// 这个实现方法既保证了安全性，又提高了上传的效率和可靠性。

// OSS客户端缓存
let ossClient = null;
// 凭证过期时间（提前30秒刷新）
let expireTime = 0;

/**
 * 获取STS临时凭证
 * @returns {Promise<{accessKeyId: string, accessKeySecret: string, securityToken: string, expiration: string}>}
 */
export async function getSTSToken() {
  try {
    const response = await axios.get(`${API_BASE_URL}/agent/sts/oss/token`, {
      headers: {
        accessToken: localStorage.getItem('token') || ''
      }
    });
    
    if (response.data && response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data?.errMessage || '获取STS临时凭证失败');
    }
  } catch (error) {
    console.error('获取STS凭证失败:', error);
    throw error;
  }
}

/**
 * 初始化OSS客户端
 * @returns {Promise<OSS>} OSS客户端实例
 */
export async function initOssClient() {
  const now = Date.now();
  
  // 如果客户端存在且未过期，直接返回
  // if (ossClient && expireTime > now) {
  //   return ossClient;
  // }
  
  try {
    // 获取STS凭证
    const stsToken = await getSTSToken();
    
    // 设置凭证过期时间（提前30秒过期，以便刷新）
    expireTime = new Date(stsToken.expiration).getTime() - 30000;
    
    // 创建OSS客户端
    ossClient = new OSS({
      region: 'oss-cn-shanghai', // OSS区域，根据实际情况修改
      accessKeyId: stsToken.accessKeyId,
      accessKeySecret: stsToken.accessKeySecret,
      stsToken: stsToken.securityToken,
      bucket: stsToken.bucketName, // 默认存储桶名称，可在上传时覆盖
      secure: true, // 使用HTTPS
      refreshSTSToken: async () => {
        const refreshedToken = await getSTSToken();
        return {
          accessKeyId: refreshedToken.accessKeyId,
          accessKeySecret: refreshedToken.accessKeySecret,
          stsToken: refreshedToken.securityToken
        };
      },
      refreshSTSTokenInterval: Math.max(1000 * 60 * 5, expireTime - now) // 5分钟或凭证剩余时间
    });
    
    return {ossClient: ossClient, env: stsToken.env};
  } catch (error) {
    console.error('初始化OSS客户端失败:', error);
    ElMessage.error('初始化OSS客户端失败，请重试');
    throw error;
  }
}

/**
 * 生成唯一文件名
 * @param {File} file 文件对象
 * @returns {string} 生成的唯一文件名
 */
function generateUniqueFileName(file) {
  const timestamp = new Date().getTime();
  const randomStr = Math.random().toString(36).substring(2, 8);

  // 处理File对象和Blob对象的差异
  let extension = '';
  if (file.name && file.name.includes('.')) {
    extension = file.name.split('.').pop();
  } else if (file.type) {
    // 从MIME类型推断扩展名
    const mimeToExt = {
      'image/png': 'png',
      'image/jpeg': 'jpg',
      // ... more MIME types
    };
    extension = mimeToExt[file.type] || '';
  }

  return `${timestamp}_${randomStr}${extension ? '.' + extension : ''}`;
}

/**
 * 上传文件到OSS
 * @param {File} file 要上传的文件
 * @param {string} [directory='uploads'] 上传目录
 * @param {string} [fileName] 自定义文件名，不提供则自动生成
 * @param {Object} [options] 上传选项
 * @returns {Promise<{url: string, name: string}>} 上传结果
 */
export async function uploadToOSS(file, conversationId, options = {}) {
  try {

    // 初始化客户端
    const client = await initOssClient();

    const fileName = `dify/${client.env}/${conversationId}/image`
    
    // 处理目录路径
    // const dir = directory.endsWith('/') ? directory : `${directory}/`;
    
    // 生成OSS对象名
    // const objectName = `${dir}${fileName || generateUniqueFileName(file)}`;
    const objectName = `${fileName}${generateUniqueFileName(file)}`;
    
    // 设置上传选项
    const uploadOptions = {
      timeout: 60000, // 60秒超时
      headers: {
        'Content-Type': file.type || 'application/octet-stream'
      },
      ...options
    };

    console.log('上传文件到OSS:', objectName, file, uploadOptions);
    
    // 上传文件
    const result = await client.ossClient.put(objectName, file, uploadOptions);
    
    if (!result || !result.url) {
      throw new Error('上传失败，未获取到URL');
    }
    
    return {
      url: result.url,
      objectName: objectName,
      name: objectName.split('/').pop()
    };
  } catch (error) {
    console.error('上传文件到OSS失败:', error);
    ElMessage.error(`上传失败: ${error.message || '未知错误'}`);
    throw error;
  }
}

/**
 * 批量上传文件到OSS
 * @param {Array<File>} files 文件数组
 * @param {string} [directory='uploads'] 上传目录
 * @param {Object} [options] 上传选项
 * @returns {Promise<Array<{url: string, name: string}>>} 上传结果数组
 */
export async function batchUploadToOSS(files, directory = 'uploads', options = {}) {
  try {
    const uploadPromises = files.map(file => uploadToOSS(file, directory, null, options));
    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error('批量上传文件到OSS失败:', error);
    ElMessage.error(`批量上传失败: ${error.message || '未知错误'}`);
    throw error;
  }
} 