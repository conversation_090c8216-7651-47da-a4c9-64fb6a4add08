/* Ant Design X Vue 组件样式覆盖 */

/* 对话气泡内容样式 */
:where(.css-dev-only-do-not-override-1r7p0rg).ant-bubble .ant-bubble-content,
:where(.css-dev-only-do-not-override-dsv36).ant-bubble .ant-bubble-content,
:where(.css-dev-only-do-not-override-1qst7a8).ant-bubble .ant-bubble-content,
.ant-bubble .ant-bubble-content {
  text-align: left !important;
  white-space: pre-line !important;
}

/* 确保所有 Bubble 组件内容左对齐 */
.ax-bubble-content,
.ant-bubble-content {
  text-align: left !important;
  white-space: pre-line !important;
  word-break: break-word !important;
  overflow-wrap: break-word !important;
}


.dark .ant-bubble-content-outlined{
  border: none !important;
  background-color: none !important;
}


.dark .ant-bubble .ant-bubble-content-outlined{
  border: 0px !important;
  padding: 10px !important;
  background-color: #635fe599 !important;
}

.dark .ax-bubble-content,
.dark .ant-bubble-content,
.dark .ant-typography{
  color: #fff !important;
}

.dark .ant-bubble-content-filled{
  background-color: var(--border-color) !important;
}

/* 气泡容器样式 */
.ant-bubble {
  max-width: none !important;
  padding: 0px;
}

.dark .ant-sender-input::-webkit-input-placeholder{
  color: #7f7f7f !important;
}

.dark .ant-input{
  color: #fff;
}

.dark .ant-typography h1,
.dark .ant-typography h2,
.dark .ant-typography h3,
.dark .ant-typography h4,
.dark .ant-typography h5,
.dark .ant-typography h6{
  color: #fff !important;
}

.dark .ant-sender{
  border: 0 1px 2px 0 rgba(0, 0, 0, 0.03),0 1px 6px -1px rgba(0, 0, 0, 0.02),0 2px 4px 0 rgba(0, 0, 0, 0.02);
}

/* 气泡列表容器样式 */
.ax-bubble-list,
.ant-bubble-list {
  height: auto !important;
  min-height: 400px !important; 
}

.chat-bubble-list {
  max-height: none !important;
  min-height: 400px !important;
  overflow-y: auto !important;
}

/* 打字机效果光标 */
.ax-typing-cursor, 
.ant-typing-cursor {
  display: inline-block !important;
  width: 2px !important;
  height: 16px !important;
  background-color: #409eff !important;
  animation: cursor-blink 1s step-end infinite !important;
  vertical-align: text-bottom !important;
  margin-left: 2px !important;
}

@keyframes cursor-blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
} 