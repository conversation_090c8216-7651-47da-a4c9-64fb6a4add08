@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* 为价格部分添加一些额外的变量 */
  --color-background-alt: #f8f9ff;
  --color-card-bg-rgb: 255, 255, 255;
  --color-accent-rgb: 94, 92, 230;
  --color-border-rgb: 200, 200, 200;
  --color-text: #333;
  --color-text-secondary: #666;
}

@media (prefers-color-scheme: dark) {
  :root {
    /* 为价格部分深色主题添加变量 */
    --color-background-alt: #111827;
    --color-card-bg-rgb: 30, 30, 40;
    --color-border-rgb: 60, 60, 70;
    --color-text: #f0f0f0;
    --color-text-secondary: #b0b0b0;
  }
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  /* margin-bottom: 1rem; */
  background: linear-gradient(to right, rgba(var(--color-accent-rgb), 0.8), rgba(var(--color-accent-rgb), 1));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
/* 
body.dark .section-title {
  background: linear-gradient(to right, rgba(var(--color-accent-rgb), 0.9), rgba(var(--color-accent-rgb), 1));
} */

.section-subtitle {
  font-size: 1.2rem;
  text-align: center;
  max-width: 700px;
  margin: 0 auto 3rem;
  color: var(--color-text-secondary);
}

body.dark .section-subtitle {
  color: var(--color-text-secondary);
}

.pricing-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 2rem;
}

.pricing-card {
  flex: 1;
  min-width: 300px;
  max-width: 350px;
  border-radius: 1rem;
  overflow: hidden;
  background: rgba(var(--color-card-bg-rgb), 1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  border: 1px solid rgba(var(--color-border-rgb), 0.2);
}

body.dark .pricing-card {
  background: rgba(var(--bg-card), 1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(var(--color-border-rgb), 0.3);
}

.pricing-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(var(--color-accent-rgb), 0.15);
}

body.dark .pricing-card:hover {
  box-shadow: 0 15px 35px rgba(var(--color-accent-rgb), 0.25);
}

.pricing-card.popular {
  transform: scale(1.05);
  border: 2px solid rgba(var(--color-accent-rgb), 0.5);
  box-shadow: 0 15px 35px rgba(var(--color-accent-rgb), 0.2);
}

body.dark .pricing-card.popular {
  border: 2px solid rgba(var(--color-accent-rgb), 0.6);
  box-shadow: 0 15px 35px rgba(var(--color-accent-rgb), 0.3);
}

.pricing-card.popular:hover {
  transform: scale(1.05) translateY(-10px);
}

.popular-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: linear-gradient(to right, rgba(var(--color-accent-rgb), 0.8), rgba(var(--color-accent-rgb), 1));
  color: white;
  padding: 0.3rem 1rem;
  font-size: 0.8rem;
  border-radius: 2rem;
  font-weight: 600;
}

.card-header {
  padding: 2rem 1.5rem;
  text-align: center;
  border-bottom: 1px solid rgba(var(--color-border-rgb), 0.1);
}

body.dark .card-header {
  border-bottom: 1px solid rgba(var(--color-border-rgb), 0.2);
}

.card-header h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
  color: var(--color-text);
}

body.dark .card-header h3 {
  color: var(--color-text);
}

.price {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--color-text);
}

body.dark .price {
  color: var(--color-text);
}

.price span {
  font-size: 1rem;
  font-weight: 400;
  color: var(--color-text-secondary);
}

body.dark .price span {
  color: var(--color-text-secondary);
}

.card-header p {
  font-size: 0.9rem;
  color: var(--color-text-secondary);
}

body.dark .card-header p {
  color: var(--color-text-secondary);
}

.card-body {
  padding: 2rem 1.5rem;
}

.features-list {
  list-style-type: none;
  padding: 0;
  margin-bottom: 2rem;
}

.features-list li {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 1rem;
  color: var(--color-text);
}

body.dark .features-list li {
  color: var(--color-text);
}

.features-list li .el-icon {
  margin-right: 0.75rem;
  color: rgb(var(--color-accent-rgb));
  font-size: 1.2rem;
}

body.dark .features-list li .el-icon {
  color: rgb(var(--color-accent-rgb));
}

.pricing-btn {
  display: block;
  width: 100%;
  padding: 0.8rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(var(--color-accent-rgb), 0.5);
  background: transparent;
  color: rgb(var(--color-accent-rgb));
}

body.dark .pricing-btn {
  border: 1px solid rgba(var(--color-accent-rgb), 0.6);
}

.pricing-btn:hover {
  background: rgba(var(--color-accent-rgb), 0.1);
}

body.dark .pricing-btn:hover {
  background: rgba(var(--color-accent-rgb), 0.2);
}

.pricing-btn.highlighted {
  background: linear-gradient(to right, rgba(var(--color-accent-rgb), 0.8), rgba(var(--color-accent-rgb), 1));
  color: white;
  border: none;
}

body.dark .pricing-btn.highlighted {
  background: linear-gradient(to right, rgba(var(--color-accent-rgb), 0.9), rgba(var(--color-accent-rgb), 1));
}

.pricing-btn.highlighted:hover {
  box-shadow: 0 5px 15px rgba(var(--color-accent-rgb), 0.3);
}

body.dark .pricing-btn.highlighted:hover {
  box-shadow: 0 5px 15px rgba(var(--color-accent-rgb), 0.4);
}

/* 媒体查询-响应式设计 */
@media (max-width: 992px) {
  .pricing-cards {
    flex-direction: column;
    align-items: center;
  }
  
  .pricing-card {
    max-width: 450px;
    width: 100%;
    margin-bottom: 2rem;
  }
  
  .pricing-card.popular {
    transform: scale(1);
    order: -1;
  }
  
  .pricing-card.popular:hover {
    transform: translateY(-10px);
  }
}

/* 确保空的列表项在暗色系下显示正确 */
body.dark .features-list li:empty {
  visibility: hidden;
} 