import { ref, reactive } from 'vue';

// 全局下拉面板管理器
class DropdownManager {
  constructor() {
    // 当前活跃的下拉面板ID
    this.activeDropdownId = ref(null);
    // 所有注册的下拉面板实例
    this.dropdownInstances = reactive(new Map());
  }

  /**
   * 注册下拉面板实例
   * @param {string} id - 下拉面板唯一标识
   * @param {object} instance - 下拉面板实例
   */
  register(id, instance) {
    this.dropdownInstances.set(id, instance);
  }

  /**
   * 注销下拉面板实例
   * @param {string} id - 下拉面板唯一标识
   */
  unregister(id) {
    this.dropdownInstances.delete(id);
    // 如果注销的是当前活跃的面板，清除活跃状态
    if (this.activeDropdownId.value === id) {
      this.activeDropdownId.value = null;
    }
  }

  /**
   * 显示指定的下拉面板，关闭其他所有面板
   * @param {string} id - 要显示的下拉面板ID
   */
  show(id) {
    // 如果当前已有活跃面板且不是要显示的面板，先关闭它
    if (this.activeDropdownId.value && this.activeDropdownId.value !== id) {
      this.hide(this.activeDropdownId.value);
    }
    
    // 设置新的活跃面板
    this.activeDropdownId.value = id;
    
    // 通知对应实例显示
    const instance = this.dropdownInstances.get(id);
    if (instance && instance.show) {
      instance.show();
    }
  }

  /**
   * 隐藏指定的下拉面板
   * @param {string} id - 要隐藏的下拉面板ID
   */
  hide(id) {
    // 如果是当前活跃面板，清除活跃状态
    if (this.activeDropdownId.value === id) {
      this.activeDropdownId.value = null;
    }
    
    // 通知对应实例隐藏
    const instance = this.dropdownInstances.get(id);
    if (instance && instance.hide) {
      instance.hide();
    }
  }

  /**
   * 隐藏所有下拉面板
   */
  hideAll() {
    this.dropdownInstances.forEach((instance, id) => {
      if (instance && instance.hide) {
        instance.hide();
      }
    });
    this.activeDropdownId.value = null;
  }

  /**
   * 切换指定下拉面板的显示状态
   * @param {string} id - 下拉面板ID
   */
  toggle(id) {
    if (this.activeDropdownId.value === id) {
      this.hide(id);
    } else {
      this.show(id);
    }
  }

  /**
   * 检查指定面板是否为活跃状态
   * @param {string} id - 下拉面板ID
   * @returns {boolean}
   */
  isActive(id) {
    return this.activeDropdownId.value === id;
  }
}

// 创建全局单例实例
const dropdownManager = new DropdownManager();

/**
 * 下拉面板管理 composable
 * @param {string} id - 下拉面板唯一标识（可选，如果不提供会自动生成）
 * @returns {object} 管理方法和状态
 */
export function useDropdownManager(id = null) {
  // 如果没有提供ID，生成一个唯一ID
  const dropdownId = id || `dropdown_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  /**
   * 注册当前下拉面板实例
   * @param {object} instance - 实例对象，包含 show 和 hide 方法
   */
  const register = (instance) => {
    dropdownManager.register(dropdownId, instance);
  };

  /**
   * 注销当前下拉面板实例
   */
  const unregister = () => {
    dropdownManager.unregister(dropdownId);
  };

  /**
   * 显示当前下拉面板
   */
  const show = () => {
    dropdownManager.show(dropdownId);
  };

  /**
   * 隐藏当前下拉面板
   */
  const hide = () => {
    dropdownManager.hide(dropdownId);
  };

  /**
   * 切换当前下拉面板显示状态
   */
  const toggle = () => {
    dropdownManager.toggle(dropdownId);
  };

  /**
   * 检查当前面板是否为活跃状态
   */
  const isActive = () => {
    return dropdownManager.isActive(dropdownId);
  };

  /**
   * 隐藏所有下拉面板
   */
  const hideAll = () => {
    dropdownManager.hideAll();
  };

  return {
    dropdownId,
    register,
    unregister,
    show,
    hide,
    toggle,
    isActive,
    hideAll,
    // 暴露全局活跃状态，用于响应式监听
    activeDropdownId: dropdownManager.activeDropdownId
  };
}

// 导出全局管理器实例（用于调试或特殊用途）
export { dropdownManager };
