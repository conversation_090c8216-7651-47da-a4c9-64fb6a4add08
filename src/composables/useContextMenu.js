import { ref, reactive, nextTick } from 'vue'

// 全局状态管理
const contextMenuState = reactive({
  visible: false,
  x: 0,
  y: 0,
  items: [],
  currentTarget: null,
  currentCallback: null
})

// 全局右键菜单管理器
class ContextMenuManager {
  constructor() {
    this.activeMenus = new Set()
    this.setupGlobalListeners()
  }

  // 设置全局监听器
  setupGlobalListeners() {
    // 监听全局点击事件，隐藏所有菜单
    document.addEventListener('click', (event) => {
      this.hideAll()
    })

    // 监听ESC键，隐藏所有菜单
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        this.hideAll()
      }
    })

    // 监听滚动事件，隐藏所有菜单
    document.addEventListener('scroll', () => {
      this.hideAll()
    }, true)

    // 监听窗口大小变化，隐藏所有菜单
    window.addEventListener('resize', () => {
      this.hideAll()
    })
  }

  // 显示右键菜单
  show(options) {
    const { x, y, items, target, callback } = options

    // 先隐藏所有现有菜单
    this.hideAll()

    // 设置新菜单状态
    contextMenuState.visible = true
    contextMenuState.x = x
    contextMenuState.y = y
    contextMenuState.items = items || []
    contextMenuState.currentTarget = target
    contextMenuState.currentCallback = callback || null

    // 记录当前活动菜单
    this.activeMenus.add(contextMenuState)

    return contextMenuState
  }

  // 隐藏所有菜单
  hideAll() {
    contextMenuState.visible = false
    contextMenuState.x = 0
    contextMenuState.y = 0
    contextMenuState.items = []
    contextMenuState.currentTarget = null
    contextMenuState.currentCallback = null
    this.activeMenus.clear()
  }

  // 隐藏特定菜单
  hide(menuState = contextMenuState) {
    if (menuState === contextMenuState) {
      this.hideAll()
    }
  }

  // 检查是否有活动菜单
  hasActiveMenu() {
    return contextMenuState.visible
  }

  // 获取当前菜单状态
  getCurrentState() {
    return contextMenuState
  }
}

// 创建全局管理器实例
const contextMenuManager = new ContextMenuManager()

// 主要的 composable 函数
export function useContextMenu() {
  // 显示右键菜单
  const showContextMenu = (event, items = [], callback = null) => {
    event.preventDefault()
    event.stopPropagation()

    const x = event.clientX
    const y = event.clientY
    const target = event.target

    return contextMenuManager.show({ x, y, items, target, callback })
  }

  // 隐藏右键菜单
  const hideContextMenu = () => {
    contextMenuManager.hideAll()
  }

  // 处理菜单项点击
  const handleMenuItemClick = (callback) => {
    return ({ item, index }) => {
      if (typeof callback === 'function') {
        callback(item, index, contextMenuState.currentTarget)
      }
      hideContextMenu()
    }
  }

  // 创建一个简单的菜单项点击处理器
  const createMenuItemHandler = (callback) => {
    return ({ item, index }) => {
      console.log('菜单项点击:', item.label, item.action)
      if (typeof callback === 'function') {
        callback(item, index, contextMenuState.currentTarget)
      }
      hideContextMenu()
    }
  }

  // 创建菜单项的辅助函数
  const createMenuItem = (label, options = {}) => {
    return {
      label,
      icon: options.icon || null,
      shortcut: options.shortcut || null,
      disabled: options.disabled || false,
      hidden: options.hidden || false,
      action: options.action || null,
      ...options
    }
  }

  // 创建分割线
  const createDivider = () => {
    return {
      divider: true
    }
  }

  return {
    // 状态
    contextMenuState,

    // 方法
    showContextMenu,
    hideContextMenu,
    handleMenuItemClick,
    createMenuItemHandler,

    // 辅助函数
    createMenuItem,
    createDivider,

    // 管理器实例（用于高级用法）
    contextMenuManager
  }
}

// 用于指令的简化版本
export function useContextMenuDirective() {
  const showMenu = (element, binding, event) => {
    event.preventDefault()
    event.stopPropagation()
    
    const { value } = binding
    let items = []
    let callback = null
    
    if (Array.isArray(value)) {
      items = value
    } else if (typeof value === 'object' && value !== null) {
      items = value.items || []
      callback = value.callback || null
    }
    
    const x = event.clientX
    const y = event.clientY

    contextMenuManager.show({ x, y, items, target: element, callback })
  }
  
  return {
    showMenu,
    hideMenu: () => contextMenuManager.hideAll()
  }
}

// 导出管理器实例供全局使用
export { contextMenuManager }
export default useContextMenu
