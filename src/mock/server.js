// 此文件用于模拟API服务，实际开发中可以用真实的后端服务代替

// 邮箱验证码登录流程模拟
export function setupMockServer() {
  // 模拟邮箱验证码存储
  const verificationCodes = {}

  // 模拟用户数据
  const users = {
    '<EMAIL>': {
      id: 20230791,
      parentUserId: 20230791,
      username: '<EMAIL>',
      parentUsername: '',
      nickName: '测试用户',
      userEmail: '<EMAIL>',
      userTel: '18800000000',
      userType: 0,
      authStatus: 1,
      needModifyPassword: null,
      lastLoginTime: new Date().toLocaleString(),
      onLineTokenNum: 0,
      newUserFlag: 0
    }
  }

  // 拦截请求
  if (window.XMLHttpRequest) {
    const originalXHR = window.XMLHttpRequest
    window.XMLHttpRequest = function() {
      const xhr = new originalXHR()
      const originalOpen = xhr.open
      const originalSend = xhr.send

      xhr.open = function(method, url) {
        // 记录原始URL用于后面的模拟响应
        this._url = url
        originalOpen.apply(this, arguments)
      }

      xhr.send = function(data) {
        // 拦截请求
        if (this._url.includes('/user/login/send/verification/code')) {
          // 发送验证码
          setTimeout(() => {
            const requestData = JSON.parse(data)
            const email = requestData.email
            
            // 生成6位随机验证码
            const code = Math.floor(100000 + Math.random() * 900000).toString()
            verificationCodes[email] = code
            
            console.log(`发送验证码到 ${email}: ${code}`)
            
            // 模拟成功响应
            const response = {
              code: 0,
              msg: '成功',
              data: null,
              extra: null
            }
            
            xhr.responseText = JSON.stringify(response)
            xhr.status = 200
            xhr.readyState = 4
            
            // 触发回调
            xhr.onreadystatechange()
          }, 500)
          
          return
        }
        
        if (this._url.includes('/user/login/email')) {
          // 验证码登录
          setTimeout(() => {
            const requestData = JSON.parse(data)
            const email = requestData.email
            const verifyCode = requestData.verifyCode
            
            // 验证验证码
            if (verificationCodes[email] === verifyCode || verifyCode === '123456') {
              // 验证成功
              const user = users[email] || {
                id: 20230791,
                parentUserId: 20230791,
                username: email,
                parentUsername: '',
                nickName: email.split('@')[0],
                userEmail: email,
                userTel: '18800000000',
                userType: 0,
                authStatus: 1,
                needModifyPassword: null,
                lastLoginTime: new Date().toLocaleString(),
                onLineTokenNum: 0,
                newUserFlag: 0
              }
              
              // 生成随机token
              const token = 'mock_token_' + Math.random().toString(36).substr(2)
              user.authorization = token
              
              const response = {
                code: 0,
                msg: '成功',
                data: user,
                extra: null
              }
              
              xhr.responseText = JSON.stringify(response)
              xhr.status = 200
              xhr.readyState = 4
              
              // 触发回调
              xhr.onreadystatechange()
            } else {
              // 验证失败
              const response = {
                code: 1,
                msg: '验证码错误',
                data: null,
                extra: null
              }
              
              xhr.responseText = JSON.stringify(response)
              xhr.status = 200
              xhr.readyState = 4
              
              // 触发回调
              xhr.onreadystatechange()
            }
          }, 800)
          
          return
        }
        
        // 不是模拟的请求，正常发送
        originalSend.apply(this, arguments)
      }
      
      return xhr
    }
  }
} 