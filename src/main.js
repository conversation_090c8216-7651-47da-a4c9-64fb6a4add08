import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import router from './router'
import App from './App.vue'
import './style.css'
import './assets/main.css'
import './assets/ant-design-overrides.css'
import './assets/dark-theme.css'
// import { setupMockServer } from './mock/server'
import ImageViewer from './components/parent/ImageViewer.vue'
import VueGridWaterfall from './components/VueGridWaterfall.vue'
import ContextMenu from './components/common/ContextMenu.vue'
import { contextMenuDirective } from './directives/contextMenu.js'

// 预取关键资源
const prefetchComponents = () => {
  // 预取可能会在初始渲染后较快用到的组件
  const componentsToPreload = [
    import('./components/parent/ImageViewer.vue')
  ];
  
  // 使用Promise.all并行预取组件
  Promise.all(componentsToPreload).catch(err => {
    console.warn('组件预取出错', err);
  });
  
  // 使用requestIdleCallback在浏览器空闲时预取其他组件
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      import('./components/create/AIImageEditor.vue');
    });
  }
};

// 启用模拟服务器 (开发环境)
// if (import.meta.env.DEV) {
//   setupMockServer()
// }

const app = createApp(App)

app.use(ElementPlus, { size: 'default', zIndex: 3000 })
app.use(router)
// 注册自定义瀑布流组件
app.component('VueGridWaterfall', VueGridWaterfall)

// 注册全局图片预览组件
app.component('ImageViewer', ImageViewer)

// 注册全局右键菜单组件
app.component('ContextMenu', ContextMenu)

// 注册全局自定义指令
app.directive('click-outside', {
  beforeMount(el, binding) {
    el.clickOutsideEvent = function(event) {
      if (!(el === event.target || el.contains(event.target))) {
        binding.value(event);
      }
    };
    document.addEventListener('click', el.clickOutsideEvent);
  },
  unmounted(el) {
    document.removeEventListener('click', el.clickOutsideEvent);
  }
});

// 注册全局右键菜单指令
app.directive('context-menu', contextMenuDirective);

// 开始渲染应用
const mountApp = () => {
  app.mount('#app');
  
  // 在应用挂载后预取组件
  prefetchComponents();
};

// 使用requestAnimationFrame确保渲染在下一帧进行，改进初始感知性能
if (window.requestAnimationFrame) {
  window.requestAnimationFrame(mountApp);
} else {
  mountApp();
}
