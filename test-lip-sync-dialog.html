<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对口型对话框测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #00d4ff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        
        .test-button:hover {
            background: #00b3e6;
        }
        
        .mock-shot-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .mock-shot-info h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .mock-shot-info p {
            margin: 5px 0;
            color: #666;
        }
        
        /* 模拟对话框样式 */
        .mock-dialog-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000;
            backdrop-filter: blur(4px);
        }
        
        .mock-dialog {
            background: #ffffff;
            border-radius: 16px;
            width: 90vw;
            max-width: 600px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            animation: slideUp 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        
        .dialog-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 24px;
            border-bottom: 1px solid #f0f0f0;
            background: #fafafa;
        }
        
        .dialog-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .close-button {
            background: none;
            border: none;
            padding: 8px;
            cursor: pointer;
            border-radius: 8px;
            color: #666;
            font-size: 20px;
        }
        
        .dialog-content {
            padding: 24px;
            max-height: 50vh;
            overflow-y: auto;
        }

        /* 左右布局容器 */
        .content-layout {
            display: flex;
            gap: 24px;
            align-items: flex-start;
        }

        /* 左侧区域 */
        .left-section {
            flex: 0 0 200px;
            min-width: 200px;
        }

        /* 右侧区域 */
        .right-section {
            flex: 1;
            min-width: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        .image-container {
            border: 2px dashed #e0e0e0;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            background: #fafafa;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .preview-image {
            max-width: 100%;
            max-height: 160px;
            border-radius: 8px;
            cursor: pointer;
            object-fit: contain;
        }

        .audio-list-section {
            margin-bottom: 24px;
        }

        .voice-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .voice-item {
            cursor: pointer;
            background-color: #f5f7fa;
            border-radius: 6px;
            padding: 8px;
            display: flex;
            align-items: center;
            gap: 0px;
            position: relative;
            transition: all 0.2s ease;
        }

        .voice-item:hover {
            background-color: #eef2f8;
        }

        .voice-play-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
            flex-shrink: 0;
            margin-right: 8px;
        }

        .voice-play {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            color: #606266;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            flex-shrink: 0;
            position: relative;
            cursor: pointer;
            background-color: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .voice-play:hover {
            transform: scale(1.05);
            color: #409eff;
            background-color: rgba(64, 158, 255, 0.1);
            border-color: rgba(64, 158, 255, 0.3);
        }

        .voice-info {
            flex: 1;
            min-width: 0;
            padding-top: 2px;
        }

        .voice-header {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
        }

        .voice-name {
            flex: 1;
            text-align: left;
            font-weight: 500;
            font-size: 14px;
            color: #303133;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            min-width: 0;
        }

        .voice-narration {
            font-size: 12px;
            color: #606266;
            text-align: left;
        }

        .voice-actions {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 4px;
            flex-shrink: 0;
        }

        .voice-duration {
            font-size: 11px;
            color: #909399;
            text-align: center;
            font-weight: 500;
            min-width: 32px;
            margin-top: auto;
            align-self: flex-end;
        }
        
        .description-text {
            display: flex;
            gap: 12px;
            padding: 16px;
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 8px;
            margin-bottom: 8px;
        }
        
        .dialog-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding: 20px 24px;
            border-top: 1px solid #f0f0f0;
            background: #fafafa;
        }
        
        .cancel-button {
            padding: 10px 20px;
            border: 1px solid #d0d0d0;
            background: #ffffff;
            color: #666;
            border-radius: 8px;
            cursor: pointer;
        }
        
        .generate-button {
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
            color: #ffffff;
            border: none;
            padding: 14px 24px;
            border-radius: 25px;
            font-size: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            min-height: 48px;
            box-shadow: 0 4px 12px rgba(0, 212, 255, 0.25);
            position: relative;
            overflow: hidden;
        }

        .generate-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .generate-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
            background: linear-gradient(135deg, #00e6ff 0%, #00b3e6 100%);
        }

        .generate-button:hover:not(:disabled)::before {
            left: 100%;
        }

        .button-text {
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .credits-info {
            font-size: 12px;
            opacity: 0.9;
            margin-left: 6px;
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 500;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>对口型确认对话框测试</h1>
        
        <div class="mock-shot-info">
            <h3>模拟分镜数据</h3>
            <p><strong>图片URL:</strong> https://via.placeholder.com/400x300/00d4ff/ffffff?text=Sample+Image</p>
            <p><strong>音频数量:</strong> 2个</p>
            <p><strong>音频1:</strong> "这是第一段测试音频内容" (3.5秒)</p>
            <p><strong>音频2:</strong> "这是第二段测试音频内容，稍微长一些" (5.2秒)</p>
        </div>
        
        <button class="test-button" onclick="showDialog()">显示对口型确认对话框</button>
        <button class="test-button" onclick="showEmptyDialog()">显示空音频对话框</button>
    </div>
    
    <!-- 模拟对话框 -->
    <div id="mockDialog" class="mock-dialog-overlay hidden" onclick="hideDialog()">
        <div class="mock-dialog" onclick="event.stopPropagation()">
            <div class="dialog-header">
                <h3 class="dialog-title">对口型确认</h3>
                <button class="close-button" onclick="hideDialog()">×</button>
            </div>
            
            <div class="dialog-content">
                <!-- 左右布局容器 -->
                <div class="content-layout">
                    <!-- 左侧：图片预览 -->
                    <div class="left-section">
                        <div class="section-title">当前图片</div>
                        <div class="image-container">
                            <img
                                id="previewImage"
                                src="https://via.placeholder.com/400x300/00d4ff/ffffff?text=Sample+Image"
                                alt="当前图片"
                                class="preview-image"
                                onclick="alert('点击预览图片')"
                            />
                        </div>
                    </div>

                    <!-- 右侧：音频列表和说明 -->
                    <div class="right-section">
                        <!-- 音频列表 -->
                        <div class="audio-list-section">
                            <div class="section-title">
                                音频列表
                                <span class="audio-count" id="audioCount">(2)</span>
                            </div>
                            <div class="voice-list" id="audioList">
                                <div class="voice-item">
                                    <div class="voice-play-section">
                                        <div class="voice-play" onclick="togglePlay(1)">
                                            ▶
                                        </div>
                                    </div>
                                    <div class="voice-info">
                                        <div class="voice-header">
                                            <div class="voice-name">音频-1</div>
                                        </div>
                                        <div class="voice-narration">这是第一段测试音频内容</div>
                                    </div>
                                    <div class="voice-actions">
                                        <div class="voice-duration">3.5s</div>
                                    </div>
                                </div>

                                <div class="voice-item">
                                    <div class="voice-play-section">
                                        <div class="voice-play" onclick="togglePlay(2)">
                                            ▶
                                        </div>
                                    </div>
                                    <div class="voice-info">
                                        <div class="voice-header">
                                            <div class="voice-name">音频-2</div>
                                        </div>
                                        <div class="voice-narration">这是第二段测试音频内容，稍微长一些</div>
                                    </div>
                                    <div class="voice-actions">
                                        <div class="voice-duration">5.2s</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 说明文字 -->
                        <div class="description-section">
                            <div class="description-text">
                                <span style="color: #fa8c16; font-size: 18px;">ℹ</span>
                                <div>
                                    <p style="margin: 0 0 8px 0;">对口型后会生成为新的视频，将音频合并到视频中，并且会删除旁白音频。</p>
                                    <p style="margin: 0; color: #fa8c16; font-weight: 500;">此操作不可撤销，请确认后继续。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="dialog-footer">
                <button class="cancel-button" onclick="hideDialog()">
                    取消
                </button>
                <button class="generate-button" onclick="confirmLipSync()">
                    <span class="button-text">确认对口型</span>
                    <span class="credits-info">⚡ 10</span>
                </button>
            </div>
        </div>
    </div>
    
    <script>
        function showDialog() {
            // 显示有音频的对话框
            document.getElementById('audioList').innerHTML = `
                <div class="voice-item">
                    <div class="voice-play-section">
                        <div class="voice-play" onclick="togglePlay(1)">▶</div>
                    </div>
                    <div class="voice-info">
                        <div class="voice-header">
                            <div class="voice-name">音频-1</div>
                        </div>
                        <div class="voice-narration">这是第一段测试音频内容</div>
                    </div>
                    <div class="voice-actions">
                        <div class="voice-duration">3.5s</div>
                    </div>
                </div>
                <div class="voice-item">
                    <div class="voice-play-section">
                        <div class="voice-play" onclick="togglePlay(2)">▶</div>
                    </div>
                    <div class="voice-info">
                        <div class="voice-header">
                            <div class="voice-name">音频-2</div>
                        </div>
                        <div class="voice-narration">这是第二段测试音频内容，稍微长一些</div>
                    </div>
                    <div class="voice-actions">
                        <div class="voice-duration">5.2s</div>
                    </div>
                </div>
            `;
            document.getElementById('audioCount').textContent = '(2)';
            document.getElementById('mockDialog').classList.remove('hidden');
        }

        function showEmptyDialog() {
            // 显示无音频的对话框
            document.getElementById('audioList').innerHTML = `
                <div class="empty-voice-list">
                    <span style="font-size: 32px;">🎤</span>
                    <div class="empty-text">暂无音频</div>
                </div>
            `;
            document.getElementById('audioCount').textContent = '';
            document.getElementById('mockDialog').classList.remove('hidden');
        }
        
        function hideDialog() {
            document.getElementById('mockDialog').classList.add('hidden');
        }
        
        function togglePlay(audioId) {
            alert('播放音频 ' + audioId);
        }
        
        function confirmLipSync() {
            alert('确认对口型处理！扣除10积分');
            hideDialog();
        }
    </script>
</body>
</html>
