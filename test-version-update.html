<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>版本更新功能测试</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .test-title {
            font-size: 24px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 16px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 8px;
        }
        
        .test-section {
            margin-bottom: 24px;
        }
        
        .test-section h3 {
            font-size: 18px;
            color: #333;
            margin-bottom: 12px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            margin-right: 12px;
            margin-bottom: 8px;
            transition: all 0.2s ease;
        }
        
        .test-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .test-button.secondary {
            background: #f1f3f4;
            color: #5f6368;
        }
        
        .test-button.secondary:hover {
            background: #e8eaed;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .version-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin: 12px 0;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 13px;
        }
        
        .status {
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            margin: 8px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .instructions {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 16px;
            margin: 16px 0;
        }
        
        .instructions h4 {
            margin-top: 0;
            color: #1976d2;
        }
        
        .instructions ol {
            margin-bottom: 0;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">版本更新功能测试页面</h1>
        
        <div class="instructions">
            <h4>测试说明</h4>
            <ol>
                <li>点击"模拟新版本"按钮来模拟服务器有新版本</li>
                <li>点击"触发版本检查"按钮来手动检查版本</li>
                <li>观察是否弹出版本更新对话框</li>
                <li>测试对话框的各种操作（立即更新、稍后提醒、关闭）</li>
                <li>可以修改 public/version.json 文件来测试不同的版本信息</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>当前版本信息</h3>
            <div id="currentVersionInfo" class="version-info">
                正在加载...
            </div>
        </div>
        
        <div class="test-section">
            <h3>测试操作</h3>
            <button class="test-button" onclick="simulateNewVersion()">模拟新版本</button>
            <button class="test-button secondary" onclick="triggerVersionCheck()">触发版本检查</button>
            <button class="test-button secondary" onclick="resetVersion()">重置版本</button>
            <button class="test-button secondary" onclick="showCurrentVersion()">显示当前版本</button>
        </div>
        
        <div class="test-section">
            <h3>测试状态</h3>
            <div id="testStatus" class="status">
                等待测试操作...
            </div>
        </div>
        
        <div class="test-section">
            <h3>版本检测服务状态</h3>
            <div id="serviceStatus" class="version-info">
                正在检查服务状态...
            </div>
        </div>
    </div>

    <script>
        // 模拟版本服务（简化版本，用于测试）
        class TestVersionService {
            constructor() {
                this.currentVersion = null;
                this.callbacks = new Set();
                this.init();
            }
            
            async init() {
                await this.getCurrentVersion();
                this.updateServiceStatus();
            }
            
            async getCurrentVersion() {
                try {
                    const response = await fetch('/version.json', { cache: 'no-cache' });
                    if (response.ok) {
                        this.currentVersion = await response.json();
                    }
                } catch (error) {
                    console.error('获取版本信息失败:', error);
                    this.currentVersion = {
                        version: '1.0.0',
                        buildTime: Date.now(),
                        hash: 'test-hash'
                    };
                }
                return this.currentVersion;
            }
            
            async checkVersion() {
                try {
                    const response = await fetch('/version.json', { cache: 'no-cache' });
                    if (response.ok) {
                        const latestVersion = await response.json();
                        
                        if (this.hasNewVersion(this.currentVersion, latestVersion)) {
                            this.notifyCallbacks({
                                type: 'newVersion',
                                currentVersion: this.currentVersion,
                                latestVersion: latestVersion
                            });
                            return true;
                        }
                    }
                } catch (error) {
                    console.error('版本检查失败:', error);
                }
                return false;
            }
            
            hasNewVersion(current, latest) {
                if (!current || !latest) return false;
                
                if (latest.buildTime && current.buildTime) {
                    return latest.buildTime > current.buildTime;
                }
                
                if (latest.hash && current.hash) {
                    return latest.hash !== current.hash;
                }
                
                return false;
            }
            
            onVersionUpdate(callback) {
                this.callbacks.add(callback);
                return () => this.callbacks.delete(callback);
            }
            
            notifyCallbacks(data) {
                this.callbacks.forEach(callback => {
                    try {
                        callback(data);
                    } catch (error) {
                        console.error('回调执行失败:', error);
                    }
                });
            }
            
            updateServiceStatus() {
                const statusEl = document.getElementById('serviceStatus');
                if (statusEl) {
                    statusEl.innerHTML = `
                        服务状态: 运行中<br>
                        当前版本: ${this.currentVersion?.version || 'Unknown'}<br>
                        构建时间: ${this.currentVersion?.buildTime ? new Date(this.currentVersion.buildTime).toLocaleString() : 'Unknown'}<br>
                        哈希值: ${this.currentVersion?.hash || 'Unknown'}
                    `;
                }
            }
        }
        
        // 创建测试服务实例
        const testVersionService = new TestVersionService();
        
        // 监听版本更新
        testVersionService.onVersionUpdate((data) => {
            updateTestStatus('检测到新版本！', 'warning');
            console.log('版本更新数据:', data);
            
            // 这里可以显示实际的更新对话框
            if (confirm('检测到新版本，是否刷新页面？')) {
                window.location.reload();
            }
        });
        
        // 测试函数
        async function simulateNewVersion() {
            updateTestStatus('正在模拟新版本...', 'warning');
            
            // 创建一个新的版本信息
            const newVersion = {
                version: '1.0.1',
                buildTime: Date.now(),
                hash: 'new-version-hash-' + Math.random().toString(36).substring(7),
                gitCommit: 'simulated-commit',
                buildEnv: 'test'
            };
            
            // 模拟更新服务器上的版本文件
            try {
                // 在实际应用中，这里应该是更新服务器上的version.json文件
                // 这里我们只是模拟触发版本检查
                console.log('模拟的新版本:', newVersion);
                
                // 手动触发版本更新回调
                testVersionService.notifyCallbacks({
                    type: 'newVersion',
                    currentVersion: testVersionService.currentVersion,
                    latestVersion: newVersion
                });
                
                updateTestStatus('新版本模拟成功！应该会弹出更新提醒。', 'success');
            } catch (error) {
                updateTestStatus('模拟新版本失败: ' + error.message, 'error');
            }
        }
        
        async function triggerVersionCheck() {
            updateTestStatus('正在检查版本...', 'warning');
            
            try {
                const hasUpdate = await testVersionService.checkVersion();
                if (hasUpdate) {
                    updateTestStatus('发现新版本！', 'warning');
                } else {
                    updateTestStatus('当前已是最新版本', 'success');
                }
            } catch (error) {
                updateTestStatus('版本检查失败: ' + error.message, 'error');
            }
        }
        
        async function resetVersion() {
            updateTestStatus('正在重置版本信息...', 'warning');
            
            try {
                await testVersionService.getCurrentVersion();
                testVersionService.updateServiceStatus();
                showCurrentVersion();
                updateTestStatus('版本信息已重置', 'success');
            } catch (error) {
                updateTestStatus('重置失败: ' + error.message, 'error');
            }
        }
        
        function showCurrentVersion() {
            const versionEl = document.getElementById('currentVersionInfo');
            if (versionEl && testVersionService.currentVersion) {
                versionEl.innerHTML = JSON.stringify(testVersionService.currentVersion, null, 2);
            }
        }
        
        function updateTestStatus(message, type = 'success') {
            const statusEl = document.getElementById('testStatus');
            if (statusEl) {
                statusEl.textContent = message;
                statusEl.className = `status ${type}`;
            }
        }
        
        // 页面加载完成后显示当前版本
        window.addEventListener('load', () => {
            setTimeout(() => {
                showCurrentVersion();
                updateTestStatus('测试页面已加载，可以开始测试', 'success');
            }, 500);
        });
    </script>
</body>
</html>
