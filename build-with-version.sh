#!/bin/bash

# 带版本号的构建脚本
echo "=== 带版本号的构建脚本 ==="

# 定义颜色
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 检查是否提供了版本号参数
if [ -n "$1" ]; then
    VERSION="$1"
    echo -e "${GREEN}使用提供的版本号: ${VERSION}${NC}"
else
    # 生成基于时间戳的版本号
    VERSION="1.0.$(date +%Y%m%d%H%M%S)"
    echo -e "${YELLOW}自动生成版本号: ${VERSION}${NC}"
fi

# 设置环境变量
export npm_package_version="${VERSION}"
export APP_VERSION="${VERSION}"
export VITE_APP_VERSION="${VERSION}"
export NODE_ENV="${2:-development}"

echo ""
echo -e "${YELLOW}构建配置:${NC}"
echo "  版本号: ${VERSION}"
echo "  环境: ${NODE_ENV}"
echo "  npm_package_version: ${npm_package_version}"
echo "  APP_VERSION: ${APP_VERSION}"
echo "  VITE_APP_VERSION: ${VITE_APP_VERSION}"

echo ""
echo -e "${GREEN}开始构建...${NC}"

# 根据环境选择构建命令
if [ "$NODE_ENV" = "production" ]; then
    BUILD_CMD="build:prod"
else
    BUILD_CMD="build"
fi

echo -e "${YELLOW}运行构建命令: npm run ${BUILD_CMD}${NC}"

# 执行构建，显式传递环境变量
npm_package_version="${VERSION}" \
APP_VERSION="${VERSION}" \
VITE_APP_VERSION="${VERSION}" \
NODE_ENV="${NODE_ENV}" \
npm run ${BUILD_CMD}

# 检查构建结果
if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}✓ 构建成功！${NC}"
    
    # 检查生成的版本文件
    if [ -f "dist/version.json" ]; then
        echo ""
        echo -e "${GREEN}✓ 版本文件已生成:${NC}"
        cat dist/version.json | head -10
    else
        echo -e "${RED}✗ 版本文件未生成${NC}"
    fi
else
    echo ""
    echo -e "${RED}✗ 构建失败${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}构建完成！${NC}"
echo ""
echo -e "${YELLOW}后续操作:${NC}"
echo "1. 启动开发服务器: npm run dev"
echo "2. 检查版本文件: cat dist/version.json"
echo "3. 测试版本检测: 访问应用并检查控制台"
