#!/bin/bash

# 调试版本的部署脚本
echo "=== 调试版本部署脚本 ==="

# 显示执行的命令
set -x

# 定义颜色
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 生成版本号（基于时间戳）
VERSION="1.0.$(date +%Y%m%d%H%M%S)"
echo -e "${YELLOW}当前部署版本: ${VERSION}${NC}"

# 设置多种环境变量，确保构建时能正确获取
export npm_package_version="${VERSION}"
export APP_VERSION="${VERSION}"
export VITE_APP_VERSION="${VERSION}"
export NODE_ENV="development"

# 显示设置的环境变量
echo -e "${YELLOW}环境变量设置:${NC}"
echo "  npm_package_version: ${npm_package_version}"
echo "  APP_VERSION: ${APP_VERSION}"
echo "  VITE_APP_VERSION: ${VITE_APP_VERSION}"
echo "  NODE_ENV: ${NODE_ENV}"

echo ""
echo -e "${YELLOW}=== 调试步骤1: 运行环境变量调试 ===${NC}"
node debug-env.js

echo ""
echo -e "${YELLOW}=== 调试步骤2: 运行构建前检查 ===${NC}"
echo "当前shell环境变量:"
env | grep -E "(npm_package_version|APP_VERSION|VITE_APP_VERSION|NODE_ENV)" || echo "未找到相关环境变量"

echo ""
echo -e "${GREEN}开始构建项目...${NC}"

# 使用最直接的方式传递环境变量
echo -e "${YELLOW}执行构建命令...${NC}"
echo "命令: npm_package_version=\"${VERSION}\" APP_VERSION=\"${VERSION}\" VITE_APP_VERSION=\"${VERSION}\" NODE_ENV=\"development\" npm run build"

npm_package_version="${VERSION}" APP_VERSION="${VERSION}" VITE_APP_VERSION="${VERSION}" NODE_ENV="development" npm run build

# 检查构建是否成功
if [ $? -ne 0 ]; then
    echo -e "${RED}构建失败，退出部署脚本${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}构建完成！${NC}"

# 检查生成的版本文件
if [ -f "dist/version.json" ]; then
    echo -e "${GREEN}✓ 版本文件已生成${NC}"
    echo "版本文件内容:"
    cat dist/version.json
    
    # 提取并比较版本号
    BUILT_VERSION=$(cat dist/version.json | grep -o '"version": "[^"]*"' | cut -d'"' -f4)
    echo ""
    echo -e "${YELLOW}版本号比较:${NC}"
    echo "  期望版本: ${VERSION}"
    echo "  实际版本: ${BUILT_VERSION}"
    
    if [ "${BUILT_VERSION}" = "${VERSION}" ]; then
        echo -e "${GREEN}✓ 版本号正确！${NC}"
    else
        echo -e "${RED}✗ 版本号不匹配${NC}"
    fi
else
    echo -e "${RED}✗ 版本文件未生成${NC}"
fi

echo ""
echo -e "${GREEN}调试部署完成！${NC}"
