<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ElMessage 样式测试</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', system-ui, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .test-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(16px);
            text-align: center;
            max-width: 600px;
        }
        
        h1 {
            color: #1f2937;
            margin-bottom: 20px;
            font-size: 28px;
            font-weight: 700;
        }
        
        p {
            color: #6b7280;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 30px;
        }
        
        button {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: inherit;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }
        
        .btn-error {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }
        
        .btn-info {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
        }
        
        .btn-demo {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        
        .note {
            background: rgba(99, 102, 241, 0.1);
            border-radius: 8px;
            padding: 16px;
            border-left: 3px solid #6366f1;
            text-align: left;
            margin-top: 20px;
        }
        
        .note h3 {
            color: #6366f1;
            margin: 0 0 8px 0;
            font-size: 16px;
        }
        
        .note p {
            margin: 0;
            font-size: 14px;
            color: #4b5563;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎨 ElMessage 样式测试</h1>
        <p>点击下方按钮测试新的 ElMessage 样式效果。新消息会在固定位置显示，自动替换旧消息，不会累积排版。</p>
        
        <div class="button-grid">
            <button class="btn-success" onclick="showMessage('success')">✅ 成功消息</button>
            <button class="btn-error" onclick="showMessage('error')">❌ 错误消息</button>
            <button class="btn-warning" onclick="showMessage('warning')">⚠️ 警告消息</button>
            <button class="btn-info" onclick="showMessage('info')">ℹ️ 信息消息</button>
            <button class="btn-demo" onclick="showSequential()">🎬 连续演示</button>
        </div>
        
        <div class="note">
            <h3>🎯 样式特点</h3>
            <p>• 柔和的背景色彩，提升视觉舒适度<br>
               • 从下往上的平滑弹出动画<br>
               • 优雅的毛玻璃效果和微妙阴影<br>
               • 与项目整体风格保持一致<br>
               • <strong>✅ 完美居中显示</strong><br>
               • <strong>🔄 新消息自动替换旧消息</strong></p>
        </div>
    </div>

    <script>
        // 当前消息实例
        let currentMessage = null;

        // 模拟 ElMessage 功能
        function showMessage(type) {
            const messages = {
                success: '操作成功！数据已保存',
                error: '操作失败，请检查网络连接后重试',
                warning: '请注意：此操作不可撤销',
                info: '系统将在5分钟后进行维护'
            };

            createMessage(type, messages[type]);
        }
        
        function showSequential() {
            const sequence = [
                { type: 'info', message: '开始演示新消息替换效果...' },
                { type: 'success', message: '第一条消息：操作成功', delay: 1000 },
                { type: 'warning', message: '第二条消息：请注意检查', delay: 2000 },
                { type: 'error', message: '第三条消息：发现错误', delay: 3000 },
                { type: 'info', message: '演示完成！每条新消息都会替换前一条', delay: 4000 }
            ];

            sequence.forEach(item => {
                setTimeout(() => {
                    createMessage(item.type, item.message);
                }, item.delay || 0);
            });
        }
        
        function createMessage(type, text) {
            // 如果有当前消息，先移除它
            if (currentMessage && currentMessage.parentNode) {
                currentMessage.style.transition = 'all 0.3s ease-out';
                currentMessage.style.opacity = '0';
                currentMessage.style.transform = 'translateX(-50%) translateY(-10px) scale(0.98)';
                setTimeout(() => {
                    if (currentMessage && currentMessage.parentNode) {
                        currentMessage.parentNode.removeChild(currentMessage);
                    }
                }, 300);
            }

            const message = document.createElement('div');
            message.className = `el-message el-message--${type}`;
            message.innerHTML = `
                <div class="el-message__icon">${getIcon(type)}</div>
                <div class="el-message__content">${text}</div>
            `;

            // 更新当前消息引用
            currentMessage = message;
            
            // 添加样式
            Object.assign(message.style, {
                position: 'fixed',
                top: '20px',
                left: '50%',
                transform: 'translateX(-50%)',
                zIndex: '3001',
                minWidth: '320px',
                maxWidth: '500px',
                borderRadius: '8px',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1), 0 3px 10px rgba(0, 0, 0, 0.06)',
                backdropFilter: 'blur(12px)',
                fontSize: '14px',
                fontWeight: '400',
                padding: '14px 16px',
                display: 'flex',
                alignItems: 'center',
                fontFamily: 'PingFang SC, Microsoft YaHei, system-ui, sans-serif'
            });
            
            // 设置类型特定样式
            const typeStyles = {
                success: {
                    background: 'rgba(240, 253, 244, 0.95)',
                    color: '#065f46',
                    borderLeft: '3px solid #10b981'
                },
                error: {
                    background: 'rgba(254, 242, 242, 0.95)',
                    color: '#7f1d1d',
                    borderLeft: '3px solid #ef4444'
                },
                warning: {
                    background: 'rgba(255, 251, 235, 0.95)',
                    color: '#92400e',
                    borderLeft: '3px solid #f59e0b'
                },
                info: {
                    background: 'rgba(239, 246, 255, 0.95)',
                    color: '#1e3a8a',
                    borderLeft: '3px solid #3b82f6'
                }
            };
            
            Object.assign(message.style, typeStyles[type]);
            
            // 添加到页面
            document.body.appendChild(message);
            
            // 动画效果
            message.style.opacity = '0';
            message.style.transform = 'translateX(-50%) translateY(40px) scale(0.95)';
            message.style.filter = 'blur(2px)';
            
            // 触发动画
            requestAnimationFrame(() => {
                message.style.transition = 'all 0.5s cubic-bezier(0.16, 1, 0.3, 1)';
                message.style.opacity = '1';
                message.style.transform = 'translateX(-50%) translateY(0) scale(1)';
                message.style.filter = 'blur(0)';
            });
            
            // 自动移除
            setTimeout(() => {
                if (currentMessage === message) {
                    message.style.transition = 'all 0.4s cubic-bezier(0.7, 0, 0.84, 0)';
                    message.style.opacity = '0';
                    message.style.transform = 'translateX(-50%) translateY(30px) scale(0.95)';
                    message.style.filter = 'blur(1px)';

                    setTimeout(() => {
                        if (message.parentNode && currentMessage === message) {
                            message.parentNode.removeChild(message);
                            currentMessage = null;
                        }
                    }, 400);
                }
            }, 3000);
        }
        
        function getIcon(type) {
            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };
            return icons[type] || 'ℹ️';
        }
    </script>
</body>
</html>
