# 版本更新自动提醒功能

## 功能概述

本功能实现了当应用有新版本发布时，自动检测并提醒用户刷新页面获取最新版本的能力。主要包括：

- 🔄 自动版本检测
- 📱 友好的更新提醒弹框
- ⏰ 智能检测时机（定时检查 + 页面可见性检查）
- 🔄 多标签页同步
- 🎨 美观的UI设计，支持深色主题

## 文件结构

```
src/
├── services/
│   └── versionService.js          # 版本检测服务
├── components/
│   └── VersionUpdateDialog.vue    # 版本更新弹框组件
└── App.vue                        # 主应用（已集成版本检测）

public/
└── version.json                   # 版本信息文件（开发环境）

vite.config.js                     # 构建配置（已添加版本生成插件）
test-version-update.html           # 功能测试页面
```

## 核心功能

### 1. 版本检测服务 (`versionService.js`)

**主要特性：**
- 定时检查版本更新（默认5分钟）
- 页面可见性检查（页面重新激活时检查）
- 多标签页同步（通过localStorage）
- 智能版本比较（支持构建时间、哈希值、版本号）
- 错误处理和重试机制

**配置选项：**
```javascript
{
  checkIntervalMs: 5 * 60 * 1000,    // 检查间隔（毫秒）
  versionEndpoint: '/version.json',   // 版本信息接口
  enableVisibilityCheck: true,       // 页面可见性检查
  enableStorageCheck: true,          // 本地存储检查
  maxRetries: 3,                     // 最大重试次数
  retryDelay: 5000                   // 重试延迟
}
```

### 2. 版本更新弹框 (`VersionUpdateDialog.vue`)

**主要特性：**
- 美观的UI设计
- 支持深色主题
- 可配置的按钮和行为
- 键盘快捷键支持（ESC关闭，Enter确认）
- 移动端适配
- 防误操作设计

**Props配置：**
```javascript
{
  visible: Boolean,           // 是否显示
  currentVersion: Object,     // 当前版本信息
  latestVersion: Object,      // 最新版本信息
  message: String,           // 自定义消息
  showVersionInfo: Boolean,   // 是否显示版本信息
  showLaterButton: Boolean,   // 是否显示"稍后提醒"按钮
  showCloseButton: Boolean,   // 是否显示关闭按钮
  autoRefreshDelay: Number,   // 自动刷新延迟（0=不自动）
  preventClose: Boolean       // 是否阻止关闭
}
```

### 3. 构建时版本生成

构建时自动生成 `version.json` 文件，包含：
```json
{
  "version": "1.0.0",
  "buildTime": 1640995200000,
  "hash": "abc123def456",
  "gitCommit": "commit-sha",
  "buildEnv": "production"
}
```

## 使用方法

### 1. 基本集成

版本检测功能已经集成到 `App.vue` 中，无需额外配置即可使用。

### 2. 自定义配置

如需自定义配置，可以修改 `versionService.js` 中的 `options` 对象：

```javascript
// 修改检查间隔为10分钟
versionService.options.checkIntervalMs = 10 * 60 * 1000;

// 禁用页面可见性检查
versionService.options.enableVisibilityCheck = false;
```

### 3. 手动触发检查

```javascript
import versionService from '@/services/versionService.js';

// 手动检查版本
await versionService.checkVersion();

// 监听版本更新
const unsubscribe = versionService.onVersionUpdate((data) => {
  console.log('检测到新版本:', data);
});

// 取消监听
unsubscribe();
```

### 4. 自定义弹框

```vue
<template>
  <VersionUpdateDialog
    :visible="showDialog"
    :current-version="currentVersion"
    :latest-version="latestVersion"
    :show-later-button="true"
    :prevent-close="false"
    message="发现新版本，建议立即更新以获得最佳体验！"
    @refresh="handleRefresh"
    @later="handleLater"
    @close="handleClose"
  />
</template>
```

## 测试功能

### 1. 使用测试页面

打开 `test-version-update.html` 进行功能测试：

```bash
# 启动开发服务器
npm run dev

# 在浏览器中访问
http://localhost:3000/test-version-update.html
```

### 2. 手动测试步骤

1. **模拟新版本：** 点击"模拟新版本"按钮
2. **触发检查：** 点击"触发版本检查"按钮
3. **修改版本文件：** 编辑 `public/version.json` 文件
4. **测试多标签页：** 在多个标签页中打开应用

### 3. 版本文件测试

修改 `public/version.json` 来测试不同场景：

```json
{
  "version": "1.0.1",
  "buildTime": 1640995300000,  // 更新构建时间
  "hash": "new-hash-value",    // 更新哈希值
  "gitCommit": "new-commit",
  "buildEnv": "production"
}
```

## 部署注意事项

### 1. 构建配置

确保 `vite.config.js` 中的版本生成插件已启用：

```javascript
// 在构建时生成版本信息
isBuildMode && generateVersionPlugin()
```

### 2. 服务器配置

确保 `version.json` 文件：
- 可以被客户端访问
- 设置了正确的缓存策略（建议不缓存或短时间缓存）
- 在每次部署时都会更新

### 3. CDN配置

如果使用CDN，确保：
- `version.json` 文件的缓存时间较短
- 或者在部署时主动刷新CDN缓存

## 最佳实践

### 1. 检查频率

- 生产环境建议5-10分钟检查一次
- 开发环境可以设置更短的间隔
- 避免过于频繁的检查影响性能

### 2. 用户体验

- 提供"稍后提醒"选项，避免强制更新
- 在用户空闲时进行检查
- 提供清晰的更新说明

### 3. 错误处理

- 网络错误时优雅降级
- 避免频繁的错误提示
- 记录错误日志便于调试

### 4. 性能优化

- 使用页面可见性API减少不必要的检查
- 利用localStorage实现多标签页同步
- 避免在页面加载时立即检查

## 故障排除

### 1. 版本检测不工作

- 检查 `version.json` 文件是否存在且可访问
- 检查网络连接和CORS设置
- 查看浏览器控制台错误信息

### 2. 弹框不显示

- 检查版本比较逻辑
- 确认事件监听器已正确设置
- 检查组件的visible属性

### 3. 多标签页不同步

- 检查localStorage权限
- 确认storage事件监听器已设置
- 检查浏览器的localStorage支持

## 扩展功能

### 1. 版本更新日志

可以扩展版本信息包含更新日志：

```json
{
  "version": "1.0.1",
  "buildTime": 1640995200000,
  "hash": "abc123",
  "changelog": [
    "修复了登录问题",
    "优化了页面加载速度",
    "新增了暗色主题"
  ]
}
```

### 2. 强制更新

对于重要的安全更新，可以设置强制更新：

```json
{
  "version": "1.0.1",
  "buildTime": 1640995200000,
  "hash": "abc123",
  "forceUpdate": true
}
```

### 3. 分环境配置

可以为不同环境设置不同的检查策略：

```javascript
const config = {
  development: { checkIntervalMs: 30000 },
  production: { checkIntervalMs: 300000 }
};
```

## 技术支持

如有问题或建议，请：
1. 查看浏览器控制台错误信息
2. 检查网络请求是否正常
3. 参考测试页面进行调试
4. 联系开发团队获取支持
