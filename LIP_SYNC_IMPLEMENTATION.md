# 对口型功能实现文档

## 功能概述

实现了点击"对口型"按钮后弹出确认对话框的功能，对话框采用左右布局，左侧显示当前图片，右侧显示音频列表和说明信息，使用GenerateButton组件作为确认按钮，扣除10积分进行对口型处理。

## 实现的文件

### 1. 对口型确认对话框组件
**文件路径**: `src/components/videoEdit/LipSyncConfirmDialog.vue`

**主要功能**:
- 左右布局设计，左侧显示图片预览，右侧显示音频和说明
- 展示音频列表，采用AudioGenerationPanel的样式风格
- 提供音频播放功能，包括波形动画效果
- 使用GenerateButton组件作为确认按钮
- 显示操作说明和警告信息

**组件特性**:
- 响应式设计，支持移动端和桌面端
- 支持暗色主题
- 音频播放控制，包括播放状态指示和波形动画
- 图片点击预览
- 优雅的动画效果
- 统一的UI风格，与现有组件保持一致

### 2. 媒体编辑菜单更新
**文件路径**: `src/components/videoEdit/MediaEditorMenu.vue`

**更新内容**:
- 将"视频配音"按钮文本改为"对口型"
- 添加对口型对话框状态管理
- 实现对话框的打开和关闭逻辑
- 集成对口型API调用

### 3. API接口添加
**文件路径**: `src/api/auth.js`

**新增接口**:
```javascript
/**
 * 对口型处理
 * @param {Object} params - 对口型处理参数
 * @param {Number} params.shotId - 分镜ID
 * @param {String} params.imageUrl - 图片URL
 * @param {Array} params.audioIds - 音频ID数组
 * @returns {Promise<Object>} - 包含处理结果的Promise对象
 */
export function processLipSync(params) {
  return apiClient.post(`/agent/canvas/shot/lip-sync`, params)
}
```

## 功能流程

1. **触发对口型**: 用户点击"对口型"按钮
2. **显示确认对话框**: 弹出对话框显示当前图片和音频列表
3. **用户确认**: 用户查看信息后点击"确认 (扣除10积分)"按钮
4. **API调用**: 调用后端对口型处理接口
5. **处理反馈**: 显示处理结果并刷新界面

## 对话框内容

### 当前图片区域
- 显示分镜的图片预览
- 支持点击放大查看
- 无图片时显示占位符

### 音频列表区域
- 显示所有音频的详细信息
- 音频名称（音频-1, 音频-2...）
- 音频时长（格式化显示）
- 音频文本内容
- 播放/暂停按钮
- 音效标识（如果是音效类型）

### 说明区域
- 操作说明：对口型后会生成新视频，合并音频，删除旁白音频
- 警告信息：此操作不可撤销
- 积分扣除提示：确认按钮显示扣除10积分

## 样式特性

- **现代化设计**: 圆角、阴影、渐变等现代UI元素
- **响应式布局**: 适配不同屏幕尺寸
- **暗色主题支持**: 完整的暗色模式适配
- **动画效果**: 弹出动画、悬停效果等
- **无障碍设计**: 合理的颜色对比度和交互反馈

## 技术实现

### Vue 3 Composition API
- 使用 `ref` 和 `computed` 管理状态
- 使用 `onBeforeUnmount` 清理资源
- 事件发射器处理组件通信

### Element Plus 集成
- 使用 Element Plus 图标组件
- 集成 Loading 和 Message 组件
- 保持与现有UI风格一致

### 音频处理
- HTML5 Audio API 播放音频
- 音频状态管理和错误处理
- 自动清理音频资源

## 测试页面

创建了 `test-lip-sync-dialog.html` 测试页面，可以独立测试对话框的UI效果和交互功能。

## 使用方法

1. 在视频编辑器中选择一个包含图片和音频的分镜
2. 点击"对口型"按钮
3. 在弹出的对话框中查看图片和音频信息
4. 点击音频播放按钮可以预听音频
5. 确认无误后点击"确认 (扣除10积分)"按钮
6. 系统将开始处理对口型，并显示处理结果

## 注意事项

- 只有包含音频的分镜才能进行对口型处理
- 对口型处理会消耗10积分
- 处理过程不可撤销
- 处理完成后会删除原有的旁白音频
- 建议在处理前确认音频内容正确

## 后续扩展

- 可以添加对口型效果预览
- 支持批量对口型处理
- 添加对口型质量设置选项
- 支持自定义积分消耗配置
