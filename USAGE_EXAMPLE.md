# 音量控制组件使用示例

## 基本使用

```vue
<template>
  <div class="editor-container">
    <!-- 其他编辑器内容 -->
    
    <!-- 音量控制面板 -->
    <VolumeControlPanel
      :current-shot="currentShot"
      :background-music="backgroundMusic"
      @update:backgroundMusicVolume="handleBackgroundMusicVolumeChange"
      @update:videoVolume="handleVideoVolumeChange"
      @update:audioVolume="handleAudioVolumeChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import VolumeControlPanel from '@/components/videoEdit/VolumeControlPanel.vue';

// 背景音乐数据
const backgroundMusic = ref({
  id: 'bg-music-1',
  name: '背景音乐',
  audioUrl: 'https://example.com/bg-music.mp3',
  volume: 0.8, // 0.0-1.0 范围
  audioDuration: 120000
});

// 当前分镜数据
const currentShot = ref({
  id: 'shot-1',
  type: 'video',
  videoUrl: 'https://example.com/video.mp4',
  videoVolume: 0.6, // 0.0-1.0 范围
  audios: [
    {
      id: 'audio-1',
      text: '这是语音内容',
      audioType: 1, // 1-语音, 2-音效
      volume: 0.7, // 0.0-1.0 范围
      audioDuration: 5000
    },
    {
      id: 'audio-2',
      audioType: 2, // 音效
      volume: 0.9,
      audioDuration: 3000
    }
  ]
});

// 事件处理方法
const handleBackgroundMusicVolumeChange = (volume) => {
  console.log('背景音乐音量变化:', volume);
  // 更新背景音乐音量
  backgroundMusic.value.volume = volume;
  // 调用API保存到服务器
  // await updateBackgroundMusicVolume(volume);
};

const handleVideoVolumeChange = (volume) => {
  console.log('视频音量变化:', volume);
  // 更新当前分镜视频音量
  currentShot.value.videoVolume = volume;
  // 调用API保存到服务器
  // await updateShotVideoVolume(currentShot.value.id, volume);
};

const handleAudioVolumeChange = (volume) => {
  console.log('整体音频音量变化:', volume);
  // 为所有音频设置相同音量
  currentShot.value.audios.forEach(audio => {
    audio.volume = volume;
  });
  // 调用API保存到服务器
  // await updateAllAudioVolume(currentShot.value.id, volume);
};
</script>
```

## 数据格式说明

### 背景音乐对象格式
```javascript
{
  id: string,           // 音乐ID
  name: string,         // 音乐名称
  audioUrl: string,     // 音频URL
  volume: number,       // 音量 (0.0-1.0)
  audioDuration: number // 音频时长(毫秒)
}
```

### 分镜对象格式
```javascript
{
  id: string,           // 分镜ID
  type: string,         // 'image' 或 'video'
  videoUrl?: string,    // 视频URL (type为video时)
  videoVolume?: number, // 视频音量 (0.0-1.0)
  audios: [             // 音频数组
    {
      id: string,           // 音频ID
      text?: string,        // 音频文本(语音时)
      audioType: number,    // 1-语音, 2-音效
      volume: number,       // 音量 (0.0-1.0)
      audioDuration: number // 音频时长(毫秒)
    }
  ]
}
```

## 组件行为

1. **背景音乐音量控制**
   - 始终显示
   - 当 `backgroundMusic` 为 null 时，滑块显示默认值100%
   - 音量变化时触发 `update:backgroundMusicVolume` 事件

2. **视频音量控制**
   - 仅当 `currentShot.type === 'video'` 时显示
   - 当 `videoVolume` 未定义时，滑块显示默认值100%
   - 音量变化时触发 `update:videoVolume` 事件

3. **音频音量控制**
   - 仅当 `currentShot.audios` 存在且长度大于0时显示
   - 显示统一的音频音量控制滑块
   - 调整时会同时影响当前分镜的所有音频音量
   - 音量变化时触发 `update:audioVolume` 事件，传递音量值

4. **无分镜提示**
   - 当 `currentShot` 为 null 时显示提示信息

## 样式定制

组件支持明暗主题，可以通过CSS变量进行定制：

```css
/* 明亮主题 */
.volume-control-panel {
  --panel-bg: #ffffff;
  --panel-border: #e4e7ed;
  --header-bg: #f8f9fa;
  --text-primary: #303133;
  --text-secondary: #606266;
  --text-tertiary: #909399;
}

/* 暗黑主题 */
body.dark .volume-control-panel {
  --panel-bg: var(--bg-secondary-video);
  --panel-border: var(--border-color-dark);
  --header-bg: var(--bg-tertiary-video);
  --text-primary: var(--text-primary-dark);
  --text-secondary: var(--text-secondary-dark);
  --text-tertiary: var(--text-tertiary-dark);
}
```
