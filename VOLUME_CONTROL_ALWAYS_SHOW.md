# 音量控制组件 - 始终显示更新

## 更新内容

根据用户需求，修改音量控制组件的显示逻辑：
- **之前**：使用 `v-if` 条件渲染，不满足条件时不显示控件
- **现在**：始终显示所有音量控件，不满足条件时显示为禁用状态且音量为0

## 修改详情

### 1. 模板部分修改

#### 视频音量控制
```vue
<!-- 修改前 -->
<div class="volume-section" v-if="currentShot && currentShot.type === 'video'">

<!-- 修改后 -->
<div class="volume-section">
  <div class="volume-label" :class="{ 'disabled': !currentShot || currentShot.type !== 'video' }">
  <!-- ... -->
  <el-slider :disabled="!currentShot || currentShot.type !== 'video'" />
  <span class="volume-value" :class="{ 'disabled': !currentShot || currentShot.type !== 'video' }">
```

#### 音频音量控制
```vue
<!-- 修改前 -->
<div class="volume-section" v-if="currentShot && currentShot.audios && currentShot.audios.length > 0">

<!-- 修改后 -->
<div class="volume-section">
  <div class="volume-label" :class="{ 'disabled': !currentShot || !currentShot.audios || currentShot.audios.length === 0 }">
  <!-- ... -->
  <el-slider :disabled="!currentShot || !currentShot.audios || currentShot.audios.length === 0" />
  <span class="volume-value" :class="{ 'disabled': !currentShot || !currentShot.audios || currentShot.audios.length === 0 }">
```

### 2. 脚本逻辑修改

#### 音量显示逻辑
```javascript
// 修改前：条件不满足时显示默认值100%
if (newShot.type === 'video' && typeof newShot.videoVolume === 'number') {
  videoVolume.value = Math.round(newShot.videoVolume * 100);
} else {
  videoVolume.value = 100; // 默认100%
}

// 修改后：条件不满足时显示0%
if (newShot.type === 'video' && typeof newShot.videoVolume === 'number') {
  videoVolume.value = Math.round(newShot.videoVolume * 100);
} else {
  videoVolume.value = newShot.type === 'video' ? 100 : 0; // 不是视频时显示0%
}
```

#### 音频音量逻辑
```javascript
// 修改前：没有音频时显示100%
if (newShot.audios && newShot.audios.length > 0) {
  // 设置音量
} else {
  overallAudioVolume.value = 100; // 默认100%
}

// 修改后：没有音频时显示0%
if (newShot.audios && newShot.audios.length > 0) {
  // 设置音量
} else {
  overallAudioVolume.value = 0; // 没有音频时显示0%
}
```

### 3. 样式修改

#### 新增禁用状态样式
```css
.volume-label.disabled {
  opacity: 0.5;
  color: #c0c4cc;
}

body.dark .volume-label.disabled {
  color: #606266;
}

.volume-value.disabled {
  opacity: 0.5;
  color: #c0c4cc;
}

body.dark .volume-value.disabled {
  color: #606266;
}
```

#### 移除不再需要的样式
- 移除 `.no-shot-tip` 相关样式（不再显示提示信息）

### 4. 组件清理

#### 移除不再需要的元素
- 移除"无分镜提示"部分的模板代码
- 移除 `InfoFilled` 图标的导入

## 用户体验改进

### 1. 一致性
- 所有音量控件始终可见，提供一致的界面布局
- 用户不会因为分镜类型变化而看到界面元素的出现/消失

### 2. 状态清晰
- 通过禁用状态和0%显示，清楚地表明当前控件不可用
- 视觉上区分可用和不可用状态

### 3. 操作预期
- 用户可以看到所有可能的音量控制选项
- 即使当前不可用，也能了解系统的完整功能

## 不同状态下的表现

### 1. 视频分镜 + 有音频
- 背景音乐：正常显示和操作
- 视频音量：正常显示和操作
- 音频音量：正常显示和操作

### 2. 视频分镜 + 无音频
- 背景音乐：正常显示和操作
- 视频音量：正常显示和操作
- 音频音量：禁用状态，显示0%

### 3. 图片分镜 + 有音频
- 背景音乐：正常显示和操作
- 视频音量：禁用状态，显示0%
- 音频音量：正常显示和操作

### 4. 图片分镜 + 无音频
- 背景音乐：正常显示和操作
- 视频音量：禁用状态，显示0%
- 音频音量：禁用状态，显示0%

### 5. 无分镜选中
- 背景音乐：正常显示和操作
- 视频音量：禁用状态，显示0%
- 音频音量：禁用状态，显示0%

## 测试更新

更新了测试组件 `VolumeControlTest.vue`：
- 添加了状态切换按钮
- 可以测试不同分镜类型下的控件表现
- 验证禁用状态的视觉效果

## 向后兼容性

此更改完全向后兼容：
- 数据结构保持不变
- API 接口保持不变
- 只是界面显示逻辑的优化
