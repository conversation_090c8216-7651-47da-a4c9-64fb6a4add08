// 测试最近使用音色功能的逻辑

// 模拟 localStorage
const mockLocalStorage = {
  data: {},
  getItem(key) {
    return this.data[key] || null;
  },
  setItem(key, value) {
    this.data[key] = value;
  }
};

// 本地存储键名
const RECENT_VOICES_KEY = 'recent_voices';

// 从本地存储获取最近使用的音色ID列表
const getRecentVoiceIds = () => {
  try {
    const stored = mockLocalStorage.getItem(RECENT_VOICES_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('获取最近使用音色失败:', error);
    return [];
  }
};

// 保存最近使用的音色ID列表到本地存储
const saveRecentVoiceIds = (voiceIds) => {
  try {
    mockLocalStorage.setItem(RECENT_VOICES_KEY, JSON.stringify(voiceIds));
  } catch (error) {
    console.error('保存最近使用音色失败:', error);
  }
};

// 添加音色到最近使用列表
const addToRecentVoices = (voiceId) => {
  const recentIds = getRecentVoiceIds();
  
  // 移除已存在的相同ID（如果有）
  const filteredIds = recentIds.filter(id => id !== voiceId);
  
  // 将新ID添加到开头
  const newRecentIds = [voiceId, ...filteredIds];
  
  // 只保留最近的12个
  const limitedIds = newRecentIds.slice(0, 12);
  
  // 保存到本地存储
  saveRecentVoiceIds(limitedIds);
  
  return limitedIds;
};

// 测试函数
function testRecentVoices() {
  console.log('=== 测试最近使用音色功能 ===');
  
  // 测试1: 添加新音色
  console.log('\n测试1: 添加新音色');
  let result = addToRecentVoices(1);
  console.log('添加音色ID 1:', result);
  console.log('预期: [1]');
  
  // 测试2: 添加更多音色
  console.log('\n测试2: 添加更多音色');
  addToRecentVoices(2);
  addToRecentVoices(3);
  result = addToRecentVoices(4);
  console.log('添加音色ID 2,3,4:', result);
  console.log('预期: [4,3,2,1]');
  
  // 测试3: 重复添加已存在的音色
  console.log('\n测试3: 重复添加已存在的音色');
  result = addToRecentVoices(2);
  console.log('重新添加音色ID 2:', result);
  console.log('预期: [2,4,3,1] (2移到最前面)');
  
  // 测试4: 添加超过12个音色
  console.log('\n测试4: 添加超过12个音色');
  for (let i = 5; i <= 15; i++) {
    addToRecentVoices(i);
  }
  result = getRecentVoiceIds();
  console.log('添加音色ID 5-15后的结果:', result);
  console.log('预期长度: 12, 实际长度:', result.length);
  console.log('预期最新的是15，最旧的应该被移除');
  
  console.log('\n=== 测试完成 ===');
}

// 运行测试
testRecentVoices();
