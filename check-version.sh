#!/bin/bash

# 版本检查工具脚本
echo "=== 版本检查工具 ==="

# 定义颜色
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# 检查本地版本文件
echo -e "${YELLOW}检查本地版本文件...${NC}"

if [ -f "public/version.json" ]; then
    echo -e "${GREEN}✓ 开发环境版本文件存在${NC}"
    echo "内容:"
    cat public/version.json | jq . 2>/dev/null || cat public/version.json
else
    echo -e "${RED}✗ 开发环境版本文件不存在${NC}"
fi

echo ""

if [ -f "dist/version.json" ]; then
    echo -e "${GREEN}✓ 构建版本文件存在${NC}"
    echo "内容:"
    cat dist/version.json | jq . 2>/dev/null || cat dist/version.json
else
    echo -e "${YELLOW}! 构建版本文件不存在（需要先构建项目）${NC}"
fi

echo ""
echo -e "${YELLOW}检查环境变量...${NC}"

if [ -n "$npm_package_version" ]; then
    echo -e "${GREEN}✓ npm_package_version: $npm_package_version${NC}"
else
    echo -e "${YELLOW}! npm_package_version 未设置${NC}"
fi

if [ -n "$APP_VERSION" ]; then
    echo -e "${GREEN}✓ APP_VERSION: $APP_VERSION${NC}"
else
    echo -e "${YELLOW}! APP_VERSION 未设置${NC}"
fi

if [ -n "$VITE_APP_VERSION" ]; then
    echo -e "${GREEN}✓ VITE_APP_VERSION: $VITE_APP_VERSION${NC}"
else
    echo -e "${YELLOW}! VITE_APP_VERSION 未设置${NC}"
fi

if [ -n "$NODE_ENV" ]; then
    echo -e "${GREEN}✓ NODE_ENV: $NODE_ENV${NC}"
else
    echo -e "${YELLOW}! NODE_ENV 未设置${NC}"
fi

# 检查Git信息
echo ""
echo -e "${YELLOW}检查Git信息...${NC}"

if command -v git &> /dev/null; then
    if git rev-parse --git-dir > /dev/null 2>&1; then
        CURRENT_COMMIT=$(git rev-parse --short HEAD)
        CURRENT_BRANCH=$(git branch --show-current)
        echo -e "${GREEN}✓ Git仓库: $CURRENT_BRANCH ($CURRENT_COMMIT)${NC}"
    else
        echo -e "${YELLOW}! 不在Git仓库中${NC}"
    fi
else
    echo -e "${RED}✗ Git未安装${NC}"
fi

# 检查网络连接（如果有服务器运行）
echo ""
echo -e "${YELLOW}检查本地服务器...${NC}"

if curl -s http://localhost:3000/version.json > /dev/null 2>&1; then
    echo -e "${GREEN}✓ 本地服务器运行中${NC}"
    echo "服务器版本信息:"
    curl -s http://localhost:3000/version.json | jq . 2>/dev/null || curl -s http://localhost:3000/version.json
else
    echo -e "${YELLOW}! 本地服务器未运行或版本文件不可访问${NC}"
fi

echo ""
echo -e "${YELLOW}=== 建议操作 ===${NC}"
TEST_VERSION="1.0.$(date +%Y%m%d%H%M%S)"
echo "1. 设置版本号:"
echo "   export npm_package_version=\"${TEST_VERSION}\""
echo "   export APP_VERSION=\"${TEST_VERSION}\""
echo "   export VITE_APP_VERSION=\"${TEST_VERSION}\""
echo ""
echo "2. 或者直接运行构建命令:"
echo "   npm_package_version=\"${TEST_VERSION}\" APP_VERSION=\"${TEST_VERSION}\" VITE_APP_VERSION=\"${TEST_VERSION}\" npm run build"
echo ""
echo "3. 启动服务: npm run dev"
echo "4. 测试版本检测功能"
echo ""
echo "5. 运行测试脚本:"
echo "   ./test-version-env.sh"

echo ""
echo -e "${GREEN}检查完成！${NC}"
