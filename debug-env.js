// 调试环境变量脚本
console.log('=== 环境变量调试 ===');
console.log('当前工作目录:', process.cwd());
console.log('Node.js版本:', process.version);
console.log('');

console.log('=== 所有环境变量 ===');
const envKeys = Object.keys(process.env).filter(key => 
  key.includes('version') || 
  key.includes('VERSION') || 
  key.includes('NODE_ENV') ||
  key.includes('npm_package') ||
  key.includes('APP_') ||
  key.includes('VITE_')
);

envKeys.forEach(key => {
  console.log(`${key}: ${process.env[key]}`);
});

console.log('');
console.log('=== 版本获取逻辑测试 ===');

const getVersion = () => {
  console.log('检查 npm_package_version:', process.env.npm_package_version);
  if (process.env.npm_package_version && process.env.npm_package_version !== '0.0.0' && process.env.npm_package_version !== '1.0.0') {
    console.log('✓ 使用 npm_package_version');
    return process.env.npm_package_version;
  }
  
  console.log('检查 APP_VERSION:', process.env.APP_VERSION);
  if (process.env.APP_VERSION) {
    console.log('✓ 使用 APP_VERSION');
    return process.env.APP_VERSION;
  }
  
  console.log('检查 VITE_APP_VERSION:', process.env.VITE_APP_VERSION);
  if (process.env.VITE_APP_VERSION) {
    console.log('✓ 使用 VITE_APP_VERSION');
    return process.env.VITE_APP_VERSION;
  }
  
  const timestamp = new Date().toISOString().replace(/[-:T]/g, '').slice(0, 14);
  const fallbackVersion = `1.0.${timestamp}`;
  console.log('✓ 使用生成的版本号');
  return fallbackVersion;
};

const finalVersion = getVersion();
console.log('');
console.log('=== 最终结果 ===');
console.log('最终版本号:', finalVersion);

// 模拟完整的版本信息对象
const versionInfo = {
  version: finalVersion,
  buildTime: Date.now(),
  hash: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
  gitCommit: process.env.VERCEL_GIT_COMMIT_SHA || process.env.GITHUB_SHA || process.env.GIT_COMMIT || 'unknown',
  buildEnv: process.env.NODE_ENV || 'development',
  deployTime: new Date().toISOString()
};

console.log('');
console.log('=== 完整版本信息 ===');
console.log(JSON.stringify(versionInfo, null, 2));
