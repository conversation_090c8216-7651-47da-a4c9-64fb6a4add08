#!/bin/bash

# 测试版本构建脚本
echo "=== 测试版本构建功能 ==="

# 设置测试版本号
TEST_VERSION="1.0.$(date +%Y%m%d%H%M%S)"
echo "测试版本号: $TEST_VERSION"

# 设置环境变量
export npm_package_version="$TEST_VERSION"
export NODE_ENV="test"
export GIT_COMMIT="test-commit-$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"

echo "环境变量设置:"
echo "  npm_package_version: $npm_package_version"
echo "  NODE_ENV: $NODE_ENV"
echo "  GIT_COMMIT: $GIT_COMMIT"

# 模拟构建过程（这里只是创建一个测试版本文件）
echo "=== 生成测试版本文件 ==="

# 创建版本信息
cat > ./test-version.json << EOF
{
  "version": "$npm_package_version",
  "buildTime": $(date +%s)000,
  "hash": "$(openssl rand -hex 8)",
  "gitCommit": "$GIT_COMMIT",
  "buildEnv": "$NODE_ENV",
  "deployTime": "$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)"
}
EOF

echo "生成的版本文件内容:"
cat ./test-version.json

echo ""
echo "=== 测试完成 ==="
echo "可以使用以下命令进行实际构建:"
echo "  export npm_package_version=\"$TEST_VERSION\" && npm run build"
